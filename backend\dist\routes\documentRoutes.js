"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const auth_1 = require("../middleware/auth");
const usageTracking_1 = require("../middleware/usageTracking");
const documentController_1 = require("../controllers/documentController");
const router = express_1.default.Router();
// All routes require authentication
router.use(auth_1.authenticate);
// Upload routes (with storage usage tracking)
router.post('/upload', (0, usageTracking_1.checkUsageLimit)('storage'), documentController_1.upload.single('document'), documentController_1.uploadDocument, usageTracking_1.incrementUsage);
router.post('/upload/multiple', (0, usageTracking_1.checkUsageLimit)('storage'), documentController_1.upload.array('documents', 10), documentController_1.uploadMultipleDocuments, usageTracking_1.incrementUsage);
// Document management routes
router.get('/', documentController_1.getUserDocuments);
router.get('/stats', documentController_1.getDocumentStats);
router.get('/blockchain/status', documentController_1.getBlockchainStatus);
router.get('/:id', documentController_1.getDocument);
router.get('/:id/download', documentController_1.downloadDocument);
router.put('/:id', documentController_1.updateDocument);
router.delete('/:id', documentController_1.deleteDocument);
// Document processing routes (with feature access control)
router.post('/:id/parse', documentController_1.parseDocument);
router.post('/:id/blockchain/verify', (0, usageTracking_1.checkFeatureAccess)('apiAccess'), documentController_1.verifyDocumentOnBlockchain);
router.post('/:id/blockchain/store', (0, usageTracking_1.checkFeatureAccess)('apiAccess'), documentController_1.storeDocumentOnBlockchain);
exports.default = router;
//# sourceMappingURL=documentRoutes.js.map