"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/profile/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/profile/page.tsx":
/*!********************************************!*\
  !*** ./src/app/dashboard/profile/page.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ProfilePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../lib/auth */ \"(app-pages-browser)/./src/lib/auth.ts\");\n/* harmony import */ var _components_dashboard_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../components/dashboard/DashboardLayout */ \"(app-pages-browser)/./src/components/dashboard/DashboardLayout.tsx\");\n/* harmony import */ var _components_FileUpload__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../components/FileUpload */ \"(app-pages-browser)/./src/components/FileUpload.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction ProfilePage() {\n    var _formData_address, _formData_address1, _formData_address2, _formData_address3, _formData_address4;\n    _s();\n    const { user, updateUser } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [editing, setEditing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [saving, setSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        type: \"\",\n        text: \"\"\n    });\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [verificationLoading, setVerificationLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [verificationMessage, setVerificationMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [logoUploading, setLogoUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [logoPreview, setLogoPreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [signatureUploading, setSignatureUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [signaturePreview, setSignaturePreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (user) {\n            setFormData({\n                ...user,\n                address: user.address || {\n                    street: \"\",\n                    city: \"\",\n                    state: \"\",\n                    pincode: \"\",\n                    country: \"India\"\n                }\n            });\n            // Set logo preview if user has a logo\n            if (user.logo) {\n                // Handle both full paths and just filenames\n                const logoPath = user.logo.includes(\"/\") ? user.logo : \"uploads/logos/\".concat(user.logo);\n                setLogoPreview(\"http://localhost:5000/\".concat(logoPath));\n            }\n            // Set signature preview if user has a signature\n            if (user.signature) {\n                // Handle both full paths and just filenames\n                const signaturePath = user.signature.includes(\"/\") ? user.signature : \"uploads/signatures/\".concat(user.signature);\n                setSignaturePreview(\"http://localhost:5000/\".concat(signaturePath));\n            }\n        }\n    }, [\n        user\n    ]);\n    const handleInputChange = (e)=>{\n        const { name, value } = e.target;\n        // Handle nested address fields\n        if ([\n            \"street\",\n            \"city\",\n            \"state\",\n            \"pincode\",\n            \"country\"\n        ].includes(name)) {\n            setFormData((prev)=>({\n                    ...prev,\n                    address: {\n                        ...prev.address,\n                        [name]: value\n                    }\n                }));\n        } else {\n            setFormData((prev)=>({\n                    ...prev,\n                    [name]: value\n                }));\n        }\n    };\n    const handleLogoUpload = async (files)=>{\n        if (files.length === 0) return;\n        const file = files[0];\n        setLogoUploading(true);\n        setMessage({\n            type: \"\",\n            text: \"\"\n        });\n        try {\n            const formData = new FormData();\n            formData.append(\"logo\", file);\n            const token = localStorage.getItem(\"token\");\n            const response = await fetch(\"http://localhost:5000/api/uploads/logo\", {\n                method: \"POST\",\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(token)\n                },\n                body: formData\n            });\n            const data = await response.json();\n            if (data.success) {\n                // Update form data with new logo path\n                setFormData((prev)=>({\n                        ...prev,\n                        logo: data.data.logoPath\n                    }));\n                // Set preview\n                setLogoPreview(\"http://localhost:5000/\".concat(data.data.logoPath));\n                // Update user context with new logo\n                if (user) {\n                    updateUser({\n                        ...user,\n                        logo: data.data.logoPath\n                    });\n                }\n                setMessage({\n                    type: \"success\",\n                    text: \"Logo uploaded successfully!\"\n                });\n            } else {\n                setMessage({\n                    type: \"error\",\n                    text: data.message || \"Failed to upload logo\"\n                });\n            }\n        } catch (error) {\n            console.error(\"Logo upload error:\", error);\n            setMessage({\n                type: \"error\",\n                text: \"Failed to upload logo\"\n            });\n        } finally{\n            setLogoUploading(false);\n        }\n    };\n    const handleRemoveLogo = async ()=>{\n        setLogoUploading(true);\n        try {\n            const token = localStorage.getItem(\"token\");\n            const response = await fetch(\"http://localhost:5000/api/uploads/logo\", {\n                method: \"DELETE\",\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(token)\n                }\n            });\n            const data = await response.json();\n            if (data.success) {\n                setFormData((prev)=>({\n                        ...prev,\n                        logo: null\n                    }));\n                setLogoPreview(null);\n                // Update user context to remove logo\n                if (user) {\n                    updateUser({\n                        ...user,\n                        logo: undefined\n                    });\n                }\n                setMessage({\n                    type: \"success\",\n                    text: \"Logo removed successfully!\"\n                });\n            } else {\n                setMessage({\n                    type: \"error\",\n                    text: data.message || \"Failed to remove logo\"\n                });\n            }\n        } catch (error) {\n            console.error(\"Logo remove error:\", error);\n            setMessage({\n                type: \"error\",\n                text: \"Failed to remove logo\"\n            });\n        } finally{\n            setLogoUploading(false);\n        }\n    };\n    const handleSave = async ()=>{\n        setSaving(true);\n        setMessage({\n            type: \"\",\n            text: \"\"\n        });\n        try {\n            var _formData_name, _formData_businessName, _formData_gstNumber, _formData_phone, _formData_address_street, _formData_address, _formData_address_city, _formData_address1, _formData_address_state, _formData_address2, _formData_address_pincode, _formData_address3, _formData_address_country, _formData_address4;\n            // Clean up the form data before sending\n            const cleanFormData = {};\n            // Only include non-empty fields\n            if ((_formData_name = formData.name) === null || _formData_name === void 0 ? void 0 : _formData_name.trim()) {\n                cleanFormData.name = formData.name.trim();\n            }\n            if ((_formData_businessName = formData.businessName) === null || _formData_businessName === void 0 ? void 0 : _formData_businessName.trim()) {\n                cleanFormData.businessName = formData.businessName.trim();\n            }\n            if ((_formData_gstNumber = formData.gstNumber) === null || _formData_gstNumber === void 0 ? void 0 : _formData_gstNumber.trim()) {\n                cleanFormData.gstNumber = formData.gstNumber.trim();\n            }\n            if ((_formData_phone = formData.phone) === null || _formData_phone === void 0 ? void 0 : _formData_phone.trim()) {\n                cleanFormData.phone = formData.phone.trim();\n            }\n            // Handle address separately\n            const address = {};\n            if ((_formData_address = formData.address) === null || _formData_address === void 0 ? void 0 : (_formData_address_street = _formData_address.street) === null || _formData_address_street === void 0 ? void 0 : _formData_address_street.trim()) {\n                address.street = formData.address.street.trim();\n            }\n            if ((_formData_address1 = formData.address) === null || _formData_address1 === void 0 ? void 0 : (_formData_address_city = _formData_address1.city) === null || _formData_address_city === void 0 ? void 0 : _formData_address_city.trim()) {\n                address.city = formData.address.city.trim();\n            }\n            if ((_formData_address2 = formData.address) === null || _formData_address2 === void 0 ? void 0 : (_formData_address_state = _formData_address2.state) === null || _formData_address_state === void 0 ? void 0 : _formData_address_state.trim()) {\n                address.state = formData.address.state.trim();\n            }\n            if ((_formData_address3 = formData.address) === null || _formData_address3 === void 0 ? void 0 : (_formData_address_pincode = _formData_address3.pincode) === null || _formData_address_pincode === void 0 ? void 0 : _formData_address_pincode.trim()) {\n                address.pincode = formData.address.pincode.trim();\n            }\n            if ((_formData_address4 = formData.address) === null || _formData_address4 === void 0 ? void 0 : (_formData_address_country = _formData_address4.country) === null || _formData_address_country === void 0 ? void 0 : _formData_address_country.trim()) {\n                address.country = formData.address.country.trim();\n            }\n            // Only include address if it has at least one field\n            if (Object.keys(address).length > 0) {\n                cleanFormData.address = address;\n            }\n            // Include logo if it exists\n            if (formData.logo) {\n                cleanFormData.logo = formData.logo;\n            }\n            console.log(\"Sending profile data:\", cleanFormData);\n            const response = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.updateProfile)(cleanFormData);\n            if (response.success && response.data) {\n                updateUser(response.data.user);\n                setEditing(false);\n                setMessage({\n                    type: \"success\",\n                    text: \"Profile updated successfully!\"\n                });\n            } else {\n                console.error(\"Profile update failed:\", response);\n                const errorMessage = response.errors ? response.errors.join(\", \") : response.message || \"Failed to update profile\";\n                setMessage({\n                    type: \"error\",\n                    text: errorMessage\n                });\n            }\n        } catch (error) {\n            console.error(\"Profile update error:\", error);\n            setMessage({\n                type: \"error\",\n                text: \"Failed to update profile\"\n            });\n        } finally{\n            setSaving(false);\n        }\n    };\n    const handleCancel = ()=>{\n        if (user) {\n            setFormData({\n                ...user,\n                address: user.address || {\n                    street: \"\",\n                    city: \"\",\n                    state: \"\",\n                    pincode: \"\",\n                    country: \"India\"\n                }\n            });\n        }\n        setEditing(false);\n        setMessage({\n            type: \"\",\n            text: \"\"\n        });\n    };\n    const sendVerificationEmail = async ()=>{\n        setVerificationLoading(true);\n        setVerificationMessage(\"\");\n        try {\n            const response = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.authenticatedFetch)(\"/api/auth/send-verification\", {\n                method: \"POST\"\n            });\n            const data = await response.json();\n            if (data.success) {\n                if (data.developmentToken) {\n                    // Development mode - show direct verification link\n                    const verificationUrl = \"\".concat(window.location.origin, \"/verify-email?token=\").concat(data.developmentToken);\n                    setVerificationMessage(\"development_link:\".concat(verificationUrl));\n                } else {\n                    setVerificationMessage(\"Verification email sent! Please check your inbox.\");\n                }\n            } else {\n                setVerificationMessage(data.message || \"Failed to send verification email.\");\n            }\n        } catch (error) {\n            console.error(\"Send verification error:\", error);\n            setVerificationMessage(\"Network error. Please try again.\");\n        } finally{\n            setVerificationLoading(false);\n        }\n    };\n    if (!user) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        title: \"Profile\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__.DashboardHeader, {\n                title: \"Profile\",\n                subtitle: \"Manage your account information and business details\",\n                actions: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [\n                        editing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleCancel,\n                            className: \"px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-800 bg-white hover:bg-gray-50 transition-colors\",\n                            disabled: saving,\n                            children: \"Cancel\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                            lineNumber: 267,\n                            columnNumber: 15\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>editing ? handleSave() : setEditing(true),\n                            className: \"px-4 py-2 bg-indigo-600 text-white rounded-lg text-sm font-medium hover:bg-indigo-700 transition-colors disabled:opacity-50\",\n                            disabled: saving,\n                            children: saving ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full mr-2 inline-block\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 19\n                                    }, void 0),\n                                    \"Saving...\"\n                                ]\n                            }, void 0, true) : editing ? \"Save Changes\" : \"Edit Profile\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                            lineNumber: 275,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                    lineNumber: 265,\n                    columnNumber: 11\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                lineNumber: 261,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-4xl mx-auto p-4 lg:p-8\",\n                children: [\n                    message.text && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6 p-4 rounded-lg \".concat(message.type === \"success\" ? \"bg-green-50 border border-green-200 text-green-800\" : \"bg-red-50 border border-red-200 text-red-800\"),\n                        children: message.text\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                        lineNumber: 294,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row items-center space-y-4 sm:space-y-0 sm:space-x-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-24 h-24 rounded-full overflow-hidden border-2 border-gray-200 bg-gradient-to-br from-indigo-500 to-purple-600 flex items-center justify-center\",\n                                    children: logoPreview ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: logoPreview,\n                                        alt: \"Business Logo\",\n                                        className: \"w-full h-full object-cover\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white text-3xl font-bold\",\n                                        children: user.name.charAt(0).toUpperCase()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                    lineNumber: 306,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center sm:text-left\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-2xl lg:text-3xl font-bold text-gray-900\",\n                                            children: user.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                            lineNumber: 320,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-800 mt-1\",\n                                            children: user.email\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                            lineNumber: 321,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col sm:flex-row items-center sm:items-start space-y-2 sm:space-y-0 sm:space-x-3 mt-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat(user.isEmailVerified ? \"bg-green-100 text-green-800\" : \"bg-yellow-100 text-yellow-800\"),\n                                                    children: user.isEmailVerified ? \"✓ Email Verified\" : \"⚠ Email Not Verified\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                    lineNumber: 323,\n                                                    columnNumber: 17\n                                                }, this),\n                                                !user.isEmailVerified && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: sendVerificationEmail,\n                                                    disabled: verificationLoading,\n                                                    className: \"inline-flex items-center px-3 py-1 text-xs font-medium text-indigo-600 bg-indigo-50 border border-indigo-200 rounded-full hover:bg-indigo-100 transition-colors disabled:opacity-50\",\n                                                    children: verificationLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"animate-spin h-3 w-3 border border-indigo-600 border-t-transparent rounded-full mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 334,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Sending...\"\n                                                        ]\n                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-3 h-3 mr-1\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                    lineNumber: 340,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 339,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Verify Email\"\n                                                        ]\n                                                    }, void 0, true)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                    lineNumber: 327,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                            lineNumber: 322,\n                                            columnNumber: 15\n                                        }, this),\n                                        verificationMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-2 p-2 rounded-md text-xs \".concat(verificationMessage.includes(\"sent\") || verificationMessage.includes(\"development_link\") ? \"bg-green-50 text-green-700 border border-green-200\" : \"bg-red-50 text-red-700 border border-red-200\"),\n                                            children: verificationMessage.startsWith(\"development_link:\") ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"mb-2\",\n                                                        children: \"\\uD83D\\uDCE7 Email service not configured (Development Mode)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 356,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: verificationMessage.replace(\"development_link:\", \"\"),\n                                                        className: \"inline-flex items-center px-3 py-1 bg-green-600 text-white text-xs rounded-md hover:bg-green-700 transition-colors\",\n                                                        target: \"_blank\",\n                                                        rel: \"noopener noreferrer\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-3 h-3 mr-1\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M5 13l4 4L19 7\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                    lineNumber: 364,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 363,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Click to Verify Email\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 357,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 355,\n                                                columnNumber: 21\n                                            }, this) : verificationMessage\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                            lineNumber: 349,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                    lineNumber: 319,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                            lineNumber: 305,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                        lineNumber: 304,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-xl shadow-sm border border-gray-200 p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold text-gray-900 mb-6\",\n                                children: \"Profile Information\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                lineNumber: 380,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-gray-900 border-b border-gray-200 pb-2\",\n                                                children: \"Personal Information\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 385,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-800 mb-2\",\n                                                        children: \"Full Name\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 390,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        name: \"name\",\n                                                        value: formData.name || \"\",\n                                                        onChange: handleInputChange,\n                                                        disabled: !editing,\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-gray-900 \".concat(!editing ? \"bg-gray-50\" : \"\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 391,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 389,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-800 mb-2\",\n                                                        children: \"Email Address\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 402,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"email\",\n                                                        name: \"email\",\n                                                        value: formData.email || \"\",\n                                                        onChange: handleInputChange,\n                                                        disabled: !editing,\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-gray-900 \".concat(!editing ? \"bg-gray-50\" : \"\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 403,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 401,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-800 mb-2\",\n                                                        children: \"Phone Number\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 414,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"tel\",\n                                                        name: \"phone\",\n                                                        value: formData.phone || \"\",\n                                                        onChange: handleInputChange,\n                                                        disabled: !editing,\n                                                        placeholder: \"Enter your phone number\",\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-gray-900 \".concat(!editing ? \"bg-gray-50\" : \"\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 415,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 413,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                        lineNumber: 384,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-gray-900 border-b border-gray-200 pb-2\",\n                                                children: \"Business Information\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 429,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-800 mb-2\",\n                                                        children: \"Business Name\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 434,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        name: \"businessName\",\n                                                        value: formData.businessName || \"\",\n                                                        onChange: handleInputChange,\n                                                        disabled: !editing,\n                                                        placeholder: \"Enter your business name\",\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-gray-900 \".concat(!editing ? \"bg-gray-50\" : \"\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 435,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 433,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-800 mb-2\",\n                                                        children: \"GST Number\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 447,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        name: \"gstNumber\",\n                                                        value: formData.gstNumber || \"\",\n                                                        onChange: handleInputChange,\n                                                        disabled: !editing,\n                                                        placeholder: \"Enter GST number (optional)\",\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-gray-900 \".concat(!editing ? \"bg-gray-50\" : \"\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 448,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 446,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-800 mb-2\",\n                                                        children: \"Business Logo\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 461,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    logoPreview ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-32 h-32 border-2 border-gray-200 rounded-lg overflow-hidden bg-gray-50 flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                    src: logoPreview,\n                                                                    alt: \"Business Logo\",\n                                                                    className: \"w-full h-full object-contain\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                    lineNumber: 465,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 464,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            editing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FileUpload__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                        onUpload: handleLogoUpload,\n                                                                        accept: \".jpg,.jpeg,.png,.gif,.svg,.webp\",\n                                                                        maxSize: 5,\n                                                                        maxFiles: 1,\n                                                                        className: \"flex-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                        lineNumber: 473,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: handleRemoveLogo,\n                                                                        disabled: logoUploading,\n                                                                        className: \"px-3 py-2 text-sm font-medium text-red-600 bg-red-50 border border-red-200 rounded-md hover:bg-red-100 transition-colors disabled:opacity-50\",\n                                                                        children: logoUploading ? \"Removing...\" : \"Remove\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                        lineNumber: 480,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 472,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 463,\n                                                        columnNumber: 19\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-32 h-32 border-2 border-dashed border-gray-300 rounded-lg bg-gray-50 flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-8 h-8 text-gray-400 mx-auto mb-2\",\n                                                                            fill: \"none\",\n                                                                            stroke: \"currentColor\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\",\n                                                                                strokeWidth: 2,\n                                                                                d: \"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                                lineNumber: 495,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                            lineNumber: 494,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-gray-500\",\n                                                                            children: \"No logo\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                            lineNumber: 497,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                    lineNumber: 493,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 492,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            editing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FileUpload__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                onUpload: handleLogoUpload,\n                                                                accept: \".jpg,.jpeg,.png,.gif,.svg,.webp\",\n                                                                maxSize: 5,\n                                                                maxFiles: 1,\n                                                                className: \"w-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 501,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 491,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    logoUploading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-2 flex items-center text-sm text-gray-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"animate-spin h-4 w-4 border-2 border-indigo-600 border-t-transparent rounded-full mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 513,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Uploading logo...\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 512,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 460,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                        lineNumber: 428,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                lineNumber: 382,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 border-b border-gray-200 pb-2 mb-4\",\n                                        children: \"Address Information\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                        lineNumber: 523,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"md:col-span-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-800 mb-2\",\n                                                        children: \"Street Address\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 529,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        name: \"street\",\n                                                        value: ((_formData_address = formData.address) === null || _formData_address === void 0 ? void 0 : _formData_address.street) || \"\",\n                                                        onChange: handleInputChange,\n                                                        disabled: !editing,\n                                                        placeholder: \"Enter your street address\",\n                                                        rows: 3,\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-gray-900 \".concat(!editing ? \"bg-gray-50\" : \"\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 530,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 528,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-800 mb-2\",\n                                                        children: \"City\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 542,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        name: \"city\",\n                                                        value: ((_formData_address1 = formData.address) === null || _formData_address1 === void 0 ? void 0 : _formData_address1.city) || \"\",\n                                                        onChange: handleInputChange,\n                                                        disabled: !editing,\n                                                        placeholder: \"Enter city\",\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-gray-900 \".concat(!editing ? \"bg-gray-50\" : \"\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 543,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 541,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-800 mb-2\",\n                                                        children: \"State\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 555,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        name: \"state\",\n                                                        value: ((_formData_address2 = formData.address) === null || _formData_address2 === void 0 ? void 0 : _formData_address2.state) || \"\",\n                                                        onChange: handleInputChange,\n                                                        disabled: !editing,\n                                                        placeholder: \"Enter state\",\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-gray-900 \".concat(!editing ? \"bg-gray-50\" : \"\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 556,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 554,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-800 mb-2\",\n                                                        children: \"PIN Code\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 568,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        name: \"pincode\",\n                                                        value: ((_formData_address3 = formData.address) === null || _formData_address3 === void 0 ? void 0 : _formData_address3.pincode) || \"\",\n                                                        onChange: handleInputChange,\n                                                        disabled: !editing,\n                                                        placeholder: \"Enter PIN code\",\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-gray-900 \".concat(!editing ? \"bg-gray-50\" : \"\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 569,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 567,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-800 mb-2\",\n                                                        children: \"Country\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 581,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        name: \"country\",\n                                                        value: ((_formData_address4 = formData.address) === null || _formData_address4 === void 0 ? void 0 : _formData_address4.country) || \"India\",\n                                                        onChange: handleInputChange,\n                                                        disabled: !editing,\n                                                        placeholder: \"Enter country\",\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-gray-900 \".concat(!editing ? \"bg-gray-50\" : \"\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 582,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 580,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                        lineNumber: 527,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                lineNumber: 522,\n                                columnNumber: 11\n                            }, this),\n                            editing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:hidden mt-6 flex space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleCancel,\n                                        className: \"flex-1 px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-800 bg-white hover:bg-gray-50 transition-colors\",\n                                        disabled: saving,\n                                        children: \"Cancel\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                        lineNumber: 598,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleSave,\n                                        className: \"flex-1 px-4 py-2 bg-indigo-600 text-white rounded-lg text-sm font-medium hover:bg-indigo-700 transition-colors disabled:opacity-50\",\n                                        disabled: saving,\n                                        children: saving ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full mr-2 inline-block\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                    lineNumber: 612,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Saving...\"\n                                            ]\n                                        }, void 0, true) : \"Save Changes\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                        lineNumber: 605,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                lineNumber: 597,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                        lineNumber: 379,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-xl shadow-sm border border-gray-200 p-6 mt-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900 mb-4\",\n                                children: \"Account Information\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                lineNumber: 623,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 sm:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center py-2 border-b border-gray-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-gray-800\",\n                                                children: \"Account Type\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 626,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-900 capitalize\",\n                                                children: user.role\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 627,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                        lineNumber: 625,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center py-2 border-b border-gray-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-gray-800\",\n                                                children: \"Member Since\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 630,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-900\",\n                                                children: user.createdAt ? new Date(user.createdAt).toLocaleDateString(\"en-IN\") : \"N/A\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 631,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                        lineNumber: 629,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center py-2 border-b border-gray-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-gray-800\",\n                                                children: \"Last Login\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 636,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-900\",\n                                                children: user.lastLogin ? new Date(user.lastLogin).toLocaleDateString(\"en-IN\") : \"N/A\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 637,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                        lineNumber: 635,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center py-2 border-b border-gray-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-gray-800\",\n                                                children: \"Email Status\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 642,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat(user.isEmailVerified ? \"bg-green-100 text-green-800\" : \"bg-yellow-100 text-yellow-800\"),\n                                                        children: user.isEmailVerified ? \"Verified\" : \"Not Verified\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 644,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    !user.isEmailVerified && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: sendVerificationEmail,\n                                                        disabled: verificationLoading,\n                                                        className: \"text-xs text-indigo-600 hover:text-indigo-800 font-medium disabled:opacity-50\",\n                                                        children: verificationLoading ? \"Sending...\" : \"Send Verification\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 648,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 643,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                        lineNumber: 641,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                lineNumber: 624,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                        lineNumber: 622,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                lineNumber: 291,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n        lineNumber: 260,\n        columnNumber: 5\n    }, this);\n}\n_s(ProfilePage, \"mLWr/FK64trBTG7PQjmr6/dSb6o=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = ProfilePage;\nvar _c;\n$RefreshReg$(_c, \"ProfilePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZGFzaGJvYXJkL3Byb2ZpbGUvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUU0QztBQUNZO0FBQ2M7QUFDMkI7QUFDekM7QUFFekMsU0FBU1E7UUEyZ0JDQyxtQkFjQUEsb0JBYUFBLG9CQWFBQSxvQkFhQUE7O0lBL2pCdkIsTUFBTSxFQUFFQyxJQUFJLEVBQUVDLFVBQVUsRUFBRSxHQUFHVCw4REFBT0E7SUFDcEMsTUFBTSxDQUFDVSxTQUFTQyxXQUFXLEdBQUdiLCtDQUFRQSxDQUFDO0lBQ3ZDLE1BQU0sQ0FBQ2MsUUFBUUMsVUFBVSxHQUFHZiwrQ0FBUUEsQ0FBQztJQUNyQyxNQUFNLENBQUNnQixTQUFTQyxXQUFXLEdBQUdqQiwrQ0FBUUEsQ0FBQztRQUFFa0IsTUFBTTtRQUFJQyxNQUFNO0lBQUc7SUFDNUQsTUFBTSxDQUFDVixVQUFVVyxZQUFZLEdBQUdwQiwrQ0FBUUEsQ0FBTSxDQUFDO0lBQy9DLE1BQU0sQ0FBQ3FCLHFCQUFxQkMsdUJBQXVCLEdBQUd0QiwrQ0FBUUEsQ0FBQztJQUMvRCxNQUFNLENBQUN1QixxQkFBcUJDLHVCQUF1QixHQUFHeEIsK0NBQVFBLENBQUM7SUFDL0QsTUFBTSxDQUFDeUIsZUFBZUMsaUJBQWlCLEdBQUcxQiwrQ0FBUUEsQ0FBQztJQUNuRCxNQUFNLENBQUMyQixhQUFhQyxlQUFlLEdBQUc1QiwrQ0FBUUEsQ0FBZ0I7SUFDOUQsTUFBTSxDQUFDNkIsb0JBQW9CQyxzQkFBc0IsR0FBRzlCLCtDQUFRQSxDQUFDO0lBQzdELE1BQU0sQ0FBQytCLGtCQUFrQkMsb0JBQW9CLEdBQUdoQywrQ0FBUUEsQ0FBZ0I7SUFFeEVDLGdEQUFTQSxDQUFDO1FBQ1IsSUFBSVMsTUFBTTtZQUNSVSxZQUFZO2dCQUNWLEdBQUdWLElBQUk7Z0JBQ1B1QixTQUFTdkIsS0FBS3VCLE9BQU8sSUFBSTtvQkFDdkJDLFFBQVE7b0JBQ1JDLE1BQU07b0JBQ05DLE9BQU87b0JBQ1BDLFNBQVM7b0JBQ1RDLFNBQVM7Z0JBQ1g7WUFDRjtZQUNBLHNDQUFzQztZQUN0QyxJQUFJNUIsS0FBSzZCLElBQUksRUFBRTtnQkFDYiw0Q0FBNEM7Z0JBQzVDLE1BQU1DLFdBQVc5QixLQUFLNkIsSUFBSSxDQUFDRSxRQUFRLENBQUMsT0FBTy9CLEtBQUs2QixJQUFJLEdBQUcsaUJBQTJCLE9BQVY3QixLQUFLNkIsSUFBSTtnQkFDakZYLGVBQWUseUJBQWtDLE9BQVRZO1lBQzFDO1lBQ0EsZ0RBQWdEO1lBQ2hELElBQUk5QixLQUFLZ0MsU0FBUyxFQUFFO2dCQUNsQiw0Q0FBNEM7Z0JBQzVDLE1BQU1DLGdCQUFnQmpDLEtBQUtnQyxTQUFTLENBQUNELFFBQVEsQ0FBQyxPQUFPL0IsS0FBS2dDLFNBQVMsR0FBRyxzQkFBcUMsT0FBZmhDLEtBQUtnQyxTQUFTO2dCQUMxR1Ysb0JBQW9CLHlCQUF1QyxPQUFkVztZQUMvQztRQUNGO0lBQ0YsR0FBRztRQUFDakM7S0FBSztJQUVULE1BQU1rQyxvQkFBb0IsQ0FBQ0M7UUFDekIsTUFBTSxFQUFFQyxJQUFJLEVBQUVDLEtBQUssRUFBRSxHQUFHRixFQUFFRyxNQUFNO1FBRWhDLCtCQUErQjtRQUMvQixJQUFJO1lBQUM7WUFBVTtZQUFRO1lBQVM7WUFBVztTQUFVLENBQUNQLFFBQVEsQ0FBQ0ssT0FBTztZQUNwRTFCLFlBQVksQ0FBQzZCLE9BQWU7b0JBQzFCLEdBQUdBLElBQUk7b0JBQ1BoQixTQUFTO3dCQUNQLEdBQUdnQixLQUFLaEIsT0FBTzt3QkFDZixDQUFDYSxLQUFLLEVBQUVDO29CQUNWO2dCQUNGO1FBQ0YsT0FBTztZQUNMM0IsWUFBWSxDQUFDNkIsT0FBZTtvQkFBRSxHQUFHQSxJQUFJO29CQUFFLENBQUNILEtBQUssRUFBRUM7Z0JBQU07UUFDdkQ7SUFDRjtJQUVBLE1BQU1HLG1CQUFtQixPQUFPQztRQUM5QixJQUFJQSxNQUFNQyxNQUFNLEtBQUssR0FBRztRQUV4QixNQUFNQyxPQUFPRixLQUFLLENBQUMsRUFBRTtRQUNyQnpCLGlCQUFpQjtRQUNqQlQsV0FBVztZQUFFQyxNQUFNO1lBQUlDLE1BQU07UUFBRztRQUVoQyxJQUFJO1lBQ0YsTUFBTVYsV0FBVyxJQUFJNkM7WUFDckI3QyxTQUFTOEMsTUFBTSxDQUFDLFFBQVFGO1lBRXhCLE1BQU1HLFFBQVFDLGFBQWFDLE9BQU8sQ0FBQztZQUNuQyxNQUFNQyxXQUFXLE1BQU1DLE1BQU0sMENBQTBDO2dCQUNyRUMsUUFBUTtnQkFDUkMsU0FBUztvQkFDUCxpQkFBaUIsVUFBZ0IsT0FBTk47Z0JBQzdCO2dCQUNBTyxNQUFNdEQ7WUFDUjtZQUVBLE1BQU11RCxPQUFPLE1BQU1MLFNBQVNNLElBQUk7WUFFaEMsSUFBSUQsS0FBS0UsT0FBTyxFQUFFO2dCQUNoQixzQ0FBc0M7Z0JBQ3RDOUMsWUFBWSxDQUFDNkIsT0FBZTt3QkFBRSxHQUFHQSxJQUFJO3dCQUFFVixNQUFNeUIsS0FBS0EsSUFBSSxDQUFDeEIsUUFBUTtvQkFBQztnQkFDaEUsY0FBYztnQkFDZFosZUFBZSx5QkFBNEMsT0FBbkJvQyxLQUFLQSxJQUFJLENBQUN4QixRQUFRO2dCQUMxRCxvQ0FBb0M7Z0JBQ3BDLElBQUk5QixNQUFNO29CQUNSQyxXQUFXO3dCQUFFLEdBQUdELElBQUk7d0JBQUU2QixNQUFNeUIsS0FBS0EsSUFBSSxDQUFDeEIsUUFBUTtvQkFBQztnQkFDakQ7Z0JBQ0F2QixXQUFXO29CQUFFQyxNQUFNO29CQUFXQyxNQUFNO2dCQUE4QjtZQUNwRSxPQUFPO2dCQUNMRixXQUFXO29CQUFFQyxNQUFNO29CQUFTQyxNQUFNNkMsS0FBS2hELE9BQU8sSUFBSTtnQkFBd0I7WUFDNUU7UUFDRixFQUFFLE9BQU9tRCxPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyxzQkFBc0JBO1lBQ3BDbEQsV0FBVztnQkFBRUMsTUFBTTtnQkFBU0MsTUFBTTtZQUF3QjtRQUM1RCxTQUFVO1lBQ1JPLGlCQUFpQjtRQUNuQjtJQUNGO0lBRUEsTUFBTTJDLG1CQUFtQjtRQUN2QjNDLGlCQUFpQjtRQUNqQixJQUFJO1lBQ0YsTUFBTThCLFFBQVFDLGFBQWFDLE9BQU8sQ0FBQztZQUNuQyxNQUFNQyxXQUFXLE1BQU1DLE1BQU0sMENBQTBDO2dCQUNyRUMsUUFBUTtnQkFDUkMsU0FBUztvQkFDUCxpQkFBaUIsVUFBZ0IsT0FBTk47Z0JBQzdCO1lBQ0Y7WUFFQSxNQUFNUSxPQUFPLE1BQU1MLFNBQVNNLElBQUk7WUFDaEMsSUFBSUQsS0FBS0UsT0FBTyxFQUFFO2dCQUNoQjlDLFlBQVksQ0FBQzZCLE9BQWU7d0JBQUUsR0FBR0EsSUFBSTt3QkFBRVYsTUFBTTtvQkFBSztnQkFDbERYLGVBQWU7Z0JBQ2YscUNBQXFDO2dCQUNyQyxJQUFJbEIsTUFBTTtvQkFDUkMsV0FBVzt3QkFBRSxHQUFHRCxJQUFJO3dCQUFFNkIsTUFBTStCO29CQUFVO2dCQUN4QztnQkFDQXJELFdBQVc7b0JBQUVDLE1BQU07b0JBQVdDLE1BQU07Z0JBQTZCO1lBQ25FLE9BQU87Z0JBQ0xGLFdBQVc7b0JBQUVDLE1BQU07b0JBQVNDLE1BQU02QyxLQUFLaEQsT0FBTyxJQUFJO2dCQUF3QjtZQUM1RTtRQUNGLEVBQUUsT0FBT21ELE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLHNCQUFzQkE7WUFDcENsRCxXQUFXO2dCQUFFQyxNQUFNO2dCQUFTQyxNQUFNO1lBQXdCO1FBQzVELFNBQVU7WUFDUk8saUJBQWlCO1FBQ25CO0lBQ0Y7SUFFQSxNQUFNNkMsYUFBYTtRQUNqQnhELFVBQVU7UUFDVkUsV0FBVztZQUFFQyxNQUFNO1lBQUlDLE1BQU07UUFBRztRQUVoQyxJQUFJO2dCQUtFVixnQkFHQUEsd0JBR0FBLHFCQUdBQSxpQkFNQUEsMEJBQUFBLG1CQUdBQSx3QkFBQUEsb0JBR0FBLHlCQUFBQSxvQkFHQUEsMkJBQUFBLG9CQUdBQSwyQkFBQUE7WUEvQkosd0NBQXdDO1lBQ3hDLE1BQU0rRCxnQkFBcUIsQ0FBQztZQUU1QixnQ0FBZ0M7WUFDaEMsS0FBSS9ELGlCQUFBQSxTQUFTcUMsSUFBSSxjQUFickMscUNBQUFBLGVBQWVnRSxJQUFJLElBQUk7Z0JBQ3pCRCxjQUFjMUIsSUFBSSxHQUFHckMsU0FBU3FDLElBQUksQ0FBQzJCLElBQUk7WUFDekM7WUFDQSxLQUFJaEUseUJBQUFBLFNBQVNpRSxZQUFZLGNBQXJCakUsNkNBQUFBLHVCQUF1QmdFLElBQUksSUFBSTtnQkFDakNELGNBQWNFLFlBQVksR0FBR2pFLFNBQVNpRSxZQUFZLENBQUNELElBQUk7WUFDekQ7WUFDQSxLQUFJaEUsc0JBQUFBLFNBQVNrRSxTQUFTLGNBQWxCbEUsMENBQUFBLG9CQUFvQmdFLElBQUksSUFBSTtnQkFDOUJELGNBQWNHLFNBQVMsR0FBR2xFLFNBQVNrRSxTQUFTLENBQUNGLElBQUk7WUFDbkQ7WUFDQSxLQUFJaEUsa0JBQUFBLFNBQVNtRSxLQUFLLGNBQWRuRSxzQ0FBQUEsZ0JBQWdCZ0UsSUFBSSxJQUFJO2dCQUMxQkQsY0FBY0ksS0FBSyxHQUFHbkUsU0FBU21FLEtBQUssQ0FBQ0gsSUFBSTtZQUMzQztZQUVBLDRCQUE0QjtZQUM1QixNQUFNeEMsVUFBZSxDQUFDO1lBQ3RCLEtBQUl4QixvQkFBQUEsU0FBU3dCLE9BQU8sY0FBaEJ4Qix5Q0FBQUEsMkJBQUFBLGtCQUFrQnlCLE1BQU0sY0FBeEJ6QiwrQ0FBQUEseUJBQTBCZ0UsSUFBSSxJQUFJO2dCQUNwQ3hDLFFBQVFDLE1BQU0sR0FBR3pCLFNBQVN3QixPQUFPLENBQUNDLE1BQU0sQ0FBQ3VDLElBQUk7WUFDL0M7WUFDQSxLQUFJaEUscUJBQUFBLFNBQVN3QixPQUFPLGNBQWhCeEIsMENBQUFBLHlCQUFBQSxtQkFBa0IwQixJQUFJLGNBQXRCMUIsNkNBQUFBLHVCQUF3QmdFLElBQUksSUFBSTtnQkFDbEN4QyxRQUFRRSxJQUFJLEdBQUcxQixTQUFTd0IsT0FBTyxDQUFDRSxJQUFJLENBQUNzQyxJQUFJO1lBQzNDO1lBQ0EsS0FBSWhFLHFCQUFBQSxTQUFTd0IsT0FBTyxjQUFoQnhCLDBDQUFBQSwwQkFBQUEsbUJBQWtCMkIsS0FBSyxjQUF2QjNCLDhDQUFBQSx3QkFBeUJnRSxJQUFJLElBQUk7Z0JBQ25DeEMsUUFBUUcsS0FBSyxHQUFHM0IsU0FBU3dCLE9BQU8sQ0FBQ0csS0FBSyxDQUFDcUMsSUFBSTtZQUM3QztZQUNBLEtBQUloRSxxQkFBQUEsU0FBU3dCLE9BQU8sY0FBaEJ4QiwwQ0FBQUEsNEJBQUFBLG1CQUFrQjRCLE9BQU8sY0FBekI1QixnREFBQUEsMEJBQTJCZ0UsSUFBSSxJQUFJO2dCQUNyQ3hDLFFBQVFJLE9BQU8sR0FBRzVCLFNBQVN3QixPQUFPLENBQUNJLE9BQU8sQ0FBQ29DLElBQUk7WUFDakQ7WUFDQSxLQUFJaEUscUJBQUFBLFNBQVN3QixPQUFPLGNBQWhCeEIsMENBQUFBLDRCQUFBQSxtQkFBa0I2QixPQUFPLGNBQXpCN0IsZ0RBQUFBLDBCQUEyQmdFLElBQUksSUFBSTtnQkFDckN4QyxRQUFRSyxPQUFPLEdBQUc3QixTQUFTd0IsT0FBTyxDQUFDSyxPQUFPLENBQUNtQyxJQUFJO1lBQ2pEO1lBRUEsb0RBQW9EO1lBQ3BELElBQUlJLE9BQU9DLElBQUksQ0FBQzdDLFNBQVNtQixNQUFNLEdBQUcsR0FBRztnQkFDbkNvQixjQUFjdkMsT0FBTyxHQUFHQTtZQUMxQjtZQUVBLDRCQUE0QjtZQUM1QixJQUFJeEIsU0FBUzhCLElBQUksRUFBRTtnQkFDakJpQyxjQUFjakMsSUFBSSxHQUFHOUIsU0FBUzhCLElBQUk7WUFDcEM7WUFFQTZCLFFBQVFXLEdBQUcsQ0FBQyx5QkFBeUJQO1lBQ3JDLE1BQU1iLFdBQVcsTUFBTXhELHdEQUFhQSxDQUFDcUU7WUFFckMsSUFBSWIsU0FBU08sT0FBTyxJQUFJUCxTQUFTSyxJQUFJLEVBQUU7Z0JBQ3JDckQsV0FBV2dELFNBQVNLLElBQUksQ0FBQ3RELElBQUk7Z0JBQzdCRyxXQUFXO2dCQUNYSSxXQUFXO29CQUFFQyxNQUFNO29CQUFXQyxNQUFNO2dCQUFnQztZQUN0RSxPQUFPO2dCQUNMaUQsUUFBUUQsS0FBSyxDQUFDLDBCQUEwQlI7Z0JBQ3hDLE1BQU1xQixlQUFlckIsU0FBU3NCLE1BQU0sR0FBR3RCLFNBQVNzQixNQUFNLENBQUNDLElBQUksQ0FBQyxRQUFRdkIsU0FBUzNDLE9BQU8sSUFBSTtnQkFDeEZDLFdBQVc7b0JBQUVDLE1BQU07b0JBQVNDLE1BQU02RDtnQkFBYTtZQUNqRDtRQUNGLEVBQUUsT0FBT2IsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMseUJBQXlCQTtZQUN2Q2xELFdBQVc7Z0JBQUVDLE1BQU07Z0JBQVNDLE1BQU07WUFBMkI7UUFDL0QsU0FBVTtZQUNSSixVQUFVO1FBQ1o7SUFDRjtJQUVBLE1BQU1vRSxlQUFlO1FBQ25CLElBQUl6RSxNQUFNO1lBQ1JVLFlBQVk7Z0JBQ1YsR0FBR1YsSUFBSTtnQkFDUHVCLFNBQVN2QixLQUFLdUIsT0FBTyxJQUFJO29CQUN2QkMsUUFBUTtvQkFDUkMsTUFBTTtvQkFDTkMsT0FBTztvQkFDUEMsU0FBUztvQkFDVEMsU0FBUztnQkFDWDtZQUNGO1FBQ0Y7UUFDQXpCLFdBQVc7UUFDWEksV0FBVztZQUFFQyxNQUFNO1lBQUlDLE1BQU07UUFBRztJQUNsQztJQUVBLE1BQU1pRSx3QkFBd0I7UUFDNUI5RCx1QkFBdUI7UUFDdkJFLHVCQUF1QjtRQUV2QixJQUFJO1lBQ0YsTUFBTW1DLFdBQVcsTUFBTXZELDZEQUFrQkEsQ0FBQywrQkFBK0I7Z0JBQ3ZFeUQsUUFBUTtZQUNWO1lBRUEsTUFBTUcsT0FBTyxNQUFNTCxTQUFTTSxJQUFJO1lBRWhDLElBQUlELEtBQUtFLE9BQU8sRUFBRTtnQkFDaEIsSUFBSUYsS0FBS3FCLGdCQUFnQixFQUFFO29CQUN6QixtREFBbUQ7b0JBQ25ELE1BQU1DLGtCQUFrQixHQUFnRHRCLE9BQTdDdUIsT0FBT0MsUUFBUSxDQUFDQyxNQUFNLEVBQUMsd0JBQTRDLE9BQXRCekIsS0FBS3FCLGdCQUFnQjtvQkFDN0Y3RCx1QkFBdUIsb0JBQW9DLE9BQWhCOEQ7Z0JBQzdDLE9BQU87b0JBQ0w5RCx1QkFBdUI7Z0JBQ3pCO1lBQ0YsT0FBTztnQkFDTEEsdUJBQXVCd0MsS0FBS2hELE9BQU8sSUFBSTtZQUN6QztRQUNGLEVBQUUsT0FBT21ELE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLDRCQUE0QkE7WUFDMUMzQyx1QkFBdUI7UUFDekIsU0FBVTtZQUNSRix1QkFBdUI7UUFDekI7SUFDRjtJQUVBLElBQUksQ0FBQ1osTUFBTSxPQUFPO0lBRWxCLHFCQUNFLDhEQUFDTCw2RUFBZUE7UUFBQ3FGLE9BQU07OzBCQUNyQiw4REFBQ3BGLGtGQUFlQTtnQkFDZG9GLE9BQU07Z0JBQ05DLFVBQVM7Z0JBQ1RDLHVCQUNFLDhEQUFDQztvQkFBSUMsV0FBVTs7d0JBQ1psRix5QkFDQyw4REFBQ21GOzRCQUNDQyxTQUFTYjs0QkFDVFcsV0FBVTs0QkFDVkcsVUFBVW5GO3NDQUNYOzs7Ozs7c0NBSUgsOERBQUNpRjs0QkFDQ0MsU0FBUyxJQUFNcEYsVUFBVTJELGVBQWUxRCxXQUFXOzRCQUNuRGlGLFdBQVU7NEJBQ1ZHLFVBQVVuRjtzQ0FFVEEsdUJBQ0M7O2tEQUNFLDhEQUFDK0U7d0NBQUlDLFdBQVU7Ozs7OztvQ0FBdUc7OytDQUd0SGxGLFVBQVUsaUJBQWlCOzs7Ozs7Ozs7Ozs7Ozs7OzswQkFNdkMsOERBQUNpRjtnQkFBSUMsV0FBVTs7b0JBRVo5RSxRQUFRRyxJQUFJLGtCQUNYLDhEQUFDMEU7d0JBQUlDLFdBQVcsdUJBSWYsT0FIQzlFLFFBQVFFLElBQUksS0FBSyxZQUNiLHVEQUNBO2tDQUVIRixRQUFRRyxJQUFJOzs7Ozs7a0NBS2pCLDhEQUFDMEU7d0JBQUlDLFdBQVU7a0NBQ2IsNEVBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0Q7b0NBQUlDLFdBQVU7OENBQ1puRSw0QkFDQyw4REFBQ3VFO3dDQUNDQyxLQUFLeEU7d0NBQ0x5RSxLQUFJO3dDQUNKTixXQUFVOzs7Ozs2REFHWiw4REFBQ087d0NBQUtQLFdBQVU7a0RBQ2JwRixLQUFLb0MsSUFBSSxDQUFDd0QsTUFBTSxDQUFDLEdBQUdDLFdBQVc7Ozs7Ozs7Ozs7OzhDQUl0Qyw4REFBQ1Y7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDVTs0Q0FBR1YsV0FBVTtzREFBZ0RwRixLQUFLb0MsSUFBSTs7Ozs7O3NEQUN2RSw4REFBQzJEOzRDQUFFWCxXQUFVO3NEQUFzQnBGLEtBQUtnRyxLQUFLOzs7Ozs7c0RBQzdDLDhEQUFDYjs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNPO29EQUFLUCxXQUFXLDREQUFtSixPQUF2RnBGLEtBQUtpRyxlQUFlLEdBQUcsZ0NBQWdDOzhEQUNqSWpHLEtBQUtpRyxlQUFlLEdBQUcscUJBQXFCOzs7Ozs7Z0RBRTlDLENBQUNqRyxLQUFLaUcsZUFBZSxrQkFDcEIsOERBQUNaO29EQUNDQyxTQUFTWjtvREFDVGEsVUFBVTVFO29EQUNWeUUsV0FBVTs4REFFVHpFLG9DQUNDOzswRUFDRSw4REFBQ3dFO2dFQUFJQyxXQUFVOzs7Ozs7NERBQTZGOztxRkFJOUc7OzBFQUNFLDhEQUFDYztnRUFBSWQsV0FBVTtnRUFBZWUsTUFBSztnRUFBT0MsUUFBTztnRUFBZUMsU0FBUTswRUFDdEUsNEVBQUNDO29FQUFLQyxlQUFjO29FQUFRQyxnQkFBZTtvRUFBUUMsYUFBYTtvRUFBR0MsR0FBRTs7Ozs7Ozs7Ozs7NERBQ2pFOzs7Ozs7Ozs7Ozs7Ozt3Q0FPZjdGLHFDQUNDLDhEQUFDc0U7NENBQUlDLFdBQVcsK0JBSWYsT0FIQ3ZFLG9CQUFvQmtCLFFBQVEsQ0FBQyxXQUFXbEIsb0JBQW9Ca0IsUUFBUSxDQUFDLHNCQUNqRSx1REFDQTtzREFFSGxCLG9CQUFvQjhGLFVBQVUsQ0FBQyxxQ0FDOUIsOERBQUN4Qjs7a0VBQ0MsOERBQUNZO3dEQUFFWCxXQUFVO2tFQUFPOzs7Ozs7a0VBQ3BCLDhEQUFDd0I7d0RBQ0NDLE1BQU1oRyxvQkFBb0JpRyxPQUFPLENBQUMscUJBQXFCO3dEQUN2RDFCLFdBQVU7d0RBQ1Y5QyxRQUFPO3dEQUNQeUUsS0FBSTs7MEVBRUosOERBQUNiO2dFQUFJZCxXQUFVO2dFQUFlZSxNQUFLO2dFQUFPQyxRQUFPO2dFQUFlQyxTQUFROzBFQUN0RSw0RUFBQ0M7b0VBQUtDLGVBQWM7b0VBQVFDLGdCQUFlO29FQUFRQyxhQUFhO29FQUFHQyxHQUFFOzs7Ozs7Ozs7Ozs0REFDakU7Ozs7Ozs7Ozs7Ozt1REFLVjdGOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FTWiw4REFBQ3NFO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQzRCO2dDQUFHNUIsV0FBVTswQ0FBMkM7Ozs7OzswQ0FFekQsOERBQUNEO2dDQUFJQyxXQUFVOztrREFFYiw4REFBQ0Q7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDNkI7Z0RBQUc3QixXQUFVOzBEQUFrRTs7Ozs7OzBEQUloRiw4REFBQ0Q7O2tFQUNDLDhEQUFDK0I7d0RBQU05QixXQUFVO2tFQUErQzs7Ozs7O2tFQUNoRSw4REFBQytCO3dEQUNDM0csTUFBSzt3REFDTDRCLE1BQUs7d0RBQ0xDLE9BQU90QyxTQUFTcUMsSUFBSSxJQUFJO3dEQUN4QmdGLFVBQVVsRjt3REFDVnFELFVBQVUsQ0FBQ3JGO3dEQUNYa0YsV0FBVyxxSUFBa0ssT0FBN0IsQ0FBQ2xGLFVBQVUsZUFBZTs7Ozs7Ozs7Ozs7OzBEQUk5Syw4REFBQ2lGOztrRUFDQyw4REFBQytCO3dEQUFNOUIsV0FBVTtrRUFBK0M7Ozs7OztrRUFDaEUsOERBQUMrQjt3REFDQzNHLE1BQUs7d0RBQ0w0QixNQUFLO3dEQUNMQyxPQUFPdEMsU0FBU2lHLEtBQUssSUFBSTt3REFDekJvQixVQUFVbEY7d0RBQ1ZxRCxVQUFVLENBQUNyRjt3REFDWGtGLFdBQVcscUlBQWtLLE9BQTdCLENBQUNsRixVQUFVLGVBQWU7Ozs7Ozs7Ozs7OzswREFJOUssOERBQUNpRjs7a0VBQ0MsOERBQUMrQjt3REFBTTlCLFdBQVU7a0VBQStDOzs7Ozs7a0VBQ2hFLDhEQUFDK0I7d0RBQ0MzRyxNQUFLO3dEQUNMNEIsTUFBSzt3REFDTEMsT0FBT3RDLFNBQVNtRSxLQUFLLElBQUk7d0RBQ3pCa0QsVUFBVWxGO3dEQUNWcUQsVUFBVSxDQUFDckY7d0RBQ1htSCxhQUFZO3dEQUNaakMsV0FBVyxxSUFBa0ssT0FBN0IsQ0FBQ2xGLFVBQVUsZUFBZTs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tEQU1oTCw4REFBQ2lGO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQzZCO2dEQUFHN0IsV0FBVTswREFBa0U7Ozs7OzswREFJaEYsOERBQUNEOztrRUFDQyw4REFBQytCO3dEQUFNOUIsV0FBVTtrRUFBK0M7Ozs7OztrRUFDaEUsOERBQUMrQjt3REFDQzNHLE1BQUs7d0RBQ0w0QixNQUFLO3dEQUNMQyxPQUFPdEMsU0FBU2lFLFlBQVksSUFBSTt3REFDaENvRCxVQUFVbEY7d0RBQ1ZxRCxVQUFVLENBQUNyRjt3REFDWG1ILGFBQVk7d0RBQ1pqQyxXQUFXLHFJQUFrSyxPQUE3QixDQUFDbEYsVUFBVSxlQUFlOzs7Ozs7Ozs7Ozs7MERBSTlLLDhEQUFDaUY7O2tFQUNDLDhEQUFDK0I7d0RBQU05QixXQUFVO2tFQUErQzs7Ozs7O2tFQUNoRSw4REFBQytCO3dEQUNDM0csTUFBSzt3REFDTDRCLE1BQUs7d0RBQ0xDLE9BQU90QyxTQUFTa0UsU0FBUyxJQUFJO3dEQUM3Qm1ELFVBQVVsRjt3REFDVnFELFVBQVUsQ0FBQ3JGO3dEQUNYbUgsYUFBWTt3REFDWmpDLFdBQVcscUlBQWtLLE9BQTdCLENBQUNsRixVQUFVLGVBQWU7Ozs7Ozs7Ozs7OzswREFLOUssOERBQUNpRjs7a0VBQ0MsOERBQUMrQjt3REFBTTlCLFdBQVU7a0VBQStDOzs7Ozs7b0RBQy9EbkUsNEJBQ0MsOERBQUNrRTt3REFBSUMsV0FBVTs7MEVBQ2IsOERBQUNEO2dFQUFJQyxXQUFVOzBFQUNiLDRFQUFDSTtvRUFDQ0MsS0FBS3hFO29FQUNMeUUsS0FBSTtvRUFDSk4sV0FBVTs7Ozs7Ozs7Ozs7NERBR2JsRix5QkFDQyw4REFBQ2lGO2dFQUFJQyxXQUFVOztrRkFDYiw4REFBQ3ZGLDhEQUFVQTt3RUFDVHlILFVBQVU5RTt3RUFDVitFLFFBQU87d0VBQ1BDLFNBQVM7d0VBQ1RDLFVBQVU7d0VBQ1ZyQyxXQUFVOzs7Ozs7a0ZBRVosOERBQUNDO3dFQUNDQyxTQUFTM0I7d0VBQ1Q0QixVQUFVeEU7d0VBQ1ZxRSxXQUFVO2tGQUVUckUsZ0JBQWdCLGdCQUFnQjs7Ozs7Ozs7Ozs7Ozs7Ozs7NkVBTXpDLDhEQUFDb0U7d0RBQUlDLFdBQVU7OzBFQUNiLDhEQUFDRDtnRUFBSUMsV0FBVTswRUFDYiw0RUFBQ0Q7b0VBQUlDLFdBQVU7O3NGQUNiLDhEQUFDYzs0RUFBSWQsV0FBVTs0RUFBcUNlLE1BQUs7NEVBQU9DLFFBQU87NEVBQWVDLFNBQVE7c0ZBQzVGLDRFQUFDQztnRkFBS0MsZUFBYztnRkFBUUMsZ0JBQWU7Z0ZBQVFDLGFBQWE7Z0ZBQUdDLEdBQUU7Ozs7Ozs7Ozs7O3NGQUV2RSw4REFBQ1g7NEVBQUVYLFdBQVU7c0ZBQXdCOzs7Ozs7Ozs7Ozs7Ozs7Ozs0REFHeENsRix5QkFDQyw4REFBQ0wsOERBQVVBO2dFQUNUeUgsVUFBVTlFO2dFQUNWK0UsUUFBTztnRUFDUEMsU0FBUztnRUFDVEMsVUFBVTtnRUFDVnJDLFdBQVU7Ozs7Ozs7Ozs7OztvREFLakJyRSwrQkFDQyw4REFBQ29FO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ0Q7Z0VBQUlDLFdBQVU7Ozs7Ozs0REFBK0Y7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBU3hILDhEQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUM2Qjt3Q0FBRzdCLFdBQVU7a0RBQXVFOzs7Ozs7a0RBSXJGLDhEQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNEO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQzhCO3dEQUFNOUIsV0FBVTtrRUFBK0M7Ozs7OztrRUFDaEUsOERBQUNzQzt3REFDQ3RGLE1BQUs7d0RBQ0xDLE9BQU90QyxFQUFBQSxvQkFBQUEsU0FBU3dCLE9BQU8sY0FBaEJ4Qix3Q0FBQUEsa0JBQWtCeUIsTUFBTSxLQUFJO3dEQUNuQzRGLFVBQVVsRjt3REFDVnFELFVBQVUsQ0FBQ3JGO3dEQUNYbUgsYUFBWTt3REFDWk0sTUFBTTt3REFDTnZDLFdBQVcscUlBQWtLLE9BQTdCLENBQUNsRixVQUFVLGVBQWU7Ozs7Ozs7Ozs7OzswREFJOUssOERBQUNpRjs7a0VBQ0MsOERBQUMrQjt3REFBTTlCLFdBQVU7a0VBQStDOzs7Ozs7a0VBQ2hFLDhEQUFDK0I7d0RBQ0MzRyxNQUFLO3dEQUNMNEIsTUFBSzt3REFDTEMsT0FBT3RDLEVBQUFBLHFCQUFBQSxTQUFTd0IsT0FBTyxjQUFoQnhCLHlDQUFBQSxtQkFBa0IwQixJQUFJLEtBQUk7d0RBQ2pDMkYsVUFBVWxGO3dEQUNWcUQsVUFBVSxDQUFDckY7d0RBQ1htSCxhQUFZO3dEQUNaakMsV0FBVyxxSUFBa0ssT0FBN0IsQ0FBQ2xGLFVBQVUsZUFBZTs7Ozs7Ozs7Ozs7OzBEQUk5Syw4REFBQ2lGOztrRUFDQyw4REFBQytCO3dEQUFNOUIsV0FBVTtrRUFBK0M7Ozs7OztrRUFDaEUsOERBQUMrQjt3REFDQzNHLE1BQUs7d0RBQ0w0QixNQUFLO3dEQUNMQyxPQUFPdEMsRUFBQUEscUJBQUFBLFNBQVN3QixPQUFPLGNBQWhCeEIseUNBQUFBLG1CQUFrQjJCLEtBQUssS0FBSTt3REFDbEMwRixVQUFVbEY7d0RBQ1ZxRCxVQUFVLENBQUNyRjt3REFDWG1ILGFBQVk7d0RBQ1pqQyxXQUFXLHFJQUFrSyxPQUE3QixDQUFDbEYsVUFBVSxlQUFlOzs7Ozs7Ozs7Ozs7MERBSTlLLDhEQUFDaUY7O2tFQUNDLDhEQUFDK0I7d0RBQU05QixXQUFVO2tFQUErQzs7Ozs7O2tFQUNoRSw4REFBQytCO3dEQUNDM0csTUFBSzt3REFDTDRCLE1BQUs7d0RBQ0xDLE9BQU90QyxFQUFBQSxxQkFBQUEsU0FBU3dCLE9BQU8sY0FBaEJ4Qix5Q0FBQUEsbUJBQWtCNEIsT0FBTyxLQUFJO3dEQUNwQ3lGLFVBQVVsRjt3REFDVnFELFVBQVUsQ0FBQ3JGO3dEQUNYbUgsYUFBWTt3REFDWmpDLFdBQVcscUlBQWtLLE9BQTdCLENBQUNsRixVQUFVLGVBQWU7Ozs7Ozs7Ozs7OzswREFJOUssOERBQUNpRjs7a0VBQ0MsOERBQUMrQjt3REFBTTlCLFdBQVU7a0VBQStDOzs7Ozs7a0VBQ2hFLDhEQUFDK0I7d0RBQ0MzRyxNQUFLO3dEQUNMNEIsTUFBSzt3REFDTEMsT0FBT3RDLEVBQUFBLHFCQUFBQSxTQUFTd0IsT0FBTyxjQUFoQnhCLHlDQUFBQSxtQkFBa0I2QixPQUFPLEtBQUk7d0RBQ3BDd0YsVUFBVWxGO3dEQUNWcUQsVUFBVSxDQUFDckY7d0RBQ1htSCxhQUFZO3dEQUNaakMsV0FBVyxxSUFBa0ssT0FBN0IsQ0FBQ2xGLFVBQVUsZUFBZTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzRCQU9qTEEseUJBQ0MsOERBQUNpRjtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNDO3dDQUNDQyxTQUFTYjt3Q0FDVFcsV0FBVTt3Q0FDVkcsVUFBVW5GO2tEQUNYOzs7Ozs7a0RBR0QsOERBQUNpRjt3Q0FDQ0MsU0FBU3pCO3dDQUNUdUIsV0FBVTt3Q0FDVkcsVUFBVW5GO2tEQUVUQSx1QkFDQzs7OERBQ0UsOERBQUMrRTtvREFBSUMsV0FBVTs7Ozs7O2dEQUF1Rzs7MkRBR3RIOzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBT1osOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQzZCO2dDQUFHN0IsV0FBVTswQ0FBeUM7Ozs7OzswQ0FDdkQsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0Q7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDTztnREFBS1AsV0FBVTswREFBb0M7Ozs7OzswREFDcEQsOERBQUNPO2dEQUFLUCxXQUFVOzBEQUFvQ3BGLEtBQUs0SCxJQUFJOzs7Ozs7Ozs7Ozs7a0RBRS9ELDhEQUFDekM7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDTztnREFBS1AsV0FBVTswREFBb0M7Ozs7OzswREFDcEQsOERBQUNPO2dEQUFLUCxXQUFVOzBEQUNicEYsS0FBSzZILFNBQVMsR0FBRyxJQUFJQyxLQUFLOUgsS0FBSzZILFNBQVMsRUFBRUUsa0JBQWtCLENBQUMsV0FBVzs7Ozs7Ozs7Ozs7O2tEQUc3RSw4REFBQzVDO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ087Z0RBQUtQLFdBQVU7MERBQW9DOzs7Ozs7MERBQ3BELDhEQUFDTztnREFBS1AsV0FBVTswREFDYnBGLEtBQUtnSSxTQUFTLEdBQUcsSUFBSUYsS0FBSzlILEtBQUtnSSxTQUFTLEVBQUVELGtCQUFrQixDQUFDLFdBQVc7Ozs7Ozs7Ozs7OztrREFHN0UsOERBQUM1Qzt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNPO2dEQUFLUCxXQUFVOzBEQUFvQzs7Ozs7OzBEQUNwRCw4REFBQ0Q7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDTzt3REFBS1AsV0FBVyw0REFBbUosT0FBdkZwRixLQUFLaUcsZUFBZSxHQUFHLGdDQUFnQztrRUFDaklqRyxLQUFLaUcsZUFBZSxHQUFHLGFBQWE7Ozs7OztvREFFdEMsQ0FBQ2pHLEtBQUtpRyxlQUFlLGtCQUNwQiw4REFBQ1o7d0RBQ0NDLFNBQVNaO3dEQUNUYSxVQUFVNUU7d0RBQ1Z5RSxXQUFVO2tFQUVUekUsc0JBQXNCLGVBQWU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVUxRDtHQTlvQndCYjs7UUFDT04sMERBQU9BOzs7S0FEZE0iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2FwcC9kYXNoYm9hcmQvcHJvZmlsZS9wYWdlLnRzeD83MTQ5Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IHVzZUF1dGggfSBmcm9tICcuLi8uLi8uLi9jb250ZXh0cy9BdXRoQ29udGV4dCc7XG5pbXBvcnQgeyB1cGRhdGVQcm9maWxlLCBhdXRoZW50aWNhdGVkRmV0Y2ggfSBmcm9tICcuLi8uLi8uLi9saWIvYXV0aCc7XG5pbXBvcnQgRGFzaGJvYXJkTGF5b3V0LCB7IERhc2hib2FyZEhlYWRlciB9IGZyb20gJy4uLy4uLy4uL2NvbXBvbmVudHMvZGFzaGJvYXJkL0Rhc2hib2FyZExheW91dCc7XG5pbXBvcnQgRmlsZVVwbG9hZCBmcm9tICcuLi8uLi8uLi9jb21wb25lbnRzL0ZpbGVVcGxvYWQnO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBQcm9maWxlUGFnZSgpIHtcbiAgY29uc3QgeyB1c2VyLCB1cGRhdGVVc2VyIH0gPSB1c2VBdXRoKCk7XG4gIGNvbnN0IFtlZGl0aW5nLCBzZXRFZGl0aW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW3NhdmluZywgc2V0U2F2aW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW21lc3NhZ2UsIHNldE1lc3NhZ2VdID0gdXNlU3RhdGUoeyB0eXBlOiAnJywgdGV4dDogJycgfSk7XG4gIGNvbnN0IFtmb3JtRGF0YSwgc2V0Rm9ybURhdGFdID0gdXNlU3RhdGU8YW55Pih7fSk7XG4gIGNvbnN0IFt2ZXJpZmljYXRpb25Mb2FkaW5nLCBzZXRWZXJpZmljYXRpb25Mb2FkaW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW3ZlcmlmaWNhdGlvbk1lc3NhZ2UsIHNldFZlcmlmaWNhdGlvbk1lc3NhZ2VdID0gdXNlU3RhdGUoJycpO1xuICBjb25zdCBbbG9nb1VwbG9hZGluZywgc2V0TG9nb1VwbG9hZGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtsb2dvUHJldmlldywgc2V0TG9nb1ByZXZpZXddID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFtzaWduYXR1cmVVcGxvYWRpbmcsIHNldFNpZ25hdHVyZVVwbG9hZGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtzaWduYXR1cmVQcmV2aWV3LCBzZXRTaWduYXR1cmVQcmV2aWV3XSA9IHVzZVN0YXRlPHN0cmluZyB8IG51bGw+KG51bGwpO1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKHVzZXIpIHtcbiAgICAgIHNldEZvcm1EYXRhKHtcbiAgICAgICAgLi4udXNlcixcbiAgICAgICAgYWRkcmVzczogdXNlci5hZGRyZXNzIHx8IHtcbiAgICAgICAgICBzdHJlZXQ6ICcnLFxuICAgICAgICAgIGNpdHk6ICcnLFxuICAgICAgICAgIHN0YXRlOiAnJyxcbiAgICAgICAgICBwaW5jb2RlOiAnJyxcbiAgICAgICAgICBjb3VudHJ5OiAnSW5kaWEnXG4gICAgICAgIH1cbiAgICAgIH0pO1xuICAgICAgLy8gU2V0IGxvZ28gcHJldmlldyBpZiB1c2VyIGhhcyBhIGxvZ29cbiAgICAgIGlmICh1c2VyLmxvZ28pIHtcbiAgICAgICAgLy8gSGFuZGxlIGJvdGggZnVsbCBwYXRocyBhbmQganVzdCBmaWxlbmFtZXNcbiAgICAgICAgY29uc3QgbG9nb1BhdGggPSB1c2VyLmxvZ28uaW5jbHVkZXMoJy8nKSA/IHVzZXIubG9nbyA6IGB1cGxvYWRzL2xvZ29zLyR7dXNlci5sb2dvfWA7XG4gICAgICAgIHNldExvZ29QcmV2aWV3KGBodHRwOi8vbG9jYWxob3N0OjUwMDAvJHtsb2dvUGF0aH1gKTtcbiAgICAgIH1cbiAgICAgIC8vIFNldCBzaWduYXR1cmUgcHJldmlldyBpZiB1c2VyIGhhcyBhIHNpZ25hdHVyZVxuICAgICAgaWYgKHVzZXIuc2lnbmF0dXJlKSB7XG4gICAgICAgIC8vIEhhbmRsZSBib3RoIGZ1bGwgcGF0aHMgYW5kIGp1c3QgZmlsZW5hbWVzXG4gICAgICAgIGNvbnN0IHNpZ25hdHVyZVBhdGggPSB1c2VyLnNpZ25hdHVyZS5pbmNsdWRlcygnLycpID8gdXNlci5zaWduYXR1cmUgOiBgdXBsb2Fkcy9zaWduYXR1cmVzLyR7dXNlci5zaWduYXR1cmV9YDtcbiAgICAgICAgc2V0U2lnbmF0dXJlUHJldmlldyhgaHR0cDovL2xvY2FsaG9zdDo1MDAwLyR7c2lnbmF0dXJlUGF0aH1gKTtcbiAgICAgIH1cbiAgICB9XG4gIH0sIFt1c2VyXSk7XG5cbiAgY29uc3QgaGFuZGxlSW5wdXRDaGFuZ2UgPSAoZTogUmVhY3QuQ2hhbmdlRXZlbnQ8SFRNTElucHV0RWxlbWVudCB8IEhUTUxUZXh0QXJlYUVsZW1lbnQ+KSA9PiB7XG4gICAgY29uc3QgeyBuYW1lLCB2YWx1ZSB9ID0gZS50YXJnZXQ7XG5cbiAgICAvLyBIYW5kbGUgbmVzdGVkIGFkZHJlc3MgZmllbGRzXG4gICAgaWYgKFsnc3RyZWV0JywgJ2NpdHknLCAnc3RhdGUnLCAncGluY29kZScsICdjb3VudHJ5J10uaW5jbHVkZXMobmFtZSkpIHtcbiAgICAgIHNldEZvcm1EYXRhKChwcmV2OiBhbnkpID0+ICh7XG4gICAgICAgIC4uLnByZXYsXG4gICAgICAgIGFkZHJlc3M6IHtcbiAgICAgICAgICAuLi5wcmV2LmFkZHJlc3MsXG4gICAgICAgICAgW25hbWVdOiB2YWx1ZVxuICAgICAgICB9XG4gICAgICB9KSk7XG4gICAgfSBlbHNlIHtcbiAgICAgIHNldEZvcm1EYXRhKChwcmV2OiBhbnkpID0+ICh7IC4uLnByZXYsIFtuYW1lXTogdmFsdWUgfSkpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBoYW5kbGVMb2dvVXBsb2FkID0gYXN5bmMgKGZpbGVzOiBGaWxlW10pID0+IHtcbiAgICBpZiAoZmlsZXMubGVuZ3RoID09PSAwKSByZXR1cm47XG5cbiAgICBjb25zdCBmaWxlID0gZmlsZXNbMF07XG4gICAgc2V0TG9nb1VwbG9hZGluZyh0cnVlKTtcbiAgICBzZXRNZXNzYWdlKHsgdHlwZTogJycsIHRleHQ6ICcnIH0pO1xuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IGZvcm1EYXRhID0gbmV3IEZvcm1EYXRhKCk7XG4gICAgICBmb3JtRGF0YS5hcHBlbmQoJ2xvZ28nLCBmaWxlKTtcblxuICAgICAgY29uc3QgdG9rZW4gPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgndG9rZW4nKTtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJ2h0dHA6Ly9sb2NhbGhvc3Q6NTAwMC9hcGkvdXBsb2Fkcy9sb2dvJywge1xuICAgICAgICBtZXRob2Q6ICdQT1NUJyxcbiAgICAgICAgaGVhZGVyczoge1xuICAgICAgICAgICdBdXRob3JpemF0aW9uJzogYEJlYXJlciAke3Rva2VufWAsXG4gICAgICAgIH0sXG4gICAgICAgIGJvZHk6IGZvcm1EYXRhLFxuICAgICAgfSk7XG5cbiAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG5cbiAgICAgIGlmIChkYXRhLnN1Y2Nlc3MpIHtcbiAgICAgICAgLy8gVXBkYXRlIGZvcm0gZGF0YSB3aXRoIG5ldyBsb2dvIHBhdGhcbiAgICAgICAgc2V0Rm9ybURhdGEoKHByZXY6IGFueSkgPT4gKHsgLi4ucHJldiwgbG9nbzogZGF0YS5kYXRhLmxvZ29QYXRoIH0pKTtcbiAgICAgICAgLy8gU2V0IHByZXZpZXdcbiAgICAgICAgc2V0TG9nb1ByZXZpZXcoYGh0dHA6Ly9sb2NhbGhvc3Q6NTAwMC8ke2RhdGEuZGF0YS5sb2dvUGF0aH1gKTtcbiAgICAgICAgLy8gVXBkYXRlIHVzZXIgY29udGV4dCB3aXRoIG5ldyBsb2dvXG4gICAgICAgIGlmICh1c2VyKSB7XG4gICAgICAgICAgdXBkYXRlVXNlcih7IC4uLnVzZXIsIGxvZ286IGRhdGEuZGF0YS5sb2dvUGF0aCB9KTtcbiAgICAgICAgfVxuICAgICAgICBzZXRNZXNzYWdlKHsgdHlwZTogJ3N1Y2Nlc3MnLCB0ZXh0OiAnTG9nbyB1cGxvYWRlZCBzdWNjZXNzZnVsbHkhJyB9KTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHNldE1lc3NhZ2UoeyB0eXBlOiAnZXJyb3InLCB0ZXh0OiBkYXRhLm1lc3NhZ2UgfHwgJ0ZhaWxlZCB0byB1cGxvYWQgbG9nbycgfSk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0xvZ28gdXBsb2FkIGVycm9yOicsIGVycm9yKTtcbiAgICAgIHNldE1lc3NhZ2UoeyB0eXBlOiAnZXJyb3InLCB0ZXh0OiAnRmFpbGVkIHRvIHVwbG9hZCBsb2dvJyB9KTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0TG9nb1VwbG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGhhbmRsZVJlbW92ZUxvZ28gPSBhc3luYyAoKSA9PiB7XG4gICAgc2V0TG9nb1VwbG9hZGluZyh0cnVlKTtcbiAgICB0cnkge1xuICAgICAgY29uc3QgdG9rZW4gPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgndG9rZW4nKTtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJ2h0dHA6Ly9sb2NhbGhvc3Q6NTAwMC9hcGkvdXBsb2Fkcy9sb2dvJywge1xuICAgICAgICBtZXRob2Q6ICdERUxFVEUnLFxuICAgICAgICBoZWFkZXJzOiB7XG4gICAgICAgICAgJ0F1dGhvcml6YXRpb24nOiBgQmVhcmVyICR7dG9rZW59YCxcbiAgICAgICAgfSxcbiAgICAgIH0pO1xuXG4gICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgaWYgKGRhdGEuc3VjY2Vzcykge1xuICAgICAgICBzZXRGb3JtRGF0YSgocHJldjogYW55KSA9PiAoeyAuLi5wcmV2LCBsb2dvOiBudWxsIH0pKTtcbiAgICAgICAgc2V0TG9nb1ByZXZpZXcobnVsbCk7XG4gICAgICAgIC8vIFVwZGF0ZSB1c2VyIGNvbnRleHQgdG8gcmVtb3ZlIGxvZ29cbiAgICAgICAgaWYgKHVzZXIpIHtcbiAgICAgICAgICB1cGRhdGVVc2VyKHsgLi4udXNlciwgbG9nbzogdW5kZWZpbmVkIH0pO1xuICAgICAgICB9XG4gICAgICAgIHNldE1lc3NhZ2UoeyB0eXBlOiAnc3VjY2VzcycsIHRleHQ6ICdMb2dvIHJlbW92ZWQgc3VjY2Vzc2Z1bGx5IScgfSk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBzZXRNZXNzYWdlKHsgdHlwZTogJ2Vycm9yJywgdGV4dDogZGF0YS5tZXNzYWdlIHx8ICdGYWlsZWQgdG8gcmVtb3ZlIGxvZ28nIH0pO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdMb2dvIHJlbW92ZSBlcnJvcjonLCBlcnJvcik7XG4gICAgICBzZXRNZXNzYWdlKHsgdHlwZTogJ2Vycm9yJywgdGV4dDogJ0ZhaWxlZCB0byByZW1vdmUgbG9nbycgfSk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldExvZ29VcGxvYWRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBoYW5kbGVTYXZlID0gYXN5bmMgKCkgPT4ge1xuICAgIHNldFNhdmluZyh0cnVlKTtcbiAgICBzZXRNZXNzYWdlKHsgdHlwZTogJycsIHRleHQ6ICcnIH0pO1xuXG4gICAgdHJ5IHtcbiAgICAgIC8vIENsZWFuIHVwIHRoZSBmb3JtIGRhdGEgYmVmb3JlIHNlbmRpbmdcbiAgICAgIGNvbnN0IGNsZWFuRm9ybURhdGE6IGFueSA9IHt9O1xuXG4gICAgICAvLyBPbmx5IGluY2x1ZGUgbm9uLWVtcHR5IGZpZWxkc1xuICAgICAgaWYgKGZvcm1EYXRhLm5hbWU/LnRyaW0oKSkge1xuICAgICAgICBjbGVhbkZvcm1EYXRhLm5hbWUgPSBmb3JtRGF0YS5uYW1lLnRyaW0oKTtcbiAgICAgIH1cbiAgICAgIGlmIChmb3JtRGF0YS5idXNpbmVzc05hbWU/LnRyaW0oKSkge1xuICAgICAgICBjbGVhbkZvcm1EYXRhLmJ1c2luZXNzTmFtZSA9IGZvcm1EYXRhLmJ1c2luZXNzTmFtZS50cmltKCk7XG4gICAgICB9XG4gICAgICBpZiAoZm9ybURhdGEuZ3N0TnVtYmVyPy50cmltKCkpIHtcbiAgICAgICAgY2xlYW5Gb3JtRGF0YS5nc3ROdW1iZXIgPSBmb3JtRGF0YS5nc3ROdW1iZXIudHJpbSgpO1xuICAgICAgfVxuICAgICAgaWYgKGZvcm1EYXRhLnBob25lPy50cmltKCkpIHtcbiAgICAgICAgY2xlYW5Gb3JtRGF0YS5waG9uZSA9IGZvcm1EYXRhLnBob25lLnRyaW0oKTtcbiAgICAgIH1cblxuICAgICAgLy8gSGFuZGxlIGFkZHJlc3Mgc2VwYXJhdGVseVxuICAgICAgY29uc3QgYWRkcmVzczogYW55ID0ge307XG4gICAgICBpZiAoZm9ybURhdGEuYWRkcmVzcz8uc3RyZWV0Py50cmltKCkpIHtcbiAgICAgICAgYWRkcmVzcy5zdHJlZXQgPSBmb3JtRGF0YS5hZGRyZXNzLnN0cmVldC50cmltKCk7XG4gICAgICB9XG4gICAgICBpZiAoZm9ybURhdGEuYWRkcmVzcz8uY2l0eT8udHJpbSgpKSB7XG4gICAgICAgIGFkZHJlc3MuY2l0eSA9IGZvcm1EYXRhLmFkZHJlc3MuY2l0eS50cmltKCk7XG4gICAgICB9XG4gICAgICBpZiAoZm9ybURhdGEuYWRkcmVzcz8uc3RhdGU/LnRyaW0oKSkge1xuICAgICAgICBhZGRyZXNzLnN0YXRlID0gZm9ybURhdGEuYWRkcmVzcy5zdGF0ZS50cmltKCk7XG4gICAgICB9XG4gICAgICBpZiAoZm9ybURhdGEuYWRkcmVzcz8ucGluY29kZT8udHJpbSgpKSB7XG4gICAgICAgIGFkZHJlc3MucGluY29kZSA9IGZvcm1EYXRhLmFkZHJlc3MucGluY29kZS50cmltKCk7XG4gICAgICB9XG4gICAgICBpZiAoZm9ybURhdGEuYWRkcmVzcz8uY291bnRyeT8udHJpbSgpKSB7XG4gICAgICAgIGFkZHJlc3MuY291bnRyeSA9IGZvcm1EYXRhLmFkZHJlc3MuY291bnRyeS50cmltKCk7XG4gICAgICB9XG5cbiAgICAgIC8vIE9ubHkgaW5jbHVkZSBhZGRyZXNzIGlmIGl0IGhhcyBhdCBsZWFzdCBvbmUgZmllbGRcbiAgICAgIGlmIChPYmplY3Qua2V5cyhhZGRyZXNzKS5sZW5ndGggPiAwKSB7XG4gICAgICAgIGNsZWFuRm9ybURhdGEuYWRkcmVzcyA9IGFkZHJlc3M7XG4gICAgICB9XG5cbiAgICAgIC8vIEluY2x1ZGUgbG9nbyBpZiBpdCBleGlzdHNcbiAgICAgIGlmIChmb3JtRGF0YS5sb2dvKSB7XG4gICAgICAgIGNsZWFuRm9ybURhdGEubG9nbyA9IGZvcm1EYXRhLmxvZ287XG4gICAgICB9XG5cbiAgICAgIGNvbnNvbGUubG9nKCdTZW5kaW5nIHByb2ZpbGUgZGF0YTonLCBjbGVhbkZvcm1EYXRhKTtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdXBkYXRlUHJvZmlsZShjbGVhbkZvcm1EYXRhKTtcblxuICAgICAgaWYgKHJlc3BvbnNlLnN1Y2Nlc3MgJiYgcmVzcG9uc2UuZGF0YSkge1xuICAgICAgICB1cGRhdGVVc2VyKHJlc3BvbnNlLmRhdGEudXNlcik7XG4gICAgICAgIHNldEVkaXRpbmcoZmFsc2UpO1xuICAgICAgICBzZXRNZXNzYWdlKHsgdHlwZTogJ3N1Y2Nlc3MnLCB0ZXh0OiAnUHJvZmlsZSB1cGRhdGVkIHN1Y2Nlc3NmdWxseSEnIH0pO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgY29uc29sZS5lcnJvcignUHJvZmlsZSB1cGRhdGUgZmFpbGVkOicsIHJlc3BvbnNlKTtcbiAgICAgICAgY29uc3QgZXJyb3JNZXNzYWdlID0gcmVzcG9uc2UuZXJyb3JzID8gcmVzcG9uc2UuZXJyb3JzLmpvaW4oJywgJykgOiByZXNwb25zZS5tZXNzYWdlIHx8ICdGYWlsZWQgdG8gdXBkYXRlIHByb2ZpbGUnO1xuICAgICAgICBzZXRNZXNzYWdlKHsgdHlwZTogJ2Vycm9yJywgdGV4dDogZXJyb3JNZXNzYWdlIH0pO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdQcm9maWxlIHVwZGF0ZSBlcnJvcjonLCBlcnJvcik7XG4gICAgICBzZXRNZXNzYWdlKHsgdHlwZTogJ2Vycm9yJywgdGV4dDogJ0ZhaWxlZCB0byB1cGRhdGUgcHJvZmlsZScgfSk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldFNhdmluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGhhbmRsZUNhbmNlbCA9ICgpID0+IHtcbiAgICBpZiAodXNlcikge1xuICAgICAgc2V0Rm9ybURhdGEoe1xuICAgICAgICAuLi51c2VyLFxuICAgICAgICBhZGRyZXNzOiB1c2VyLmFkZHJlc3MgfHwge1xuICAgICAgICAgIHN0cmVldDogJycsXG4gICAgICAgICAgY2l0eTogJycsXG4gICAgICAgICAgc3RhdGU6ICcnLFxuICAgICAgICAgIHBpbmNvZGU6ICcnLFxuICAgICAgICAgIGNvdW50cnk6ICdJbmRpYSdcbiAgICAgICAgfVxuICAgICAgfSk7XG4gICAgfVxuICAgIHNldEVkaXRpbmcoZmFsc2UpO1xuICAgIHNldE1lc3NhZ2UoeyB0eXBlOiAnJywgdGV4dDogJycgfSk7XG4gIH07XG5cbiAgY29uc3Qgc2VuZFZlcmlmaWNhdGlvbkVtYWlsID0gYXN5bmMgKCkgPT4ge1xuICAgIHNldFZlcmlmaWNhdGlvbkxvYWRpbmcodHJ1ZSk7XG4gICAgc2V0VmVyaWZpY2F0aW9uTWVzc2FnZSgnJyk7XG5cbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBhdXRoZW50aWNhdGVkRmV0Y2goJy9hcGkvYXV0aC9zZW5kLXZlcmlmaWNhdGlvbicsIHtcbiAgICAgICAgbWV0aG9kOiAnUE9TVCdcbiAgICAgIH0pO1xuXG4gICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuXG4gICAgICBpZiAoZGF0YS5zdWNjZXNzKSB7XG4gICAgICAgIGlmIChkYXRhLmRldmVsb3BtZW50VG9rZW4pIHtcbiAgICAgICAgICAvLyBEZXZlbG9wbWVudCBtb2RlIC0gc2hvdyBkaXJlY3QgdmVyaWZpY2F0aW9uIGxpbmtcbiAgICAgICAgICBjb25zdCB2ZXJpZmljYXRpb25VcmwgPSBgJHt3aW5kb3cubG9jYXRpb24ub3JpZ2lufS92ZXJpZnktZW1haWw/dG9rZW49JHtkYXRhLmRldmVsb3BtZW50VG9rZW59YDtcbiAgICAgICAgICBzZXRWZXJpZmljYXRpb25NZXNzYWdlKGBkZXZlbG9wbWVudF9saW5rOiR7dmVyaWZpY2F0aW9uVXJsfWApO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIHNldFZlcmlmaWNhdGlvbk1lc3NhZ2UoJ1ZlcmlmaWNhdGlvbiBlbWFpbCBzZW50ISBQbGVhc2UgY2hlY2sgeW91ciBpbmJveC4nKTtcbiAgICAgICAgfVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgc2V0VmVyaWZpY2F0aW9uTWVzc2FnZShkYXRhLm1lc3NhZ2UgfHwgJ0ZhaWxlZCB0byBzZW5kIHZlcmlmaWNhdGlvbiBlbWFpbC4nKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignU2VuZCB2ZXJpZmljYXRpb24gZXJyb3I6JywgZXJyb3IpO1xuICAgICAgc2V0VmVyaWZpY2F0aW9uTWVzc2FnZSgnTmV0d29yayBlcnJvci4gUGxlYXNlIHRyeSBhZ2Fpbi4nKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0VmVyaWZpY2F0aW9uTG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIGlmICghdXNlcikgcmV0dXJuIG51bGw7XG5cbiAgcmV0dXJuIChcbiAgICA8RGFzaGJvYXJkTGF5b3V0IHRpdGxlPVwiUHJvZmlsZVwiPlxuICAgICAgPERhc2hib2FyZEhlYWRlclxuICAgICAgICB0aXRsZT1cIlByb2ZpbGVcIlxuICAgICAgICBzdWJ0aXRsZT1cIk1hbmFnZSB5b3VyIGFjY291bnQgaW5mb3JtYXRpb24gYW5kIGJ1c2luZXNzIGRldGFpbHNcIlxuICAgICAgICBhY3Rpb25zPXtcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtM1wiPlxuICAgICAgICAgICAge2VkaXRpbmcgJiYgKFxuICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlQ2FuY2VsfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTQgcHktMiBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbGcgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktODAwIGJnLXdoaXRlIGhvdmVyOmJnLWdyYXktNTAgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgICAgIGRpc2FibGVkPXtzYXZpbmd9XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICBDYW5jZWxcbiAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICApfVxuICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBlZGl0aW5nID8gaGFuZGxlU2F2ZSgpIDogc2V0RWRpdGluZyh0cnVlKX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicHgtNCBweS0yIGJnLWluZGlnby02MDAgdGV4dC13aGl0ZSByb3VuZGVkLWxnIHRleHQtc20gZm9udC1tZWRpdW0gaG92ZXI6YmctaW5kaWdvLTcwMCB0cmFuc2l0aW9uLWNvbG9ycyBkaXNhYmxlZDpvcGFjaXR5LTUwXCJcbiAgICAgICAgICAgICAgZGlzYWJsZWQ9e3NhdmluZ31cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAge3NhdmluZyA/IChcbiAgICAgICAgICAgICAgICA8PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhbmltYXRlLXNwaW4gaC00IHctNCBib3JkZXItMiBib3JkZXItd2hpdGUgYm9yZGVyLXQtdHJhbnNwYXJlbnQgcm91bmRlZC1mdWxsIG1yLTIgaW5saW5lLWJsb2NrXCI+PC9kaXY+XG4gICAgICAgICAgICAgICAgICBTYXZpbmcuLi5cbiAgICAgICAgICAgICAgICA8Lz5cbiAgICAgICAgICAgICAgKSA6IGVkaXRpbmcgPyAnU2F2ZSBDaGFuZ2VzJyA6ICdFZGl0IFByb2ZpbGUnfVxuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIH1cbiAgICAgIC8+XG5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctNHhsIG14LWF1dG8gcC00IGxnOnAtOFwiPlxuICAgICAgICB7LyogTWVzc2FnZSAqL31cbiAgICAgICAge21lc3NhZ2UudGV4dCAmJiAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2BtYi02IHAtNCByb3VuZGVkLWxnICR7XG4gICAgICAgICAgICBtZXNzYWdlLnR5cGUgPT09ICdzdWNjZXNzJ1xuICAgICAgICAgICAgICA/ICdiZy1ncmVlbi01MCBib3JkZXIgYm9yZGVyLWdyZWVuLTIwMCB0ZXh0LWdyZWVuLTgwMCdcbiAgICAgICAgICAgICAgOiAnYmctcmVkLTUwIGJvcmRlciBib3JkZXItcmVkLTIwMCB0ZXh0LXJlZC04MDAnXG4gICAgICAgICAgfWB9PlxuICAgICAgICAgICAge21lc3NhZ2UudGV4dH1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKX1cblxuICAgICAgICB7LyogUHJvZmlsZSBIZWFkZXIgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgcm91bmRlZC14bCBzaGFkb3ctc20gYm9yZGVyIGJvcmRlci1ncmF5LTIwMCBwLTYgbWItNlwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBzbTpmbGV4LXJvdyBpdGVtcy1jZW50ZXIgc3BhY2UteS00IHNtOnNwYWNlLXktMCBzbTpzcGFjZS14LTZcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0yNCBoLTI0IHJvdW5kZWQtZnVsbCBvdmVyZmxvdy1oaWRkZW4gYm9yZGVyLTIgYm9yZGVyLWdyYXktMjAwIGJnLWdyYWRpZW50LXRvLWJyIGZyb20taW5kaWdvLTUwMCB0by1wdXJwbGUtNjAwIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICAgIHtsb2dvUHJldmlldyA/IChcbiAgICAgICAgICAgICAgICA8aW1nXG4gICAgICAgICAgICAgICAgICBzcmM9e2xvZ29QcmV2aWV3fVxuICAgICAgICAgICAgICAgICAgYWx0PVwiQnVzaW5lc3MgTG9nb1wiXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgaC1mdWxsIG9iamVjdC1jb3ZlclwiXG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIHRleHQtM3hsIGZvbnQtYm9sZFwiPlxuICAgICAgICAgICAgICAgICAge3VzZXIubmFtZS5jaGFyQXQoMCkudG9VcHBlckNhc2UoKX1cbiAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICl9XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgc206dGV4dC1sZWZ0XCI+XG4gICAgICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBsZzp0ZXh0LTN4bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMFwiPnt1c2VyLm5hbWV9PC9oMT5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTgwMCBtdC0xXCI+e3VzZXIuZW1haWx9PC9wPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgc206ZmxleC1yb3cgaXRlbXMtY2VudGVyIHNtOml0ZW1zLXN0YXJ0IHNwYWNlLXktMiBzbTpzcGFjZS15LTAgc206c3BhY2UteC0zIG10LTJcIj5cbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9e2BpbmxpbmUtZmxleCBweC0yIHB5LTEgdGV4dC14cyBmb250LXNlbWlib2xkIHJvdW5kZWQtZnVsbCAke3VzZXIuaXNFbWFpbFZlcmlmaWVkID8gJ2JnLWdyZWVuLTEwMCB0ZXh0LWdyZWVuLTgwMCcgOiAnYmcteWVsbG93LTEwMCB0ZXh0LXllbGxvdy04MDAnfWB9PlxuICAgICAgICAgICAgICAgICAge3VzZXIuaXNFbWFpbFZlcmlmaWVkID8gJ+KckyBFbWFpbCBWZXJpZmllZCcgOiAn4pqgIEVtYWlsIE5vdCBWZXJpZmllZCd9XG4gICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgIHshdXNlci5pc0VtYWlsVmVyaWZpZWQgJiYgKFxuICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXtzZW5kVmVyaWZpY2F0aW9uRW1haWx9XG4gICAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXt2ZXJpZmljYXRpb25Mb2FkaW5nfVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIgcHgtMyBweS0xIHRleHQteHMgZm9udC1tZWRpdW0gdGV4dC1pbmRpZ28tNjAwIGJnLWluZGlnby01MCBib3JkZXIgYm9yZGVyLWluZGlnby0yMDAgcm91bmRlZC1mdWxsIGhvdmVyOmJnLWluZGlnby0xMDAgdHJhbnNpdGlvbi1jb2xvcnMgZGlzYWJsZWQ6b3BhY2l0eS01MFwiXG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIHt2ZXJpZmljYXRpb25Mb2FkaW5nID8gKFxuICAgICAgICAgICAgICAgICAgICAgIDw+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFuaW1hdGUtc3BpbiBoLTMgdy0zIGJvcmRlciBib3JkZXItaW5kaWdvLTYwMCBib3JkZXItdC10cmFuc3BhcmVudCByb3VuZGVkLWZ1bGwgbXItMVwiPjwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgU2VuZGluZy4uLlxuICAgICAgICAgICAgICAgICAgICAgIDwvPlxuICAgICAgICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgICAgICAgIDw+XG4gICAgICAgICAgICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctMyBoLTMgbXItMVwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgc3Ryb2tlV2lkdGg9ezJ9IGQ9XCJNMyA4bDcuODkgNC4yNmEyIDIgMCAwMDIuMjIgMEwyMSA4TTUgMTloMTRhMiAyIDAgMDAyLTJWN2EyIDIgMCAwMC0yLTJINWEyIDIgMCAwMC0yIDJ2MTBhMiAyIDAgMDAyIDJ6XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgICAgICAgICAgICAgVmVyaWZ5IEVtYWlsXG4gICAgICAgICAgICAgICAgICAgICAgPC8+XG4gICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAge3ZlcmlmaWNhdGlvbk1lc3NhZ2UgJiYgKFxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgbXQtMiBwLTIgcm91bmRlZC1tZCB0ZXh0LXhzICR7XG4gICAgICAgICAgICAgICAgICB2ZXJpZmljYXRpb25NZXNzYWdlLmluY2x1ZGVzKCdzZW50JykgfHwgdmVyaWZpY2F0aW9uTWVzc2FnZS5pbmNsdWRlcygnZGV2ZWxvcG1lbnRfbGluaycpXG4gICAgICAgICAgICAgICAgICAgID8gJ2JnLWdyZWVuLTUwIHRleHQtZ3JlZW4tNzAwIGJvcmRlciBib3JkZXItZ3JlZW4tMjAwJ1xuICAgICAgICAgICAgICAgICAgICA6ICdiZy1yZWQtNTAgdGV4dC1yZWQtNzAwIGJvcmRlciBib3JkZXItcmVkLTIwMCdcbiAgICAgICAgICAgICAgICB9YH0+XG4gICAgICAgICAgICAgICAgICB7dmVyaWZpY2F0aW9uTWVzc2FnZS5zdGFydHNXaXRoKCdkZXZlbG9wbWVudF9saW5rOicpID8gKFxuICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cIm1iLTJcIj7wn5OnIEVtYWlsIHNlcnZpY2Ugbm90IGNvbmZpZ3VyZWQgKERldmVsb3BtZW50IE1vZGUpPC9wPlxuICAgICAgICAgICAgICAgICAgICAgIDxhXG4gICAgICAgICAgICAgICAgICAgICAgICBocmVmPXt2ZXJpZmljYXRpb25NZXNzYWdlLnJlcGxhY2UoJ2RldmVsb3BtZW50X2xpbms6JywgJycpfVxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIHB4LTMgcHktMSBiZy1ncmVlbi02MDAgdGV4dC13aGl0ZSB0ZXh0LXhzIHJvdW5kZWQtbWQgaG92ZXI6YmctZ3JlZW4tNzAwIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIHRhcmdldD1cIl9ibGFua1wiXG4gICAgICAgICAgICAgICAgICAgICAgICByZWw9XCJub29wZW5lciBub3JlZmVycmVyXCJcbiAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctMyBoLTMgbXItMVwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgc3Ryb2tlV2lkdGg9ezJ9IGQ9XCJNNSAxM2w0IDRMMTkgN1wiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgICAgICAgICAgIENsaWNrIHRvIFZlcmlmeSBFbWFpbFxuICAgICAgICAgICAgICAgICAgICAgIDwvYT5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgICAgICB2ZXJpZmljYXRpb25NZXNzYWdlXG4gICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBQcm9maWxlIEZvcm0gKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgcm91bmRlZC14bCBzaGFkb3ctc20gYm9yZGVyIGJvcmRlci1ncmF5LTIwMCBwLTZcIj5cbiAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDAgbWItNlwiPlByb2ZpbGUgSW5mb3JtYXRpb248L2gyPlxuICAgICAgICAgIFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBnYXAtNlwiPlxuICAgICAgICAgICAgey8qIFBlcnNvbmFsIEluZm9ybWF0aW9uICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMCBib3JkZXItYiBib3JkZXItZ3JheS0yMDAgcGItMlwiPlxuICAgICAgICAgICAgICAgIFBlcnNvbmFsIEluZm9ybWF0aW9uXG4gICAgICAgICAgICAgIDwvaDM+XG4gICAgICAgICAgICAgIFxuICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS04MDAgbWItMlwiPkZ1bGwgTmFtZTwvbGFiZWw+XG4gICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgICBuYW1lPVwibmFtZVwiXG4gICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEubmFtZSB8fCAnJ31cbiAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVJbnB1dENoYW5nZX1cbiAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXshZWRpdGluZ31cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHctZnVsbCBweC0zIHB5LTIgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLW1kIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLWluZGlnby01MDAgZm9jdXM6Ym9yZGVyLWluZGlnby01MDAgdGV4dC1ncmF5LTkwMCAkeyFlZGl0aW5nID8gJ2JnLWdyYXktNTAnIDogJyd9YH1cbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS04MDAgbWItMlwiPkVtYWlsIEFkZHJlc3M8L2xhYmVsPlxuICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgdHlwZT1cImVtYWlsXCJcbiAgICAgICAgICAgICAgICAgIG5hbWU9XCJlbWFpbFwiXG4gICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEuZW1haWwgfHwgJyd9XG4gICAgICAgICAgICAgICAgICBvbkNoYW5nZT17aGFuZGxlSW5wdXRDaGFuZ2V9XG4gICAgICAgICAgICAgICAgICBkaXNhYmxlZD17IWVkaXRpbmd9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2B3LWZ1bGwgcHgtMyBweS0yIGJvcmRlciBib3JkZXItZ3JheS0zMDAgcm91bmRlZC1tZCBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy1pbmRpZ28tNTAwIGZvY3VzOmJvcmRlci1pbmRpZ28tNTAwIHRleHQtZ3JheS05MDAgJHshZWRpdGluZyA/ICdiZy1ncmF5LTUwJyA6ICcnfWB9XG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktODAwIG1iLTJcIj5QaG9uZSBOdW1iZXI8L2xhYmVsPlxuICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgdHlwZT1cInRlbFwiXG4gICAgICAgICAgICAgICAgICBuYW1lPVwicGhvbmVcIlxuICAgICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLnBob25lIHx8ICcnfVxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9e2hhbmRsZUlucHV0Q2hhbmdlfVxuICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9eyFlZGl0aW5nfVxuICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJFbnRlciB5b3VyIHBob25lIG51bWJlclwiXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2B3LWZ1bGwgcHgtMyBweS0yIGJvcmRlciBib3JkZXItZ3JheS0zMDAgcm91bmRlZC1tZCBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy1pbmRpZ28tNTAwIGZvY3VzOmJvcmRlci1pbmRpZ28tNTAwIHRleHQtZ3JheS05MDAgJHshZWRpdGluZyA/ICdiZy1ncmF5LTUwJyA6ICcnfWB9XG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qIEJ1c2luZXNzIEluZm9ybWF0aW9uICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMCBib3JkZXItYiBib3JkZXItZ3JheS0yMDAgcGItMlwiPlxuICAgICAgICAgICAgICAgIEJ1c2luZXNzIEluZm9ybWF0aW9uXG4gICAgICAgICAgICAgIDwvaDM+XG4gICAgICAgICAgICAgIFxuICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS04MDAgbWItMlwiPkJ1c2luZXNzIE5hbWU8L2xhYmVsPlxuICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgICAgICAgbmFtZT1cImJ1c2luZXNzTmFtZVwiXG4gICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEuYnVzaW5lc3NOYW1lIHx8ICcnfVxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9e2hhbmRsZUlucHV0Q2hhbmdlfVxuICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9eyFlZGl0aW5nfVxuICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJFbnRlciB5b3VyIGJ1c2luZXNzIG5hbWVcIlxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgdy1mdWxsIHB4LTMgcHktMiBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbWQgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctaW5kaWdvLTUwMCBmb2N1czpib3JkZXItaW5kaWdvLTUwMCB0ZXh0LWdyYXktOTAwICR7IWVkaXRpbmcgPyAnYmctZ3JheS01MCcgOiAnJ31gfVxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTgwMCBtYi0yXCI+R1NUIE51bWJlcjwvbGFiZWw+XG4gICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgICBuYW1lPVwiZ3N0TnVtYmVyXCJcbiAgICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5nc3ROdW1iZXIgfHwgJyd9XG4gICAgICAgICAgICAgICAgICBvbkNoYW5nZT17aGFuZGxlSW5wdXRDaGFuZ2V9XG4gICAgICAgICAgICAgICAgICBkaXNhYmxlZD17IWVkaXRpbmd9XG4gICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkVudGVyIEdTVCBudW1iZXIgKG9wdGlvbmFsKVwiXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2B3LWZ1bGwgcHgtMyBweS0yIGJvcmRlciBib3JkZXItZ3JheS0zMDAgcm91bmRlZC1tZCBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy1pbmRpZ28tNTAwIGZvY3VzOmJvcmRlci1pbmRpZ28tNTAwIHRleHQtZ3JheS05MDAgJHshZWRpdGluZyA/ICdiZy1ncmF5LTUwJyA6ICcnfWB9XG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgey8qIEJ1c2luZXNzIExvZ28gKi99XG4gICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTgwMCBtYi0yXCI+QnVzaW5lc3MgTG9nbzwvbGFiZWw+XG4gICAgICAgICAgICAgICAge2xvZ29QcmV2aWV3ID8gKFxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTNcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTMyIGgtMzIgYm9yZGVyLTIgYm9yZGVyLWdyYXktMjAwIHJvdW5kZWQtbGcgb3ZlcmZsb3ctaGlkZGVuIGJnLWdyYXktNTAgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8aW1nXG4gICAgICAgICAgICAgICAgICAgICAgICBzcmM9e2xvZ29QcmV2aWV3fVxuICAgICAgICAgICAgICAgICAgICAgICAgYWx0PVwiQnVzaW5lc3MgTG9nb1wiXG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgaC1mdWxsIG9iamVjdC1jb250YWluXCJcbiAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAge2VkaXRpbmcgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxGaWxlVXBsb2FkXG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9uVXBsb2FkPXtoYW5kbGVMb2dvVXBsb2FkfVxuICAgICAgICAgICAgICAgICAgICAgICAgICBhY2NlcHQ9XCIuanBnLC5qcGVnLC5wbmcsLmdpZiwuc3ZnLC53ZWJwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgbWF4U2l6ZT17NX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgbWF4RmlsZXM9ezF9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXgtMVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVSZW1vdmVMb2dvfVxuICAgICAgICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZD17bG9nb1VwbG9hZGluZ31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicHgtMyBweS0yIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1yZWQtNjAwIGJnLXJlZC01MCBib3JkZXIgYm9yZGVyLXJlZC0yMDAgcm91bmRlZC1tZCBob3ZlcjpiZy1yZWQtMTAwIHRyYW5zaXRpb24tY29sb3JzIGRpc2FibGVkOm9wYWNpdHktNTBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7bG9nb1VwbG9hZGluZyA/ICdSZW1vdmluZy4uLicgOiAnUmVtb3ZlJ31cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0zXCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0zMiBoLTMyIGJvcmRlci0yIGJvcmRlci1kYXNoZWQgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbGcgYmctZ3JheS01MCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy04IGgtOCB0ZXh0LWdyYXktNDAwIG14LWF1dG8gbWItMlwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgc3Ryb2tlV2lkdGg9ezJ9IGQ9XCJNNCAxNmw0LjU4Ni00LjU4NmEyIDIgMCAwMTIuODI4IDBMMTYgMTZtLTItMmwxLjU4Ni0xLjU4NmEyIDIgMCAwMTIuODI4IDBMMjAgMTRtLTYtNmguMDFNNiAyMGgxMmEyIDIgMCAwMDItMlY2YTIgMiAwIDAwLTItMkg2YTIgMiAwIDAwLTIgMnYxMmEyIDIgMCAwMDIgMnpcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS01MDBcIj5ObyBsb2dvPC9wPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAge2VkaXRpbmcgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgIDxGaWxlVXBsb2FkXG4gICAgICAgICAgICAgICAgICAgICAgICBvblVwbG9hZD17aGFuZGxlTG9nb1VwbG9hZH1cbiAgICAgICAgICAgICAgICAgICAgICAgIGFjY2VwdD1cIi5qcGcsLmpwZWcsLnBuZywuZ2lmLC5zdmcsLndlYnBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgbWF4U2l6ZT17NX1cbiAgICAgICAgICAgICAgICAgICAgICAgIG1heEZpbGVzPXsxfVxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsXCJcbiAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICB7bG9nb1VwbG9hZGluZyAmJiAoXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTIgZmxleCBpdGVtcy1jZW50ZXIgdGV4dC1zbSB0ZXh0LWdyYXktNjAwXCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYW5pbWF0ZS1zcGluIGgtNCB3LTQgYm9yZGVyLTIgYm9yZGVyLWluZGlnby02MDAgYm9yZGVyLXQtdHJhbnNwYXJlbnQgcm91bmRlZC1mdWxsIG1yLTJcIj48L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgVXBsb2FkaW5nIGxvZ28uLi5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogQWRkcmVzcyBJbmZvcm1hdGlvbiAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LThcIj5cbiAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDAgYm9yZGVyLWIgYm9yZGVyLWdyYXktMjAwIHBiLTIgbWItNFwiPlxuICAgICAgICAgICAgICBBZGRyZXNzIEluZm9ybWF0aW9uXG4gICAgICAgICAgICA8L2gzPlxuICAgICAgICAgICAgXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgZ2FwLTZcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtZDpjb2wtc3Bhbi0yXCI+XG4gICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTgwMCBtYi0yXCI+U3RyZWV0IEFkZHJlc3M8L2xhYmVsPlxuICAgICAgICAgICAgICAgIDx0ZXh0YXJlYVxuICAgICAgICAgICAgICAgICAgbmFtZT1cInN0cmVldFwiXG4gICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEuYWRkcmVzcz8uc3RyZWV0IHx8ICcnfVxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9e2hhbmRsZUlucHV0Q2hhbmdlfVxuICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9eyFlZGl0aW5nfVxuICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJFbnRlciB5b3VyIHN0cmVldCBhZGRyZXNzXCJcbiAgICAgICAgICAgICAgICAgIHJvd3M9ezN9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2B3LWZ1bGwgcHgtMyBweS0yIGJvcmRlciBib3JkZXItZ3JheS0zMDAgcm91bmRlZC1tZCBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy1pbmRpZ28tNTAwIGZvY3VzOmJvcmRlci1pbmRpZ28tNTAwIHRleHQtZ3JheS05MDAgJHshZWRpdGluZyA/ICdiZy1ncmF5LTUwJyA6ICcnfWB9XG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktODAwIG1iLTJcIj5DaXR5PC9sYWJlbD5cbiAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgICAgICAgIG5hbWU9XCJjaXR5XCJcbiAgICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5hZGRyZXNzPy5jaXR5IHx8ICcnfVxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9e2hhbmRsZUlucHV0Q2hhbmdlfVxuICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9eyFlZGl0aW5nfVxuICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJFbnRlciBjaXR5XCJcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHctZnVsbCBweC0zIHB5LTIgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLW1kIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLWluZGlnby01MDAgZm9jdXM6Ym9yZGVyLWluZGlnby01MDAgdGV4dC1ncmF5LTkwMCAkeyFlZGl0aW5nID8gJ2JnLWdyYXktNTAnIDogJyd9YH1cbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS04MDAgbWItMlwiPlN0YXRlPC9sYWJlbD5cbiAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgICAgICAgIG5hbWU9XCJzdGF0ZVwiXG4gICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEuYWRkcmVzcz8uc3RhdGUgfHwgJyd9XG4gICAgICAgICAgICAgICAgICBvbkNoYW5nZT17aGFuZGxlSW5wdXRDaGFuZ2V9XG4gICAgICAgICAgICAgICAgICBkaXNhYmxlZD17IWVkaXRpbmd9XG4gICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkVudGVyIHN0YXRlXCJcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHctZnVsbCBweC0zIHB5LTIgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLW1kIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLWluZGlnby01MDAgZm9jdXM6Ym9yZGVyLWluZGlnby01MDAgdGV4dC1ncmF5LTkwMCAkeyFlZGl0aW5nID8gJ2JnLWdyYXktNTAnIDogJyd9YH1cbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS04MDAgbWItMlwiPlBJTiBDb2RlPC9sYWJlbD5cbiAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgICAgICAgIG5hbWU9XCJwaW5jb2RlXCJcbiAgICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5hZGRyZXNzPy5waW5jb2RlIHx8ICcnfVxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9e2hhbmRsZUlucHV0Q2hhbmdlfVxuICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9eyFlZGl0aW5nfVxuICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJFbnRlciBQSU4gY29kZVwiXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2B3LWZ1bGwgcHgtMyBweS0yIGJvcmRlciBib3JkZXItZ3JheS0zMDAgcm91bmRlZC1tZCBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy1pbmRpZ28tNTAwIGZvY3VzOmJvcmRlci1pbmRpZ28tNTAwIHRleHQtZ3JheS05MDAgJHshZWRpdGluZyA/ICdiZy1ncmF5LTUwJyA6ICcnfWB9XG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktODAwIG1iLTJcIj5Db3VudHJ5PC9sYWJlbD5cbiAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgICAgICAgIG5hbWU9XCJjb3VudHJ5XCJcbiAgICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5hZGRyZXNzPy5jb3VudHJ5IHx8ICdJbmRpYSd9XG4gICAgICAgICAgICAgICAgICBvbkNoYW5nZT17aGFuZGxlSW5wdXRDaGFuZ2V9XG4gICAgICAgICAgICAgICAgICBkaXNhYmxlZD17IWVkaXRpbmd9XG4gICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkVudGVyIGNvdW50cnlcIlxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgdy1mdWxsIHB4LTMgcHktMiBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbWQgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctaW5kaWdvLTUwMCBmb2N1czpib3JkZXItaW5kaWdvLTUwMCB0ZXh0LWdyYXktOTAwICR7IWVkaXRpbmcgPyAnYmctZ3JheS01MCcgOiAnJ31gfVxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogTW9iaWxlIFNhdmUgQnV0dG9uICovfVxuICAgICAgICAgIHtlZGl0aW5nICYmIChcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibGc6aGlkZGVuIG10LTYgZmxleCBzcGFjZS14LTNcIj5cbiAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZUNhbmNlbH1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4LTEgcHgtNCBweS0yIGJvcmRlciBib3JkZXItZ3JheS0zMDAgcm91bmRlZC1sZyB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS04MDAgYmctd2hpdGUgaG92ZXI6YmctZ3JheS01MCB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICAgICAgZGlzYWJsZWQ9e3NhdmluZ31cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIENhbmNlbFxuICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZVNhdmV9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleC0xIHB4LTQgcHktMiBiZy1pbmRpZ28tNjAwIHRleHQtd2hpdGUgcm91bmRlZC1sZyB0ZXh0LXNtIGZvbnQtbWVkaXVtIGhvdmVyOmJnLWluZGlnby03MDAgdHJhbnNpdGlvbi1jb2xvcnMgZGlzYWJsZWQ6b3BhY2l0eS01MFwiXG4gICAgICAgICAgICAgICAgZGlzYWJsZWQ9e3NhdmluZ31cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIHtzYXZpbmcgPyAoXG4gICAgICAgICAgICAgICAgICA8PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFuaW1hdGUtc3BpbiBoLTQgdy00IGJvcmRlci0yIGJvcmRlci13aGl0ZSBib3JkZXItdC10cmFuc3BhcmVudCByb3VuZGVkLWZ1bGwgbXItMiBpbmxpbmUtYmxvY2tcIj48L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgU2F2aW5nLi4uXG4gICAgICAgICAgICAgICAgICA8Lz5cbiAgICAgICAgICAgICAgICApIDogJ1NhdmUgQ2hhbmdlcyd9XG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKX1cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIEFjY291bnQgU3RhdHMgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgcm91bmRlZC14bCBzaGFkb3ctc20gYm9yZGVyIGJvcmRlci1ncmF5LTIwMCBwLTYgbXQtNlwiPlxuICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDAgbWItNFwiPkFjY291bnQgSW5mb3JtYXRpb248L2gzPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBzbTpncmlkLWNvbHMtMiBnYXAtNFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlbiBpdGVtcy1jZW50ZXIgcHktMiBib3JkZXItYiBib3JkZXItZ3JheS0xMDBcIj5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktODAwXCI+QWNjb3VudCBUeXBlPC9zcGFuPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS05MDAgY2FwaXRhbGl6ZVwiPnt1c2VyLnJvbGV9PC9zcGFuPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLWNlbnRlciBweS0yIGJvcmRlci1iIGJvcmRlci1ncmF5LTEwMFwiPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS04MDBcIj5NZW1iZXIgU2luY2U8L3NwYW4+XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTkwMFwiPlxuICAgICAgICAgICAgICAgIHt1c2VyLmNyZWF0ZWRBdCA/IG5ldyBEYXRlKHVzZXIuY3JlYXRlZEF0KS50b0xvY2FsZURhdGVTdHJpbmcoJ2VuLUlOJykgOiAnTi9BJ31cbiAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLWNlbnRlciBweS0yIGJvcmRlci1iIGJvcmRlci1ncmF5LTEwMFwiPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS04MDBcIj5MYXN0IExvZ2luPC9zcGFuPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS05MDBcIj5cbiAgICAgICAgICAgICAgICB7dXNlci5sYXN0TG9naW4gPyBuZXcgRGF0ZSh1c2VyLmxhc3RMb2dpbikudG9Mb2NhbGVEYXRlU3RyaW5nKCdlbi1JTicpIDogJ04vQSd9XG4gICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlbiBpdGVtcy1jZW50ZXIgcHktMiBib3JkZXItYiBib3JkZXItZ3JheS0xMDBcIj5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktODAwXCI+RW1haWwgU3RhdHVzPC9zcGFuPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT17YGlubGluZS1mbGV4IHB4LTIgcHktMSB0ZXh0LXhzIGZvbnQtc2VtaWJvbGQgcm91bmRlZC1mdWxsICR7dXNlci5pc0VtYWlsVmVyaWZpZWQgPyAnYmctZ3JlZW4tMTAwIHRleHQtZ3JlZW4tODAwJyA6ICdiZy15ZWxsb3ctMTAwIHRleHQteWVsbG93LTgwMCd9YH0+XG4gICAgICAgICAgICAgICAgICB7dXNlci5pc0VtYWlsVmVyaWZpZWQgPyAnVmVyaWZpZWQnIDogJ05vdCBWZXJpZmllZCd9XG4gICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgIHshdXNlci5pc0VtYWlsVmVyaWZpZWQgJiYgKFxuICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXtzZW5kVmVyaWZpY2F0aW9uRW1haWx9XG4gICAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXt2ZXJpZmljYXRpb25Mb2FkaW5nfVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtaW5kaWdvLTYwMCBob3Zlcjp0ZXh0LWluZGlnby04MDAgZm9udC1tZWRpdW0gZGlzYWJsZWQ6b3BhY2l0eS01MFwiXG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIHt2ZXJpZmljYXRpb25Mb2FkaW5nID8gJ1NlbmRpbmcuLi4nIDogJ1NlbmQgVmVyaWZpY2F0aW9uJ31cbiAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgPC9EYXNoYm9hcmRMYXlvdXQ+XG4gICk7XG59XG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJ1c2VBdXRoIiwidXBkYXRlUHJvZmlsZSIsImF1dGhlbnRpY2F0ZWRGZXRjaCIsIkRhc2hib2FyZExheW91dCIsIkRhc2hib2FyZEhlYWRlciIsIkZpbGVVcGxvYWQiLCJQcm9maWxlUGFnZSIsImZvcm1EYXRhIiwidXNlciIsInVwZGF0ZVVzZXIiLCJlZGl0aW5nIiwic2V0RWRpdGluZyIsInNhdmluZyIsInNldFNhdmluZyIsIm1lc3NhZ2UiLCJzZXRNZXNzYWdlIiwidHlwZSIsInRleHQiLCJzZXRGb3JtRGF0YSIsInZlcmlmaWNhdGlvbkxvYWRpbmciLCJzZXRWZXJpZmljYXRpb25Mb2FkaW5nIiwidmVyaWZpY2F0aW9uTWVzc2FnZSIsInNldFZlcmlmaWNhdGlvbk1lc3NhZ2UiLCJsb2dvVXBsb2FkaW5nIiwic2V0TG9nb1VwbG9hZGluZyIsImxvZ29QcmV2aWV3Iiwic2V0TG9nb1ByZXZpZXciLCJzaWduYXR1cmVVcGxvYWRpbmciLCJzZXRTaWduYXR1cmVVcGxvYWRpbmciLCJzaWduYXR1cmVQcmV2aWV3Iiwic2V0U2lnbmF0dXJlUHJldmlldyIsImFkZHJlc3MiLCJzdHJlZXQiLCJjaXR5Iiwic3RhdGUiLCJwaW5jb2RlIiwiY291bnRyeSIsImxvZ28iLCJsb2dvUGF0aCIsImluY2x1ZGVzIiwic2lnbmF0dXJlIiwic2lnbmF0dXJlUGF0aCIsImhhbmRsZUlucHV0Q2hhbmdlIiwiZSIsIm5hbWUiLCJ2YWx1ZSIsInRhcmdldCIsInByZXYiLCJoYW5kbGVMb2dvVXBsb2FkIiwiZmlsZXMiLCJsZW5ndGgiLCJmaWxlIiwiRm9ybURhdGEiLCJhcHBlbmQiLCJ0b2tlbiIsImxvY2FsU3RvcmFnZSIsImdldEl0ZW0iLCJyZXNwb25zZSIsImZldGNoIiwibWV0aG9kIiwiaGVhZGVycyIsImJvZHkiLCJkYXRhIiwianNvbiIsInN1Y2Nlc3MiLCJlcnJvciIsImNvbnNvbGUiLCJoYW5kbGVSZW1vdmVMb2dvIiwidW5kZWZpbmVkIiwiaGFuZGxlU2F2ZSIsImNsZWFuRm9ybURhdGEiLCJ0cmltIiwiYnVzaW5lc3NOYW1lIiwiZ3N0TnVtYmVyIiwicGhvbmUiLCJPYmplY3QiLCJrZXlzIiwibG9nIiwiZXJyb3JNZXNzYWdlIiwiZXJyb3JzIiwiam9pbiIsImhhbmRsZUNhbmNlbCIsInNlbmRWZXJpZmljYXRpb25FbWFpbCIsImRldmVsb3BtZW50VG9rZW4iLCJ2ZXJpZmljYXRpb25VcmwiLCJ3aW5kb3ciLCJsb2NhdGlvbiIsIm9yaWdpbiIsInRpdGxlIiwic3VidGl0bGUiLCJhY3Rpb25zIiwiZGl2IiwiY2xhc3NOYW1lIiwiYnV0dG9uIiwib25DbGljayIsImRpc2FibGVkIiwiaW1nIiwic3JjIiwiYWx0Iiwic3BhbiIsImNoYXJBdCIsInRvVXBwZXJDYXNlIiwiaDEiLCJwIiwiZW1haWwiLCJpc0VtYWlsVmVyaWZpZWQiLCJzdmciLCJmaWxsIiwic3Ryb2tlIiwidmlld0JveCIsInBhdGgiLCJzdHJva2VMaW5lY2FwIiwic3Ryb2tlTGluZWpvaW4iLCJzdHJva2VXaWR0aCIsImQiLCJzdGFydHNXaXRoIiwiYSIsImhyZWYiLCJyZXBsYWNlIiwicmVsIiwiaDIiLCJoMyIsImxhYmVsIiwiaW5wdXQiLCJvbkNoYW5nZSIsInBsYWNlaG9sZGVyIiwib25VcGxvYWQiLCJhY2NlcHQiLCJtYXhTaXplIiwibWF4RmlsZXMiLCJ0ZXh0YXJlYSIsInJvd3MiLCJyb2xlIiwiY3JlYXRlZEF0IiwiRGF0ZSIsInRvTG9jYWxlRGF0ZVN0cmluZyIsImxhc3RMb2dpbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/profile/page.tsx\n"));

/***/ })

});