/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/_not-found/page";
exports.ids = ["app/_not-found/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5Cgupta%5CDesktop%5CinvoNest%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cgupta%5CDesktop%5CinvoNest%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5Cgupta%5CDesktop%5CinvoNest%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cgupta%5CDesktop%5CinvoNest%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9100\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n          children: [\"/_not-found\", {\n            children: ['__PAGE__', {}, {\n              page: [\n                () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)),\n                \"next/dist/client/components/not-found-error\"\n              ]\n            }]\n          }, {}]\n        },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/_not-found/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/_not-found/page\",\n        pathname: \"/_not-found\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5Cgupta%5CDesktop%5CinvoNest%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cgupta%5CDesktop%5CinvoNest%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/react-hot-toast/dist/index.mjs */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AuthContext.tsx */ \"(ssr)/./src/contexts/AuthContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth),\n/* harmony export */   useRequireAuth: () => (/* binding */ useRequireAuth),\n/* harmony export */   withAuth: () => (/* binding */ withAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../lib/auth */ \"(ssr)/./src/lib/auth.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth,withAuth,useRequireAuth auto */ \n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst AuthProvider = ({ children })=>{\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // Initialize auth state\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initAuth = async ()=>{\n            try {\n                if ((0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.isAuthenticated)()) {\n                    // Try to get user from localStorage first\n                    const cachedUser = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.getCurrentUser)();\n                    if (cachedUser) {\n                        setUser(cachedUser);\n                    }\n                    // Then refresh from server\n                    const freshUser = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.getProfile)();\n                    if (freshUser) {\n                        setUser(freshUser);\n                    } else {\n                        // Token might be expired, clear auth state\n                        (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.removeTokens)();\n                        setUser(null);\n                    }\n                }\n            } catch (error) {\n                console.error(\"Auth initialization error:\", error);\n                (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.removeTokens)();\n                setUser(null);\n            } finally{\n                setLoading(false);\n            }\n        };\n        initAuth();\n    }, []);\n    const login = async (data, remember = false)=>{\n        try {\n            const response = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.login)(data);\n            if (response.success && response.data) {\n                (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.setTokens)(response.data.token, response.data.refreshToken, remember);\n                (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.setCurrentUser)(response.data.user);\n                setUser(response.data.user);\n            }\n            return response;\n        } catch (error) {\n            console.error(\"Login error:\", error);\n            return {\n                success: false,\n                message: \"An unexpected error occurred during login.\"\n            };\n        }\n    };\n    const register = async (data)=>{\n        try {\n            const response = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.register)(data);\n            if (response.success && response.data) {\n                (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.setTokens)(response.data.token, response.data.refreshToken, true);\n                (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.setCurrentUser)(response.data.user);\n                setUser(response.data.user);\n            }\n            return response;\n        } catch (error) {\n            console.error(\"Register error:\", error);\n            return {\n                success: false,\n                message: \"An unexpected error occurred during registration.\"\n            };\n        }\n    };\n    const logout = async ()=>{\n        try {\n            await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.logout)();\n        } catch (error) {\n            console.error(\"Logout error:\", error);\n        } finally{\n            setUser(null);\n            router.push(\"/login\");\n        }\n    };\n    const updateUser = (updatedUser)=>{\n        setUser(updatedUser);\n        (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.setCurrentUser)(updatedUser);\n    };\n    const refreshUser = async ()=>{\n        try {\n            const freshUser = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.getProfile)();\n            if (freshUser) {\n                setUser(freshUser);\n            }\n        } catch (error) {\n            console.error(\"Refresh user error:\", error);\n        }\n    };\n    const value = {\n        user,\n        loading,\n        isLoggedIn: !!user,\n        login,\n        register,\n        logout,\n        updateUser,\n        refreshUser\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 155,\n        columnNumber: 5\n    }, undefined);\n};\nconst useAuth = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n};\n// Higher-order component for protected routes\nconst withAuth = (WrappedComponent)=>{\n    const AuthenticatedComponent = (props)=>{\n        const { isLoggedIn, loading } = useAuth();\n        const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n        (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n            if (!loading && !isLoggedIn) {\n                router.push(\"/login\");\n            }\n        }, [\n            isLoggedIn,\n            loading,\n            router\n        ]);\n        if (loading) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                    lineNumber: 186,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                lineNumber: 185,\n                columnNumber: 9\n            }, undefined);\n        }\n        if (!isLoggedIn) {\n            return null;\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WrappedComponent, {\n            ...props\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n            lineNumber: 195,\n            columnNumber: 12\n        }, undefined);\n    };\n    AuthenticatedComponent.displayName = `withAuth(${WrappedComponent.displayName || WrappedComponent.name})`;\n    return AuthenticatedComponent;\n};\n// Hook for protected routes\nconst useRequireAuth = ()=>{\n    const { isLoggedIn, loading } = useAuth();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!loading && !isLoggedIn) {\n            router.push(\"/login\");\n        }\n    }, [\n        isLoggedIn,\n        loading,\n        router\n    ]);\n    return {\n        isLoggedIn,\n        loading\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29udGV4dHMvQXV0aENvbnRleHQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBRXlGO0FBQzdDO0FBZXZCO0FBYXJCLE1BQU1rQiw0QkFBY2pCLG9EQUFhQSxDQUE4QmtCO0FBTXhELE1BQU1DLGVBQTRDLENBQUMsRUFBRUMsUUFBUSxFQUFFO0lBQ3BFLE1BQU0sQ0FBQ0MsTUFBTUMsUUFBUSxHQUFHbkIsK0NBQVFBLENBQWM7SUFDOUMsTUFBTSxDQUFDb0IsU0FBU0MsV0FBVyxHQUFHckIsK0NBQVFBLENBQUM7SUFDdkMsTUFBTXNCLFNBQVNyQiwwREFBU0E7SUFFeEIsd0JBQXdCO0lBQ3hCRixnREFBU0EsQ0FBQztRQUNSLE1BQU13QixXQUFXO1lBQ2YsSUFBSTtnQkFDRixJQUFJYiwwREFBZUEsSUFBSTtvQkFDckIsMENBQTBDO29CQUMxQyxNQUFNYyxhQUFhZix5REFBY0E7b0JBQ2pDLElBQUllLFlBQVk7d0JBQ2RMLFFBQVFLO29CQUNWO29CQUVBLDJCQUEyQjtvQkFDM0IsTUFBTUMsWUFBWSxNQUFNakIscURBQVVBO29CQUNsQyxJQUFJaUIsV0FBVzt3QkFDYk4sUUFBUU07b0JBQ1YsT0FBTzt3QkFDTCwyQ0FBMkM7d0JBQzNDZCx1REFBWUE7d0JBQ1pRLFFBQVE7b0JBQ1Y7Z0JBQ0Y7WUFDRixFQUFFLE9BQU9PLE9BQU87Z0JBQ2RDLFFBQVFELEtBQUssQ0FBQyw4QkFBOEJBO2dCQUM1Q2YsdURBQVlBO2dCQUNaUSxRQUFRO1lBQ1YsU0FBVTtnQkFDUkUsV0FBVztZQUNiO1FBQ0Y7UUFFQUU7SUFDRixHQUFHLEVBQUU7SUFFTCxNQUFNckIsUUFBUSxPQUFPMEIsTUFBaUJDLFdBQW9CLEtBQUs7UUFDN0QsSUFBSTtZQUNGLE1BQU1DLFdBQVcsTUFBTTNCLGdEQUFRQSxDQUFDeUI7WUFFaEMsSUFBSUUsU0FBU0MsT0FBTyxJQUFJRCxTQUFTRixJQUFJLEVBQUU7Z0JBQ3JDaEIsb0RBQVNBLENBQUNrQixTQUFTRixJQUFJLENBQUNJLEtBQUssRUFBRUYsU0FBU0YsSUFBSSxDQUFDSyxZQUFZLEVBQUVKO2dCQUMzRGhCLHlEQUFjQSxDQUFDaUIsU0FBU0YsSUFBSSxDQUFDVixJQUFJO2dCQUNqQ0MsUUFBUVcsU0FBU0YsSUFBSSxDQUFDVixJQUFJO1lBQzVCO1lBRUEsT0FBT1k7UUFDVCxFQUFFLE9BQU9KLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLGdCQUFnQkE7WUFDOUIsT0FBTztnQkFDTEssU0FBUztnQkFDVEcsU0FBUztZQUNYO1FBQ0Y7SUFDRjtJQUVBLE1BQU05QixXQUFXLE9BQU93QjtRQUN0QixJQUFJO1lBQ0YsTUFBTUUsV0FBVyxNQUFNekIsbURBQVdBLENBQUN1QjtZQUVuQyxJQUFJRSxTQUFTQyxPQUFPLElBQUlELFNBQVNGLElBQUksRUFBRTtnQkFDckNoQixvREFBU0EsQ0FBQ2tCLFNBQVNGLElBQUksQ0FBQ0ksS0FBSyxFQUFFRixTQUFTRixJQUFJLENBQUNLLFlBQVksRUFBRTtnQkFDM0RwQix5REFBY0EsQ0FBQ2lCLFNBQVNGLElBQUksQ0FBQ1YsSUFBSTtnQkFDakNDLFFBQVFXLFNBQVNGLElBQUksQ0FBQ1YsSUFBSTtZQUM1QjtZQUVBLE9BQU9ZO1FBQ1QsRUFBRSxPQUFPSixPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyxtQkFBbUJBO1lBQ2pDLE9BQU87Z0JBQ0xLLFNBQVM7Z0JBQ1RHLFNBQVM7WUFDWDtRQUNGO0lBQ0Y7SUFFQSxNQUFNNUIsU0FBUztRQUNiLElBQUk7WUFDRixNQUFNQyxpREFBU0E7UUFDakIsRUFBRSxPQUFPbUIsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsaUJBQWlCQTtRQUNqQyxTQUFVO1lBQ1JQLFFBQVE7WUFDUkcsT0FBT2EsSUFBSSxDQUFDO1FBQ2Q7SUFDRjtJQUVBLE1BQU1DLGFBQWEsQ0FBQ0M7UUFDbEJsQixRQUFRa0I7UUFDUnhCLHlEQUFjQSxDQUFDd0I7SUFDakI7SUFFQSxNQUFNQyxjQUFjO1FBQ2xCLElBQUk7WUFDRixNQUFNYixZQUFZLE1BQU1qQixxREFBVUE7WUFDbEMsSUFBSWlCLFdBQVc7Z0JBQ2JOLFFBQVFNO1lBQ1Y7UUFDRixFQUFFLE9BQU9DLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLHVCQUF1QkE7UUFDdkM7SUFDRjtJQUVBLE1BQU1hLFFBQXlCO1FBQzdCckI7UUFDQUU7UUFDQW9CLFlBQVksQ0FBQyxDQUFDdEI7UUFDZGhCO1FBQ0FFO1FBQ0FFO1FBQ0E4QjtRQUNBRTtJQUNGO0lBRUEscUJBQ0UsOERBQUN4QixZQUFZMkIsUUFBUTtRQUFDRixPQUFPQTtrQkFDMUJ0Qjs7Ozs7O0FBR1AsRUFBRTtBQUVLLE1BQU15QixVQUFVO0lBQ3JCLE1BQU1DLFVBQVU3QyxpREFBVUEsQ0FBQ2dCO0lBQzNCLElBQUk2QixZQUFZNUIsV0FBVztRQUN6QixNQUFNLElBQUk2QixNQUFNO0lBQ2xCO0lBQ0EsT0FBT0Q7QUFDVCxFQUFFO0FBRUYsOENBQThDO0FBQ3ZDLE1BQU1FLFdBQVcsQ0FDdEJDO0lBRUEsTUFBTUMseUJBQXNDLENBQUNDO1FBQzNDLE1BQU0sRUFBRVIsVUFBVSxFQUFFcEIsT0FBTyxFQUFFLEdBQUdzQjtRQUNoQyxNQUFNcEIsU0FBU3JCLDBEQUFTQTtRQUV4QkYsZ0RBQVNBLENBQUM7WUFDUixJQUFJLENBQUNxQixXQUFXLENBQUNvQixZQUFZO2dCQUMzQmxCLE9BQU9hLElBQUksQ0FBQztZQUNkO1FBQ0YsR0FBRztZQUFDSztZQUFZcEI7WUFBU0U7U0FBTztRQUVoQyxJQUFJRixTQUFTO1lBQ1gscUJBQ0UsOERBQUM2QjtnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ0Q7b0JBQUlDLFdBQVU7Ozs7Ozs7Ozs7O1FBR3JCO1FBRUEsSUFBSSxDQUFDVixZQUFZO1lBQ2YsT0FBTztRQUNUO1FBRUEscUJBQU8sOERBQUNNO1lBQWtCLEdBQUdFLEtBQUs7Ozs7OztJQUNwQztJQUVBRCx1QkFBdUJJLFdBQVcsR0FBRyxDQUFDLFNBQVMsRUFBRUwsaUJBQWlCSyxXQUFXLElBQUlMLGlCQUFpQk0sSUFBSSxDQUFDLENBQUMsQ0FBQztJQUV6RyxPQUFPTDtBQUNULEVBQUU7QUFFRiw0QkFBNEI7QUFDckIsTUFBTU0saUJBQWlCO0lBQzVCLE1BQU0sRUFBRWIsVUFBVSxFQUFFcEIsT0FBTyxFQUFFLEdBQUdzQjtJQUNoQyxNQUFNcEIsU0FBU3JCLDBEQUFTQTtJQUV4QkYsZ0RBQVNBLENBQUM7UUFDUixJQUFJLENBQUNxQixXQUFXLENBQUNvQixZQUFZO1lBQzNCbEIsT0FBT2EsSUFBSSxDQUFDO1FBQ2Q7SUFDRixHQUFHO1FBQUNLO1FBQVlwQjtRQUFTRTtLQUFPO0lBRWhDLE9BQU87UUFBRWtCO1FBQVlwQjtJQUFRO0FBQy9CLEVBQUUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mcm9udGVuZC8uL3NyYy9jb250ZXh0cy9BdXRoQ29udGV4dC50c3g/MWZhMiJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCBSZWFjdCwgeyBjcmVhdGVDb250ZXh0LCB1c2VDb250ZXh0LCB1c2VFZmZlY3QsIHVzZVN0YXRlLCBSZWFjdE5vZGUgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyB1c2VSb3V0ZXIgfSBmcm9tICduZXh0L25hdmlnYXRpb24nO1xuaW1wb3J0IHsgXG4gIFVzZXIsIFxuICBsb2dpbiBhcyBhcGlMb2dpbiwgXG4gIHJlZ2lzdGVyIGFzIGFwaVJlZ2lzdGVyLCBcbiAgbG9nb3V0IGFzIGFwaUxvZ291dCxcbiAgZ2V0UHJvZmlsZSxcbiAgZ2V0Q3VycmVudFVzZXIsXG4gIGlzQXV0aGVudGljYXRlZCxcbiAgcmVtb3ZlVG9rZW5zLFxuICBzZXRUb2tlbnMsXG4gIHNldEN1cnJlbnRVc2VyLFxuICBMb2dpbkRhdGEsXG4gIFJlZ2lzdGVyRGF0YSxcbiAgQXV0aFJlc3BvbnNlXG59IGZyb20gJy4uL2xpYi9hdXRoJztcblxuaW50ZXJmYWNlIEF1dGhDb250ZXh0VHlwZSB7XG4gIHVzZXI6IFVzZXIgfCBudWxsO1xuICBsb2FkaW5nOiBib29sZWFuO1xuICBpc0xvZ2dlZEluOiBib29sZWFuO1xuICBsb2dpbjogKGRhdGE6IExvZ2luRGF0YSwgcmVtZW1iZXI/OiBib29sZWFuKSA9PiBQcm9taXNlPEF1dGhSZXNwb25zZT47XG4gIHJlZ2lzdGVyOiAoZGF0YTogUmVnaXN0ZXJEYXRhKSA9PiBQcm9taXNlPEF1dGhSZXNwb25zZT47XG4gIGxvZ291dDogKCkgPT4gUHJvbWlzZTx2b2lkPjtcbiAgdXBkYXRlVXNlcjogKHVzZXI6IFVzZXIpID0+IHZvaWQ7XG4gIHJlZnJlc2hVc2VyOiAoKSA9PiBQcm9taXNlPHZvaWQ+O1xufVxuXG5jb25zdCBBdXRoQ29udGV4dCA9IGNyZWF0ZUNvbnRleHQ8QXV0aENvbnRleHRUeXBlIHwgdW5kZWZpbmVkPih1bmRlZmluZWQpO1xuXG5pbnRlcmZhY2UgQXV0aFByb3ZpZGVyUHJvcHMge1xuICBjaGlsZHJlbjogUmVhY3ROb2RlO1xufVxuXG5leHBvcnQgY29uc3QgQXV0aFByb3ZpZGVyOiBSZWFjdC5GQzxBdXRoUHJvdmlkZXJQcm9wcz4gPSAoeyBjaGlsZHJlbiB9KSA9PiB7XG4gIGNvbnN0IFt1c2VyLCBzZXRVc2VyXSA9IHVzZVN0YXRlPFVzZXIgfCBudWxsPihudWxsKTtcbiAgY29uc3QgW2xvYWRpbmcsIHNldExvYWRpbmddID0gdXNlU3RhdGUodHJ1ZSk7XG4gIGNvbnN0IHJvdXRlciA9IHVzZVJvdXRlcigpO1xuXG4gIC8vIEluaXRpYWxpemUgYXV0aCBzdGF0ZVxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IGluaXRBdXRoID0gYXN5bmMgKCkgPT4ge1xuICAgICAgdHJ5IHtcbiAgICAgICAgaWYgKGlzQXV0aGVudGljYXRlZCgpKSB7XG4gICAgICAgICAgLy8gVHJ5IHRvIGdldCB1c2VyIGZyb20gbG9jYWxTdG9yYWdlIGZpcnN0XG4gICAgICAgICAgY29uc3QgY2FjaGVkVXNlciA9IGdldEN1cnJlbnRVc2VyKCk7XG4gICAgICAgICAgaWYgKGNhY2hlZFVzZXIpIHtcbiAgICAgICAgICAgIHNldFVzZXIoY2FjaGVkVXNlcik7XG4gICAgICAgICAgfVxuICAgICAgICAgIFxuICAgICAgICAgIC8vIFRoZW4gcmVmcmVzaCBmcm9tIHNlcnZlclxuICAgICAgICAgIGNvbnN0IGZyZXNoVXNlciA9IGF3YWl0IGdldFByb2ZpbGUoKTtcbiAgICAgICAgICBpZiAoZnJlc2hVc2VyKSB7XG4gICAgICAgICAgICBzZXRVc2VyKGZyZXNoVXNlcik7XG4gICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIC8vIFRva2VuIG1pZ2h0IGJlIGV4cGlyZWQsIGNsZWFyIGF1dGggc3RhdGVcbiAgICAgICAgICAgIHJlbW92ZVRva2VucygpO1xuICAgICAgICAgICAgc2V0VXNlcihudWxsKTtcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0F1dGggaW5pdGlhbGl6YXRpb24gZXJyb3I6JywgZXJyb3IpO1xuICAgICAgICByZW1vdmVUb2tlbnMoKTtcbiAgICAgICAgc2V0VXNlcihudWxsKTtcbiAgICAgIH0gZmluYWxseSB7XG4gICAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xuICAgICAgfVxuICAgIH07XG5cbiAgICBpbml0QXV0aCgpO1xuICB9LCBbXSk7XG5cbiAgY29uc3QgbG9naW4gPSBhc3luYyAoZGF0YTogTG9naW5EYXRhLCByZW1lbWJlcjogYm9vbGVhbiA9IGZhbHNlKTogUHJvbWlzZTxBdXRoUmVzcG9uc2U+ID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBhcGlMb2dpbihkYXRhKTtcbiAgICAgIFxuICAgICAgaWYgKHJlc3BvbnNlLnN1Y2Nlc3MgJiYgcmVzcG9uc2UuZGF0YSkge1xuICAgICAgICBzZXRUb2tlbnMocmVzcG9uc2UuZGF0YS50b2tlbiwgcmVzcG9uc2UuZGF0YS5yZWZyZXNoVG9rZW4sIHJlbWVtYmVyKTtcbiAgICAgICAgc2V0Q3VycmVudFVzZXIocmVzcG9uc2UuZGF0YS51c2VyKTtcbiAgICAgICAgc2V0VXNlcihyZXNwb25zZS5kYXRhLnVzZXIpO1xuICAgICAgfVxuICAgICAgXG4gICAgICByZXR1cm4gcmVzcG9uc2U7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0xvZ2luIGVycm9yOicsIGVycm9yKTtcbiAgICAgIHJldHVybiB7XG4gICAgICAgIHN1Y2Nlc3M6IGZhbHNlLFxuICAgICAgICBtZXNzYWdlOiAnQW4gdW5leHBlY3RlZCBlcnJvciBvY2N1cnJlZCBkdXJpbmcgbG9naW4uJyxcbiAgICAgIH07XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IHJlZ2lzdGVyID0gYXN5bmMgKGRhdGE6IFJlZ2lzdGVyRGF0YSk6IFByb21pc2U8QXV0aFJlc3BvbnNlPiA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXBpUmVnaXN0ZXIoZGF0YSk7XG4gICAgICBcbiAgICAgIGlmIChyZXNwb25zZS5zdWNjZXNzICYmIHJlc3BvbnNlLmRhdGEpIHtcbiAgICAgICAgc2V0VG9rZW5zKHJlc3BvbnNlLmRhdGEudG9rZW4sIHJlc3BvbnNlLmRhdGEucmVmcmVzaFRva2VuLCB0cnVlKTtcbiAgICAgICAgc2V0Q3VycmVudFVzZXIocmVzcG9uc2UuZGF0YS51c2VyKTtcbiAgICAgICAgc2V0VXNlcihyZXNwb25zZS5kYXRhLnVzZXIpO1xuICAgICAgfVxuICAgICAgXG4gICAgICByZXR1cm4gcmVzcG9uc2U7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ1JlZ2lzdGVyIGVycm9yOicsIGVycm9yKTtcbiAgICAgIHJldHVybiB7XG4gICAgICAgIHN1Y2Nlc3M6IGZhbHNlLFxuICAgICAgICBtZXNzYWdlOiAnQW4gdW5leHBlY3RlZCBlcnJvciBvY2N1cnJlZCBkdXJpbmcgcmVnaXN0cmF0aW9uLicsXG4gICAgICB9O1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBsb2dvdXQgPSBhc3luYyAoKTogUHJvbWlzZTx2b2lkPiA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGF3YWl0IGFwaUxvZ291dCgpO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdMb2dvdXQgZXJyb3I6JywgZXJyb3IpO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRVc2VyKG51bGwpO1xuICAgICAgcm91dGVyLnB1c2goJy9sb2dpbicpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCB1cGRhdGVVc2VyID0gKHVwZGF0ZWRVc2VyOiBVc2VyKTogdm9pZCA9PiB7XG4gICAgc2V0VXNlcih1cGRhdGVkVXNlcik7XG4gICAgc2V0Q3VycmVudFVzZXIodXBkYXRlZFVzZXIpO1xuICB9O1xuXG4gIGNvbnN0IHJlZnJlc2hVc2VyID0gYXN5bmMgKCk6IFByb21pc2U8dm9pZD4gPT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCBmcmVzaFVzZXIgPSBhd2FpdCBnZXRQcm9maWxlKCk7XG4gICAgICBpZiAoZnJlc2hVc2VyKSB7XG4gICAgICAgIHNldFVzZXIoZnJlc2hVc2VyKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignUmVmcmVzaCB1c2VyIGVycm9yOicsIGVycm9yKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgdmFsdWU6IEF1dGhDb250ZXh0VHlwZSA9IHtcbiAgICB1c2VyLFxuICAgIGxvYWRpbmcsXG4gICAgaXNMb2dnZWRJbjogISF1c2VyLFxuICAgIGxvZ2luLFxuICAgIHJlZ2lzdGVyLFxuICAgIGxvZ291dCxcbiAgICB1cGRhdGVVc2VyLFxuICAgIHJlZnJlc2hVc2VyLFxuICB9O1xuXG4gIHJldHVybiAoXG4gICAgPEF1dGhDb250ZXh0LlByb3ZpZGVyIHZhbHVlPXt2YWx1ZX0+XG4gICAgICB7Y2hpbGRyZW59XG4gICAgPC9BdXRoQ29udGV4dC5Qcm92aWRlcj5cbiAgKTtcbn07XG5cbmV4cG9ydCBjb25zdCB1c2VBdXRoID0gKCk6IEF1dGhDb250ZXh0VHlwZSA9PiB7XG4gIGNvbnN0IGNvbnRleHQgPSB1c2VDb250ZXh0KEF1dGhDb250ZXh0KTtcbiAgaWYgKGNvbnRleHQgPT09IHVuZGVmaW5lZCkge1xuICAgIHRocm93IG5ldyBFcnJvcigndXNlQXV0aCBtdXN0IGJlIHVzZWQgd2l0aGluIGFuIEF1dGhQcm92aWRlcicpO1xuICB9XG4gIHJldHVybiBjb250ZXh0O1xufTtcblxuLy8gSGlnaGVyLW9yZGVyIGNvbXBvbmVudCBmb3IgcHJvdGVjdGVkIHJvdXRlc1xuZXhwb3J0IGNvbnN0IHdpdGhBdXRoID0gPFAgZXh0ZW5kcyBvYmplY3Q+KFxuICBXcmFwcGVkQ29tcG9uZW50OiBSZWFjdC5Db21wb25lbnRUeXBlPFA+XG4pOiBSZWFjdC5GQzxQPiA9PiB7XG4gIGNvbnN0IEF1dGhlbnRpY2F0ZWRDb21wb25lbnQ6IFJlYWN0LkZDPFA+ID0gKHByb3BzKSA9PiB7XG4gICAgY29uc3QgeyBpc0xvZ2dlZEluLCBsb2FkaW5nIH0gPSB1c2VBdXRoKCk7XG4gICAgY29uc3Qgcm91dGVyID0gdXNlUm91dGVyKCk7XG5cbiAgICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgICAgaWYgKCFsb2FkaW5nICYmICFpc0xvZ2dlZEluKSB7XG4gICAgICAgIHJvdXRlci5wdXNoKCcvbG9naW4nKTtcbiAgICAgIH1cbiAgICB9LCBbaXNMb2dnZWRJbiwgbG9hZGluZywgcm91dGVyXSk7XG5cbiAgICBpZiAobG9hZGluZykge1xuICAgICAgcmV0dXJuIChcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFuaW1hdGUtc3BpbiByb3VuZGVkLWZ1bGwgaC0zMiB3LTMyIGJvcmRlci1iLTIgYm9yZGVyLWluZGlnby02MDBcIj48L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICApO1xuICAgIH1cblxuICAgIGlmICghaXNMb2dnZWRJbikge1xuICAgICAgcmV0dXJuIG51bGw7XG4gICAgfVxuXG4gICAgcmV0dXJuIDxXcmFwcGVkQ29tcG9uZW50IHsuLi5wcm9wc30gLz47XG4gIH07XG5cbiAgQXV0aGVudGljYXRlZENvbXBvbmVudC5kaXNwbGF5TmFtZSA9IGB3aXRoQXV0aCgke1dyYXBwZWRDb21wb25lbnQuZGlzcGxheU5hbWUgfHwgV3JhcHBlZENvbXBvbmVudC5uYW1lfSlgO1xuICBcbiAgcmV0dXJuIEF1dGhlbnRpY2F0ZWRDb21wb25lbnQ7XG59O1xuXG4vLyBIb29rIGZvciBwcm90ZWN0ZWQgcm91dGVzXG5leHBvcnQgY29uc3QgdXNlUmVxdWlyZUF1dGggPSAoKSA9PiB7XG4gIGNvbnN0IHsgaXNMb2dnZWRJbiwgbG9hZGluZyB9ID0gdXNlQXV0aCgpO1xuICBjb25zdCByb3V0ZXIgPSB1c2VSb3V0ZXIoKTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmICghbG9hZGluZyAmJiAhaXNMb2dnZWRJbikge1xuICAgICAgcm91dGVyLnB1c2goJy9sb2dpbicpO1xuICAgIH1cbiAgfSwgW2lzTG9nZ2VkSW4sIGxvYWRpbmcsIHJvdXRlcl0pO1xuXG4gIHJldHVybiB7IGlzTG9nZ2VkSW4sIGxvYWRpbmcgfTtcbn07XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJjcmVhdGVDb250ZXh0IiwidXNlQ29udGV4dCIsInVzZUVmZmVjdCIsInVzZVN0YXRlIiwidXNlUm91dGVyIiwibG9naW4iLCJhcGlMb2dpbiIsInJlZ2lzdGVyIiwiYXBpUmVnaXN0ZXIiLCJsb2dvdXQiLCJhcGlMb2dvdXQiLCJnZXRQcm9maWxlIiwiZ2V0Q3VycmVudFVzZXIiLCJpc0F1dGhlbnRpY2F0ZWQiLCJyZW1vdmVUb2tlbnMiLCJzZXRUb2tlbnMiLCJzZXRDdXJyZW50VXNlciIsIkF1dGhDb250ZXh0IiwidW5kZWZpbmVkIiwiQXV0aFByb3ZpZGVyIiwiY2hpbGRyZW4iLCJ1c2VyIiwic2V0VXNlciIsImxvYWRpbmciLCJzZXRMb2FkaW5nIiwicm91dGVyIiwiaW5pdEF1dGgiLCJjYWNoZWRVc2VyIiwiZnJlc2hVc2VyIiwiZXJyb3IiLCJjb25zb2xlIiwiZGF0YSIsInJlbWVtYmVyIiwicmVzcG9uc2UiLCJzdWNjZXNzIiwidG9rZW4iLCJyZWZyZXNoVG9rZW4iLCJtZXNzYWdlIiwicHVzaCIsInVwZGF0ZVVzZXIiLCJ1cGRhdGVkVXNlciIsInJlZnJlc2hVc2VyIiwidmFsdWUiLCJpc0xvZ2dlZEluIiwiUHJvdmlkZXIiLCJ1c2VBdXRoIiwiY29udGV4dCIsIkVycm9yIiwid2l0aEF1dGgiLCJXcmFwcGVkQ29tcG9uZW50IiwiQXV0aGVudGljYXRlZENvbXBvbmVudCIsInByb3BzIiwiZGl2IiwiY2xhc3NOYW1lIiwiZGlzcGxheU5hbWUiLCJuYW1lIiwidXNlUmVxdWlyZUF1dGgiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authenticatedFetch: () => (/* binding */ authenticatedFetch),\n/* harmony export */   getCurrentUser: () => (/* binding */ getCurrentUser),\n/* harmony export */   getProfile: () => (/* binding */ getProfile),\n/* harmony export */   getRefreshToken: () => (/* binding */ getRefreshToken),\n/* harmony export */   getToken: () => (/* binding */ getToken),\n/* harmony export */   isAuthenticated: () => (/* binding */ isAuthenticated),\n/* harmony export */   login: () => (/* binding */ login),\n/* harmony export */   logout: () => (/* binding */ logout),\n/* harmony export */   refreshAuthToken: () => (/* binding */ refreshAuthToken),\n/* harmony export */   register: () => (/* binding */ register),\n/* harmony export */   removeTokens: () => (/* binding */ removeTokens),\n/* harmony export */   setCurrentUser: () => (/* binding */ setCurrentUser),\n/* harmony export */   setTokens: () => (/* binding */ setTokens),\n/* harmony export */   updateProfile: () => (/* binding */ updateProfile)\n/* harmony export */ });\n// Authentication utilities and API functions\nconst API_BASE_URL = \"http://localhost:5000\" || 0;\n// Token management\nconst getToken = ()=>{\n    if (true) return null;\n    return localStorage.getItem(\"token\") || sessionStorage.getItem(\"token\");\n};\nconst getRefreshToken = ()=>{\n    if (true) return null;\n    return localStorage.getItem(\"refreshToken\") || sessionStorage.getItem(\"refreshToken\");\n};\nconst setTokens = (token, refreshToken, remember = false)=>{\n    if (true) return;\n    const storage = remember ? localStorage : sessionStorage;\n    storage.setItem(\"token\", token);\n    storage.setItem(\"refreshToken\", refreshToken);\n    // Also store in localStorage for consistency\n    localStorage.setItem(\"token\", token);\n};\nconst removeTokens = ()=>{\n    if (true) return;\n    localStorage.removeItem(\"token\");\n    localStorage.removeItem(\"refreshToken\");\n    localStorage.removeItem(\"user\");\n    sessionStorage.removeItem(\"token\");\n    sessionStorage.removeItem(\"refreshToken\");\n    sessionStorage.removeItem(\"user\");\n};\nconst getCurrentUser = ()=>{\n    if (true) return null;\n    const userStr = localStorage.getItem(\"user\") || sessionStorage.getItem(\"user\");\n    if (!userStr) return null;\n    try {\n        return JSON.parse(userStr);\n    } catch  {\n        return null;\n    }\n};\nconst setCurrentUser = (user)=>{\n    if (true) return;\n    localStorage.setItem(\"user\", JSON.stringify(user));\n};\n// API functions\nconst login = async (data)=>{\n    try {\n        console.log(\"\\uD83D\\uDD10 Login attempt:\", {\n            email: data.email,\n            passwordLength: data.password?.length\n        });\n        console.log(\"\\uD83C\\uDF10 API URL:\", `${API_BASE_URL}/api/auth/login`);\n        const response = await fetch(`${API_BASE_URL}/api/auth/login`, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(data)\n        });\n        console.log(\"\\uD83D\\uDCE1 Response status:\", response.status, response.statusText);\n        const result = await response.json();\n        console.log(\"\\uD83D\\uDCCB Response data:\", result);\n        return result;\n    } catch (error) {\n        console.error(\"❌ Login error:\", error);\n        return {\n            success: false,\n            message: \"Network error. Please check your connection and try again.\"\n        };\n    }\n};\nconst register = async (data)=>{\n    try {\n        const response = await fetch(`${API_BASE_URL}/api/auth/register`, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(data)\n        });\n        return await response.json();\n    } catch (error) {\n        console.error(\"Register error:\", error);\n        return {\n            success: false,\n            message: \"Network error. Please check your connection and try again.\"\n        };\n    }\n};\nconst logout = async ()=>{\n    try {\n        const token = getToken();\n        if (token) {\n            await fetch(`${API_BASE_URL}/api/auth/logout`, {\n                method: \"POST\",\n                headers: {\n                    \"Authorization\": `Bearer ${token}`,\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n        }\n    } catch (error) {\n        console.error(\"Logout error:\", error);\n    } finally{\n        removeTokens();\n    }\n};\nconst getProfile = async ()=>{\n    try {\n        const token = getToken();\n        if (!token) return null;\n        const response = await fetch(`${API_BASE_URL}/api/auth/profile`, {\n            headers: {\n                \"Authorization\": `Bearer ${token}`,\n                \"Content-Type\": \"application/json\"\n            }\n        });\n        const data = await response.json();\n        if (data.success) {\n            setCurrentUser(data.data.user);\n            return data.data.user;\n        }\n        return null;\n    } catch (error) {\n        console.error(\"Get profile error:\", error);\n        return null;\n    }\n};\nconst updateProfile = async (userData)=>{\n    try {\n        const token = getToken();\n        if (!token) {\n            return {\n                success: false,\n                message: \"No authentication token found\"\n            };\n        }\n        console.log(\"Sending profile update request:\", userData);\n        const response = await fetch(`${API_BASE_URL}/api/auth/profile`, {\n            method: \"PUT\",\n            headers: {\n                \"Authorization\": `Bearer ${token}`,\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(userData)\n        });\n        const data = await response.json();\n        console.log(\"Profile update response:\", data);\n        if (data.success) {\n            setCurrentUser(data.data.user);\n        }\n        return data;\n    } catch (error) {\n        console.error(\"Update profile error:\", error);\n        return {\n            success: false,\n            message: \"Network error. Please check your connection and try again.\"\n        };\n    }\n};\n// Check if user is authenticated\nconst isAuthenticated = ()=>{\n    return !!getToken();\n};\n// Create authenticated fetch function\nconst authenticatedFetch = async (url, options = {})=>{\n    const token = getToken();\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        ...options.headers || {}\n    };\n    if (token) {\n        headers[\"Authorization\"] = `Bearer ${token}`;\n    }\n    return fetch(url.startsWith(\"http\") ? url : `${API_BASE_URL}${url}`, {\n        ...options,\n        headers\n    });\n};\n// Refresh token function\nconst refreshAuthToken = async ()=>{\n    try {\n        const refreshToken = getRefreshToken();\n        if (!refreshToken) return false;\n        const response = await fetch(`${API_BASE_URL}/api/auth/refresh`, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                refreshToken\n            })\n        });\n        const data = await response.json();\n        if (data.success) {\n            setTokens(data.data.token, data.data.refreshToken);\n            return true;\n        }\n        return false;\n    } catch (error) {\n        console.error(\"Token refresh error:\", error);\n        return false;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"1f6b6a191842\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZnJvbnRlbmQvLi9zcmMvYXBwL2dsb2JhbHMuY3NzPzc1YzEiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIxZjZiNmExOTE4NDJcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata),\n/* harmony export */   viewport: () => (/* binding */ viewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-inter\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../contexts/AuthContext */ \"(rsc)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"(rsc)/./node_modules/react-hot-toast/dist/index.mjs\");\n\n\n\n\n\nconst metadata = {\n    title: \"InvoNest - AI-Powered Invoicing & Compliance Platform\",\n    description: \"Secure, AI-powered invoicing and compliance platform for Indian MSMEs, freelancers, and gig workers. GST-compliant invoice generation with blockchain integrity.\",\n    keywords: [\n        \"invoicing\",\n        \"GST\",\n        \"compliance\",\n        \"AI\",\n        \"MSME\",\n        \"India\",\n        \"tax\",\n        \"blockchain\"\n    ],\n    authors: [\n        {\n            name: \"InvoNest Team\"\n        }\n    ]\n};\nconst viewport = {\n    width: \"device-width\",\n    initialScale: 1,\n    maximumScale: 1,\n    userScalable: false,\n    viewportFit: \"cover\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.svg\",\n                        type: \"image/svg+xml\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\",\n                        sizes: \"32x32\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"manifest\",\n                        href: \"/manifest.json\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"theme-color\",\n                        content: \"#4f46e5\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-capable\",\n                        content: \"yes\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-status-bar-style\",\n                        content: \"default\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-title\",\n                        content: \"InvoNest\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        href: \"/icons/icon-192x192.svg\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"mobile-web-app-capable\",\n                        content: \"yes\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"application-name\",\n                        content: \"InvoNest\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"msapplication-TileColor\",\n                        content: \"#4f46e5\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"msapplication-TileImage\",\n                        content: \"/icons/icon-144x144.svg\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"format-detection\",\n                        content: \"telephone=no\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 9\n                    }, this),\n                     true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                                httpEquiv: \"Cache-Control\",\n                                content: \"no-cache, no-store, must-revalidate\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 50,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                                httpEquiv: \"Pragma\",\n                                content: \"no-cache\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 51,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                                httpEquiv: \"Expires\",\n                                content: \"0\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 52,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                                name: \"cache-bust\",\n                                content: Date.now().toString()\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                                src: \"/disable-turbopack-overlay.js\",\n                                defer: true\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().variable)} font-sans antialiased bg-gray-50 touch-manipulation`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"min-h-screen safe-area-inset\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.Toaster, {\n                            position: \"top-right\",\n                            toastOptions: {\n                                duration: 4000,\n                                style: {\n                                    background: \"#363636\",\n                                    color: \"#fff\"\n                                },\n                                success: {\n                                    duration: 3000,\n                                    iconTheme: {\n                                        primary: \"#4ade80\",\n                                        secondary: \"#fff\"\n                                    }\n                                },\n                                error: {\n                                    duration: 4000,\n                                    iconTheme: {\n                                        primary: \"#ef4444\",\n                                        secondary: \"#fff\"\n                                    }\n                                }\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 63,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 59,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFNTUE7QUFKaUI7QUFDZ0M7QUFDYjtBQU9uQyxNQUFNRyxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0lBQ2JDLFVBQVU7UUFBQztRQUFhO1FBQU87UUFBYztRQUFNO1FBQVE7UUFBUztRQUFPO0tBQWE7SUFDeEZDLFNBQVM7UUFBQztZQUFFQyxNQUFNO1FBQWdCO0tBQUU7QUFDdEMsRUFBRTtBQUVLLE1BQU1DLFdBQVc7SUFDdEJDLE9BQU87SUFDUEMsY0FBYztJQUNkQyxjQUFjO0lBQ2RDLGNBQWM7SUFDZEMsYUFBYTtBQUNmLEVBQUU7QUFFYSxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1I7SUFDQSxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSzs7MEJBQ1QsOERBQUNDOztrQ0FDQyw4REFBQ0M7d0JBQUtDLEtBQUk7d0JBQU9DLE1BQUs7d0JBQWVDLE1BQUs7Ozs7OztrQ0FDMUMsOERBQUNIO3dCQUFLQyxLQUFJO3dCQUFPQyxNQUFLO3dCQUFlRSxPQUFNOzs7Ozs7a0NBQzNDLDhEQUFDSjt3QkFBS0MsS0FBSTt3QkFBV0MsTUFBSzs7Ozs7O2tDQUMxQiw4REFBQ0c7d0JBQUtqQixNQUFLO3dCQUFja0IsU0FBUTs7Ozs7O2tDQUNqQyw4REFBQ0Q7d0JBQUtqQixNQUFLO3dCQUErQmtCLFNBQVE7Ozs7OztrQ0FDbEQsOERBQUNEO3dCQUFLakIsTUFBSzt3QkFBd0NrQixTQUFROzs7Ozs7a0NBQzNELDhEQUFDRDt3QkFBS2pCLE1BQUs7d0JBQTZCa0IsU0FBUTs7Ozs7O2tDQUNoRCw4REFBQ047d0JBQUtDLEtBQUk7d0JBQW1CQyxNQUFLOzs7Ozs7a0NBQ2xDLDhEQUFDRzt3QkFBS2pCLE1BQUs7d0JBQXlCa0IsU0FBUTs7Ozs7O2tDQUM1Qyw4REFBQ0Q7d0JBQUtqQixNQUFLO3dCQUFtQmtCLFNBQVE7Ozs7OztrQ0FDdEMsOERBQUNEO3dCQUFLakIsTUFBSzt3QkFBMEJrQixTQUFROzs7Ozs7a0NBQzdDLDhEQUFDRDt3QkFBS2pCLE1BQUs7d0JBQTBCa0IsU0FBUTs7Ozs7O2tDQUM3Qyw4REFBQ0Q7d0JBQUtqQixNQUFLO3dCQUFtQmtCLFNBQVE7Ozs7OztvQkE5QzlDLEtBK0NrQyxrQkFDeEI7OzBDQUNFLDhEQUFDRDtnQ0FBS0UsV0FBVTtnQ0FBZ0JELFNBQVE7Ozs7OzswQ0FDeEMsOERBQUNEO2dDQUFLRSxXQUFVO2dDQUFTRCxTQUFROzs7Ozs7MENBQ2pDLDhEQUFDRDtnQ0FBS0UsV0FBVTtnQ0FBVUQsU0FBUTs7Ozs7OzBDQUNsQyw4REFBQ0Q7Z0NBQUtqQixNQUFLO2dDQUFha0IsU0FBU0UsS0FBS0MsR0FBRyxHQUFHQyxRQUFROzs7Ozs7MENBQ3BELDhEQUFDQztnQ0FBT0MsS0FBSTtnQ0FBZ0NDLEtBQUs7Ozs7Ozs7Ozs7Ozs7OzBCQUl2RCw4REFBQ0M7Z0JBQUtDLFdBQVcsQ0FBQyxFQUFFbkMsa0xBQWMsQ0FBQyxvREFBb0QsQ0FBQzswQkFDdEYsNEVBQUNDLCtEQUFZQTs7c0NBQ1gsOERBQUNvQzs0QkFBSUYsV0FBVTtzQ0FDWm5COzs7Ozs7c0NBRUgsOERBQUNkLG9EQUFPQTs0QkFDTm9DLFVBQVM7NEJBQ1RDLGNBQWM7Z0NBQ1pDLFVBQVU7Z0NBQ1ZDLE9BQU87b0NBQ0xDLFlBQVk7b0NBQ1pDLE9BQU87Z0NBQ1Q7Z0NBQ0FDLFNBQVM7b0NBQ1BKLFVBQVU7b0NBQ1ZLLFdBQVc7d0NBQ1RDLFNBQVM7d0NBQ1RDLFdBQVc7b0NBQ2I7Z0NBQ0Y7Z0NBQ0FDLE9BQU87b0NBQ0xSLFVBQVU7b0NBQ1ZLLFdBQVc7d0NBQ1RDLFNBQVM7d0NBQ1RDLFdBQVc7b0NBQ2I7Z0NBQ0Y7NEJBQ0Y7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTVoiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mcm9udGVuZC8uL3NyYy9hcHAvbGF5b3V0LnRzeD81N2E5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tIFwibmV4dFwiO1xuaW1wb3J0IHsgSW50ZXIgfSBmcm9tIFwibmV4dC9mb250L2dvb2dsZVwiO1xuaW1wb3J0IFwiLi9nbG9iYWxzLmNzc1wiO1xuaW1wb3J0IHsgQXV0aFByb3ZpZGVyIH0gZnJvbSBcIi4uL2NvbnRleHRzL0F1dGhDb250ZXh0XCI7XG5pbXBvcnQgeyBUb2FzdGVyIH0gZnJvbSBcInJlYWN0LWhvdC10b2FzdFwiO1xuXG5jb25zdCBpbnRlciA9IEludGVyKHtcbiAgc3Vic2V0czogW1wibGF0aW5cIl0sXG4gIHZhcmlhYmxlOiBcIi0tZm9udC1pbnRlclwiLFxufSk7XG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiBcIkludm9OZXN0IC0gQUktUG93ZXJlZCBJbnZvaWNpbmcgJiBDb21wbGlhbmNlIFBsYXRmb3JtXCIsXG4gIGRlc2NyaXB0aW9uOiBcIlNlY3VyZSwgQUktcG93ZXJlZCBpbnZvaWNpbmcgYW5kIGNvbXBsaWFuY2UgcGxhdGZvcm0gZm9yIEluZGlhbiBNU01FcywgZnJlZWxhbmNlcnMsIGFuZCBnaWcgd29ya2Vycy4gR1NULWNvbXBsaWFudCBpbnZvaWNlIGdlbmVyYXRpb24gd2l0aCBibG9ja2NoYWluIGludGVncml0eS5cIixcbiAga2V5d29yZHM6IFtcImludm9pY2luZ1wiLCBcIkdTVFwiLCBcImNvbXBsaWFuY2VcIiwgXCJBSVwiLCBcIk1TTUVcIiwgXCJJbmRpYVwiLCBcInRheFwiLCBcImJsb2NrY2hhaW5cIl0sXG4gIGF1dGhvcnM6IFt7IG5hbWU6IFwiSW52b05lc3QgVGVhbVwiIH1dLFxufTtcblxuZXhwb3J0IGNvbnN0IHZpZXdwb3J0ID0ge1xuICB3aWR0aDogJ2RldmljZS13aWR0aCcsXG4gIGluaXRpYWxTY2FsZTogMSxcbiAgbWF4aW11bVNjYWxlOiAxLFxuICB1c2VyU2NhbGFibGU6IGZhbHNlLFxuICB2aWV3cG9ydEZpdDogJ2NvdmVyJyxcbn07XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IFJlYWRvbmx5PHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbn0+KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cImVuXCI+XG4gICAgICA8aGVhZD5cbiAgICAgICAgPGxpbmsgcmVsPVwiaWNvblwiIGhyZWY9XCIvZmF2aWNvbi5zdmdcIiB0eXBlPVwiaW1hZ2Uvc3ZnK3htbFwiIC8+XG4gICAgICAgIDxsaW5rIHJlbD1cImljb25cIiBocmVmPVwiL2Zhdmljb24uaWNvXCIgc2l6ZXM9XCIzMngzMlwiIC8+XG4gICAgICAgIDxsaW5rIHJlbD1cIm1hbmlmZXN0XCIgaHJlZj1cIi9tYW5pZmVzdC5qc29uXCIgLz5cbiAgICAgICAgPG1ldGEgbmFtZT1cInRoZW1lLWNvbG9yXCIgY29udGVudD1cIiM0ZjQ2ZTVcIiAvPlxuICAgICAgICA8bWV0YSBuYW1lPVwiYXBwbGUtbW9iaWxlLXdlYi1hcHAtY2FwYWJsZVwiIGNvbnRlbnQ9XCJ5ZXNcIiAvPlxuICAgICAgICA8bWV0YSBuYW1lPVwiYXBwbGUtbW9iaWxlLXdlYi1hcHAtc3RhdHVzLWJhci1zdHlsZVwiIGNvbnRlbnQ9XCJkZWZhdWx0XCIgLz5cbiAgICAgICAgPG1ldGEgbmFtZT1cImFwcGxlLW1vYmlsZS13ZWItYXBwLXRpdGxlXCIgY29udGVudD1cIkludm9OZXN0XCIgLz5cbiAgICAgICAgPGxpbmsgcmVsPVwiYXBwbGUtdG91Y2gtaWNvblwiIGhyZWY9XCIvaWNvbnMvaWNvbi0xOTJ4MTkyLnN2Z1wiIC8+XG4gICAgICAgIDxtZXRhIG5hbWU9XCJtb2JpbGUtd2ViLWFwcC1jYXBhYmxlXCIgY29udGVudD1cInllc1wiIC8+XG4gICAgICAgIDxtZXRhIG5hbWU9XCJhcHBsaWNhdGlvbi1uYW1lXCIgY29udGVudD1cIkludm9OZXN0XCIgLz5cbiAgICAgICAgPG1ldGEgbmFtZT1cIm1zYXBwbGljYXRpb24tVGlsZUNvbG9yXCIgY29udGVudD1cIiM0ZjQ2ZTVcIiAvPlxuICAgICAgICA8bWV0YSBuYW1lPVwibXNhcHBsaWNhdGlvbi1UaWxlSW1hZ2VcIiBjb250ZW50PVwiL2ljb25zL2ljb24tMTQ0eDE0NC5zdmdcIiAvPlxuICAgICAgICA8bWV0YSBuYW1lPVwiZm9ybWF0LWRldGVjdGlvblwiIGNvbnRlbnQ9XCJ0ZWxlcGhvbmU9bm9cIiAvPlxuICAgICAgICB7cHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdkZXZlbG9wbWVudCcgJiYgKFxuICAgICAgICAgIDw+XG4gICAgICAgICAgICA8bWV0YSBodHRwRXF1aXY9XCJDYWNoZS1Db250cm9sXCIgY29udGVudD1cIm5vLWNhY2hlLCBuby1zdG9yZSwgbXVzdC1yZXZhbGlkYXRlXCIgLz5cbiAgICAgICAgICAgIDxtZXRhIGh0dHBFcXVpdj1cIlByYWdtYVwiIGNvbnRlbnQ9XCJuby1jYWNoZVwiIC8+XG4gICAgICAgICAgICA8bWV0YSBodHRwRXF1aXY9XCJFeHBpcmVzXCIgY29udGVudD1cIjBcIiAvPlxuICAgICAgICAgICAgPG1ldGEgbmFtZT1cImNhY2hlLWJ1c3RcIiBjb250ZW50PXtEYXRlLm5vdygpLnRvU3RyaW5nKCl9IC8+XG4gICAgICAgICAgICA8c2NyaXB0IHNyYz1cIi9kaXNhYmxlLXR1cmJvcGFjay1vdmVybGF5LmpzXCIgZGVmZXIgLz5cbiAgICAgICAgICA8Lz5cbiAgICAgICAgKX1cbiAgICAgIDwvaGVhZD5cbiAgICAgIDxib2R5IGNsYXNzTmFtZT17YCR7aW50ZXIudmFyaWFibGV9IGZvbnQtc2FucyBhbnRpYWxpYXNlZCBiZy1ncmF5LTUwIHRvdWNoLW1hbmlwdWxhdGlvbmB9PlxuICAgICAgICA8QXV0aFByb3ZpZGVyPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIHNhZmUtYXJlYS1pbnNldFwiPlxuICAgICAgICAgICAge2NoaWxkcmVufVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDxUb2FzdGVyXG4gICAgICAgICAgICBwb3NpdGlvbj1cInRvcC1yaWdodFwiXG4gICAgICAgICAgICB0b2FzdE9wdGlvbnM9e3tcbiAgICAgICAgICAgICAgZHVyYXRpb246IDQwMDAsXG4gICAgICAgICAgICAgIHN0eWxlOiB7XG4gICAgICAgICAgICAgICAgYmFja2dyb3VuZDogJyMzNjM2MzYnLFxuICAgICAgICAgICAgICAgIGNvbG9yOiAnI2ZmZicsXG4gICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgIHN1Y2Nlc3M6IHtcbiAgICAgICAgICAgICAgICBkdXJhdGlvbjogMzAwMCxcbiAgICAgICAgICAgICAgICBpY29uVGhlbWU6IHtcbiAgICAgICAgICAgICAgICAgIHByaW1hcnk6ICcjNGFkZTgwJyxcbiAgICAgICAgICAgICAgICAgIHNlY29uZGFyeTogJyNmZmYnLFxuICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgIGVycm9yOiB7XG4gICAgICAgICAgICAgICAgZHVyYXRpb246IDQwMDAsXG4gICAgICAgICAgICAgICAgaWNvblRoZW1lOiB7XG4gICAgICAgICAgICAgICAgICBwcmltYXJ5OiAnI2VmNDQ0NCcsXG4gICAgICAgICAgICAgICAgICBzZWNvbmRhcnk6ICcjZmZmJyxcbiAgICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgfX1cbiAgICAgICAgICAvPlxuICAgICAgICA8L0F1dGhQcm92aWRlcj5cbiAgICAgIDwvYm9keT5cbiAgICA8L2h0bWw+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiaW50ZXIiLCJBdXRoUHJvdmlkZXIiLCJUb2FzdGVyIiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwia2V5d29yZHMiLCJhdXRob3JzIiwibmFtZSIsInZpZXdwb3J0Iiwid2lkdGgiLCJpbml0aWFsU2NhbGUiLCJtYXhpbXVtU2NhbGUiLCJ1c2VyU2NhbGFibGUiLCJ2aWV3cG9ydEZpdCIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiaGVhZCIsImxpbmsiLCJyZWwiLCJocmVmIiwidHlwZSIsInNpemVzIiwibWV0YSIsImNvbnRlbnQiLCJodHRwRXF1aXYiLCJEYXRlIiwibm93IiwidG9TdHJpbmciLCJzY3JpcHQiLCJzcmMiLCJkZWZlciIsImJvZHkiLCJjbGFzc05hbWUiLCJ2YXJpYWJsZSIsImRpdiIsInBvc2l0aW9uIiwidG9hc3RPcHRpb25zIiwiZHVyYXRpb24iLCJzdHlsZSIsImJhY2tncm91bmQiLCJjb2xvciIsInN1Y2Nlc3MiLCJpY29uVGhlbWUiLCJwcmltYXJ5Iiwic2Vjb25kYXJ5IiwiZXJyb3IiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ e0),
/* harmony export */   useAuth: () => (/* binding */ e1),
/* harmony export */   useRequireAuth: () => (/* binding */ e3),
/* harmony export */   withAuth: () => (/* binding */ e2)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\invoNest\frontend\src\contexts\AuthContext.tsx#AuthProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\invoNest\frontend\src\contexts\AuthContext.tsx#useAuth`);

const e2 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\invoNest\frontend\src\contexts\AuthContext.tsx#withAuth`);

const e3 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\invoNest\frontend\src\contexts\AuthContext.tsx#useRequireAuth`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/react-hot-toast","vendor-chunks/goober","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5Cgupta%5CDesktop%5CinvoNest%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cgupta%5CDesktop%5CinvoNest%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();