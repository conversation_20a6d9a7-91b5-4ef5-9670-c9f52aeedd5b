export interface ParsedDocumentData {
    text: string;
    entities: Array<{
        type: string;
        value: string;
        confidence: number;
    }>;
    invoiceData?: {
        invoiceNumber?: string;
        date?: Date;
        amount?: number;
        vendor?: string;
        gstNumber?: string;
        customerName?: string;
        customerGST?: string;
        items?: Array<{
            description: string;
            quantity?: number;
            rate?: number;
            amount?: number;
        }>;
    };
    documentType?: 'invoice' | 'receipt' | 'tax_document' | 'compliance' | 'other';
    confidence: number;
}
export declare class DocumentParsingService {
    private pdfExtract;
    constructor();
    /**
     * Extract text from PDF files
     */
    private extractTextFromPDF;
    /**
     * Extract text from image files using OCR (placeholder - would need actual OCR service)
     */
    private extractTextFromImage;
    /**
     * Extract text from text files
     */
    private extractTextFromTextFile;
    /**
     * Extract text based on file type
     */
    private extractText;
    /**
     * Rule-based text analysis as fallback when AI is not available
     */
    private analyzeTextWithRules;
    /**
     * Parse document and extract structured data using rule-based analysis only
     */
    parseDocument(filePath: string, mimeType: string): Promise<ParsedDocumentData>;
    /**
     * Validate extracted invoice data
     */
    validateInvoiceData(invoiceData: any): {
        isValid: boolean;
        errors: string[];
        warnings: string[];
    };
    /**
     * Get supported file types
     */
    getSupportedFileTypes(): string[];
    /**
     * Check if file type is supported for parsing
     */
    isFileTypeSupported(mimeType: string): boolean;
}
export declare const documentParsingService: DocumentParsingService;
//# sourceMappingURL=documentParsingService.d.ts.map