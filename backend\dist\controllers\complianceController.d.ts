import { Request, Response } from 'express';
export declare const getUserCompliance: (req: Request, res: Response) => Promise<void>;
export declare const getComplianceCalendar: (req: Request, res: Response) => Promise<void>;
export declare const getUpcomingDeadlines: (req: Request, res: Response) => Promise<void>;
export declare const markComplianceCompleted: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const updateComplianceSettings: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const addCustomCompliance: (req: Request, res: Response) => Promise<void>;
export declare const getComplianceStats: (req: Request, res: Response) => Promise<void>;
export declare const getOverdueActivity: (req: Request, res: Response) => Promise<void>;
export declare const clearUserCompliance: (req: Request, res: Response) => Promise<void>;
//# sourceMappingURL=complianceController.d.ts.map