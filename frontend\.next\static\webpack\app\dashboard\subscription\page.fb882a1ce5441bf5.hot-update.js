"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/subscription/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/subscription/page.tsx":
/*!*************************************************!*\
  !*** ./src/app/dashboard/subscription/page.tsx ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SubscriptionPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../lib/auth */ \"(app-pages-browser)/./src/lib/auth.ts\");\n/* harmony import */ var _components_dashboard_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../components/dashboard/DashboardLayout */ \"(app-pages-browser)/./src/components/dashboard/DashboardLayout.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction SubscriptionPage() {\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [plans, setPlans] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentSubscription, setCurrentSubscription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [upgrading, setUpgrading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [billingInterval, setBillingInterval] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"monthly\");\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (user) {\n            fetchPlansAndSubscription();\n        }\n    }, [\n        user\n    ]);\n    const fetchPlansAndSubscription = async ()=>{\n        try {\n            // First sync usage to ensure accurate data\n            try {\n                await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.authenticatedFetch)(\"/api/subscriptions/usage/sync\", {\n                    method: \"POST\"\n                });\n            } catch (syncError) {\n                console.warn(\"Failed to sync usage:\", syncError);\n            // Continue even if sync fails\n            }\n            const [plansResponse, subscriptionResponse] = await Promise.all([\n                (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.authenticatedFetch)(\"/api/subscriptions/plans\"),\n                (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.authenticatedFetch)(\"/api/subscriptions/current\")\n            ]);\n            const plansData = await plansResponse.json();\n            const subscriptionData = await subscriptionResponse.json();\n            if (plansData.success) {\n                setPlans(plansData.data.plans);\n            }\n            if (subscriptionData.success) {\n                setCurrentSubscription(subscriptionData.data.subscription);\n            }\n        } catch (error) {\n            console.error(\"Failed to fetch subscription data:\", error);\n            setError(\"Failed to load subscription information\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleUpgrade = async (planId)=>{\n        if (planId === \"free\") {\n            // Handle downgrade to free\n            try {\n                setUpgrading(planId);\n                const response = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.authenticatedFetch)(\"/api/subscriptions/change\", {\n                    method: \"POST\",\n                    body: JSON.stringify({\n                        planId,\n                        interval: billingInterval\n                    })\n                });\n                const data = await response.json();\n                if (data.success) {\n                    await fetchPlansAndSubscription();\n                    setError(\"\");\n                } else {\n                    setError(data.message);\n                }\n            } catch (error) {\n                console.error(\"Failed to change subscription:\", error);\n                setError(\"Failed to change subscription\");\n            } finally{\n                setUpgrading(null);\n            }\n            return;\n        }\n        // Handle paid plan upgrade\n        try {\n            var _plans_find;\n            setUpgrading(planId);\n            // Create payment order\n            const orderResponse = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.authenticatedFetch)(\"/api/subscriptions/payment/create-order\", {\n                method: \"POST\",\n                body: JSON.stringify({\n                    planId,\n                    interval: billingInterval\n                })\n            });\n            const orderData = await orderResponse.json();\n            console.log(\"\\uD83D\\uDD0D Order response:\", orderData);\n            if (!orderData.success) {\n                throw new Error(orderData.message || \"Failed to create payment order\");\n            }\n            // Check if Razorpay key is available\n            if (!orderData.data.key) {\n                throw new Error(\"Payment gateway is not configured. Please contact support.\");\n            }\n            console.log(\"✅ Order data received, initializing Razorpay...\");\n            // Initialize Razorpay payment\n            const options = {\n                key: orderData.data.key,\n                amount: orderData.data.amount,\n                currency: orderData.data.currency,\n                name: \"InvoNest\",\n                description: \"Upgrade to \".concat((_plans_find = plans.find((p)=>p.id === planId)) === null || _plans_find === void 0 ? void 0 : _plans_find.name, \" Plan\"),\n                order_id: orderData.data.orderId,\n                handler: async (response)=>{\n                    try {\n                        // Verify payment\n                        const verifyResponse = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.authenticatedFetch)(\"/api/subscriptions/payment/verify\", {\n                            method: \"POST\",\n                            body: JSON.stringify({\n                                razorpay_order_id: response.razorpay_order_id,\n                                razorpay_payment_id: response.razorpay_payment_id,\n                                razorpay_signature: response.razorpay_signature\n                            })\n                        });\n                        const verifyData = await verifyResponse.json();\n                        if (verifyData.success) {\n                            await fetchPlansAndSubscription();\n                            setError(\"\");\n                            alert(\"Subscription upgraded successfully!\");\n                        } else {\n                            throw new Error(verifyData.message || \"Payment verification failed\");\n                        }\n                    } catch (error) {\n                        console.error(\"Payment verification failed:\", error);\n                        setError(error instanceof Error ? error.message : \"Payment verification failed\");\n                    } finally{\n                        setUpgrading(null);\n                    }\n                },\n                modal: {\n                    ondismiss: ()=>{\n                        console.log(\"\\uD83D\\uDD04 Razorpay modal dismissed\");\n                        setUpgrading(null);\n                    }\n                },\n                theme: {\n                    color: \"#4F46E5\"\n                }\n            };\n            // Load Razorpay script and open payment modal\n            console.log(\"\\uD83D\\uDD04 Loading Razorpay script...\");\n            // Check if Razorpay script is already loaded\n            if (window.Razorpay) {\n                console.log(\"✅ Razorpay already loaded, opening payment modal...\");\n                try {\n                    const rzp = new window.Razorpay(options);\n                    rzp.open();\n                } catch (error) {\n                    console.error(\"Failed to initialize Razorpay:\", error);\n                    setError(\"Failed to initialize payment gateway\");\n                    setUpgrading(null);\n                }\n                return;\n            }\n            const script = document.createElement(\"script\");\n            script.src = \"https://checkout.razorpay.com/v1/checkout.js\";\n            script.onload = ()=>{\n                console.log(\"✅ Razorpay script loaded successfully\");\n                try {\n                    const rzp = new window.Razorpay(options);\n                    console.log(\"✅ Razorpay instance created, opening modal...\");\n                    // Add a small delay to ensure DOM is ready\n                    setTimeout(()=>{\n                        rzp.open();\n                        console.log(\"\\uD83D\\uDE80 Razorpay modal opened successfully\");\n                        // Check if modal elements are created\n                        setTimeout(()=>{\n                            const modalElements = document.querySelectorAll('[id*=\"razorpay\"], [class*=\"razorpay\"]');\n                            console.log(\"\\uD83D\\uDD0D Razorpay modal elements found:\", modalElements.length);\n                            modalElements.forEach((el, index)=>{\n                                console.log(\"Element \".concat(index, \":\"), el.tagName, el.id, el.className);\n                            });\n                        }, 500);\n                    }, 100);\n                } catch (error) {\n                    console.error(\"Failed to initialize Razorpay:\", error);\n                    setError(\"Failed to initialize payment gateway\");\n                    setUpgrading(null);\n                }\n            };\n            script.onerror = ()=>{\n                console.error(\"❌ Failed to load Razorpay script\");\n                setError(\"Failed to load payment gateway\");\n                setUpgrading(null);\n            };\n            document.head.appendChild(script);\n        } catch (error) {\n            console.error(\"Failed to upgrade subscription:\", error);\n            setError(error instanceof Error ? error.message : \"Failed to upgrade subscription\");\n            setUpgrading(null);\n        }\n    };\n    const formatPrice = (price)=>{\n        return new Intl.NumberFormat(\"en-IN\", {\n            style: \"currency\",\n            currency: \"INR\"\n        }).format(price / 100);\n    };\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString(\"en-IN\", {\n            year: \"numeric\",\n            month: \"long\",\n            day: \"numeric\"\n        });\n    };\n    const getUsageColor = (percentage)=>{\n        if (percentage >= 90) return \"bg-red-500\";\n        if (percentage >= 75) return \"bg-yellow-500\";\n        return \"bg-green-500\";\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center py-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"animate-spin h-8 w-8 text-indigo-600\",\n                            xmlns: \"http://www.w3.org/2000/svg\",\n                            fill: \"none\",\n                            viewBox: \"0 0 24 24\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                    className: \"opacity-25\",\n                                    cx: \"12\",\n                                    cy: \"12\",\n                                    r: \"10\",\n                                    stroke: \"currentColor\",\n                                    strokeWidth: \"4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                    lineNumber: 274,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    className: \"opacity-75\",\n                                    fill: \"currentColor\",\n                                    d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                    lineNumber: 275,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                            lineNumber: 273,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-lg text-gray-600\",\n                            children: \"Loading subscription...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                            lineNumber: 277,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                    lineNumber: 272,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                lineNumber: 271,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n            lineNumber: 270,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__.DashboardHeader, {\n                title: \"Subscription & Billing\",\n                subtitle: \"Manage your InvoNest subscription and view usage\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                lineNumber: 286,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto p-4 lg:p-8 space-y-8\",\n                children: [\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                        lineNumber: 294,\n                        columnNumber: 11\n                    }, this),\n                    currentSubscription && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-xl shadow-sm border border-gray-200 p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                children: \"Current Subscription\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                lineNumber: 302,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-gray-900\",\n                                                children: currentSubscription.planName\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                lineNumber: 305,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600\",\n                                                children: [\n                                                    formatPrice(currentSubscription.amount),\n                                                    \" / \",\n                                                    currentSubscription.interval\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                lineNumber: 306,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-500 mt-1\",\n                                                children: [\n                                                    currentSubscription.status === \"active\" ? \"Active\" : currentSubscription.status,\n                                                    currentSubscription.currentPeriodEnd && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            \" until \",\n                                                            formatDate(currentSubscription.currentPeriodEnd)\n                                                        ]\n                                                    }, void 0, true)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                lineNumber: 309,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                        lineNumber: 304,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"md:col-span-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-sm font-medium text-gray-900 mb-3\",\n                                                children: \"Usage This Period\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                lineNumber: 319,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between text-sm text-gray-900\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Invoices\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                        lineNumber: 323,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            currentSubscription.usage.invoicesUsed,\n                                                                            \" / \",\n                                                                            currentSubscription.features.maxInvoices === -1 ? \"∞\" : currentSubscription.features.maxInvoices\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                        lineNumber: 324,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 322,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            currentSubscription.features.maxInvoices !== -1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-full bg-gray-200 rounded-full h-2 mt-1\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-2 rounded-full \".concat(getUsageColor(currentSubscription.usagePercentages.invoices).split(\" \")[1]),\n                                                                    style: {\n                                                                        width: \"\".concat(Math.min(currentSubscription.usagePercentages.invoices, 100), \"%\")\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                    lineNumber: 330,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 329,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                        lineNumber: 321,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between text-sm text-gray-900\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Storage\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                        lineNumber: 340,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            currentSubscription.usage.storageUsed,\n                                                                            \"MB / \",\n                                                                            currentSubscription.features.maxStorage,\n                                                                            \"MB\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                        lineNumber: 341,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 339,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-full bg-gray-200 rounded-full h-2 mt-1\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-2 rounded-full \".concat(getUsageColor(currentSubscription.usagePercentages.storage).split(\" \")[1]),\n                                                                    style: {\n                                                                        width: \"\".concat(Math.min(currentSubscription.usagePercentages.storage, 100), \"%\")\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                    lineNumber: 346,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 345,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                        lineNumber: 338,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                lineNumber: 320,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                        lineNumber: 318,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                lineNumber: 303,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                        lineNumber: 301,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-100 rounded-lg p-1 flex\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setBillingInterval(\"monthly\"),\n                                    className: \"px-4 py-2 text-sm font-medium rounded-md transition-colors \".concat(billingInterval === \"monthly\" ? \"bg-white text-indigo-600 shadow-sm\" : \"text-gray-800 hover:text-gray-900\"),\n                                    children: \"Monthly\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                    lineNumber: 361,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setBillingInterval(\"yearly\"),\n                                    className: \"px-4 py-2 text-sm font-medium rounded-md transition-colors \".concat(billingInterval === \"yearly\" ? \"bg-white text-indigo-600 shadow-sm\" : \"text-gray-800 hover:text-gray-900\"),\n                                    children: [\n                                        \"Yearly\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-1 text-xs bg-green-100 text-green-800 px-1.5 py-0.5 rounded\",\n                                            children: \"Save 17%\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                            lineNumber: 380,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                    lineNumber: 371,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                            lineNumber: 360,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                        lineNumber: 359,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                        children: plans.map((plan)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative bg-white rounded-xl shadow-sm border-2 transition-all \".concat(plan.popular ? \"border-indigo-500 ring-2 ring-indigo-200\" : \"border-gray-200 hover:border-indigo-300\", \" \").concat((currentSubscription === null || currentSubscription === void 0 ? void 0 : currentSubscription.planId) === plan.id ? \"ring-2 ring-green-200 border-green-500\" : \"\"),\n                                children: [\n                                    plan.popular && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -top-3 left-1/2 transform -translate-x-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"bg-indigo-600 text-white px-3 py-1 text-xs font-medium rounded-full\",\n                                            children: \"Most Popular\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                            lineNumber: 400,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                        lineNumber: 399,\n                                        columnNumber: 17\n                                    }, this),\n                                    (currentSubscription === null || currentSubscription === void 0 ? void 0 : currentSubscription.planId) === plan.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -top-3 right-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"bg-green-600 text-white px-3 py-1 text-xs font-medium rounded-full\",\n                                            children: \"Current Plan\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                            lineNumber: 408,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                        lineNumber: 407,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-semibold text-gray-900\",\n                                                children: plan.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                lineNumber: 415,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 mt-2\",\n                                                children: plan.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                lineNumber: 416,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-3xl font-bold text-gray-900\",\n                                                        children: formatPrice(plan.pricing[billingInterval])\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                        lineNumber: 419,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    plan.pricing[billingInterval] > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600 ml-1\",\n                                                        children: [\n                                                            \"/\",\n                                                            billingInterval === \"monthly\" ? \"month\" : \"year\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                        lineNumber: 423,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                lineNumber: 418,\n                                                columnNumber: 17\n                                            }, this),\n                                            billingInterval === \"yearly\" && plan.pricing.yearly > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-green-600 mt-1\",\n                                                children: [\n                                                    \"Save ₹\",\n                                                    ((plan.pricing.monthly * 12 - plan.pricing.yearly) / 100).toLocaleString(\"en-IN\"),\n                                                    \" per year\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                lineNumber: 430,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"mt-6 space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-4 h-4 text-green-500 mr-3\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M5 13l4 4L19 7\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                    lineNumber: 438,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 437,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: [\n                                                                    plan.features.maxInvoices === -1 ? \"Unlimited\" : plan.features.maxInvoices,\n                                                                    \" invoices/month\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 440,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                        lineNumber: 436,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-4 h-4 text-green-500 mr-3\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M5 13l4 4L19 7\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                    lineNumber: 446,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 445,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: [\n                                                                    plan.features.maxStorage,\n                                                                    \"MB storage\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 448,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                        lineNumber: 444,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    plan.features.documentAnalysis && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-4 h-4 text-green-500 mr-3\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M5 13l4 4L19 7\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                    lineNumber: 455,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 454,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: \"Document analysis\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 457,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                        lineNumber: 453,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    plan.features.prioritySupport && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-4 h-4 text-green-500 mr-3\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M5 13l4 4L19 7\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                    lineNumber: 463,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 462,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: \"Priority support\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 465,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                        lineNumber: 461,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    plan.features.apiAccess && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-4 h-4 text-green-500 mr-3\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M5 13l4 4L19 7\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                    lineNumber: 471,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 470,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: \"API access\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 473,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                        lineNumber: 469,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    plan.features.multiUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-4 h-4 text-green-500 mr-3\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M5 13l4 4L19 7\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                    lineNumber: 479,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 478,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: [\n                                                                    \"Up to \",\n                                                                    plan.features.maxUsers,\n                                                                    \" users\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 481,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                        lineNumber: 477,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                lineNumber: 435,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleUpgrade(plan.id),\n                                                disabled: upgrading === plan.id || (currentSubscription === null || currentSubscription === void 0 ? void 0 : currentSubscription.planId) === plan.id,\n                                                className: \"w-full mt-6 px-4 py-2 rounded-lg text-sm font-medium transition-colors \".concat((currentSubscription === null || currentSubscription === void 0 ? void 0 : currentSubscription.planId) === plan.id ? \"bg-green-100 text-green-800 cursor-not-allowed\" : plan.popular ? \"bg-indigo-600 text-white hover:bg-indigo-700 disabled:opacity-50\" : \"bg-gray-100 text-gray-900 hover:bg-gray-200 disabled:opacity-50\"),\n                                                children: upgrading === plan.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"animate-spin -ml-1 mr-3 h-4 w-4\",\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            fill: \"none\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                    className: \"opacity-25\",\n                                                                    cx: \"12\",\n                                                                    cy: \"12\",\n                                                                    r: \"10\",\n                                                                    stroke: \"currentColor\",\n                                                                    strokeWidth: \"4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                    lineNumber: 502,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    className: \"opacity-75\",\n                                                                    fill: \"currentColor\",\n                                                                    d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                    lineNumber: 503,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                            lineNumber: 501,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"Processing...\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                    lineNumber: 500,\n                                                    columnNumber: 21\n                                                }, this) : (currentSubscription === null || currentSubscription === void 0 ? void 0 : currentSubscription.planId) === plan.id ? \"Current Plan\" : plan.id === \"free\" ? \"Downgrade to Free\" : \"Upgrade to \".concat(plan.name)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                lineNumber: 488,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                        lineNumber: 414,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, plan.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                lineNumber: 390,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                        lineNumber: 388,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-xl shadow-sm border border-gray-200 p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold text-gray-900 mb-6\",\n                                children: \"Feature Comparison\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                lineNumber: 522,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"overflow-x-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                    className: \"min-w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                className: \"border-b border-gray-200\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-left py-3 px-4 font-medium text-gray-900\",\n                                                        children: \"Feature\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                        lineNumber: 527,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    plans.map((plan)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-center py-3 px-4 font-medium text-gray-900\",\n                                                            children: plan.name\n                                                        }, plan.id, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                            lineNumber: 529,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                lineNumber: 526,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                            lineNumber: 525,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                            className: \"divide-y divide-gray-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4 text-sm text-gray-900\",\n                                                            children: \"Monthly Invoices\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                            lineNumber: 537,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        plans.map((plan)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"text-center py-3 px-4 text-sm text-gray-900\",\n                                                                children: plan.features.maxInvoices === -1 ? \"∞\" : plan.features.maxInvoices\n                                                            }, plan.id, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 539,\n                                                                columnNumber: 21\n                                                            }, this))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                    lineNumber: 536,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4 text-sm text-gray-900\",\n                                                            children: \"Storage\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                            lineNumber: 545,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        plans.map((plan)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"text-center py-3 px-4 text-sm text-gray-900\",\n                                                                children: [\n                                                                    plan.features.maxStorage,\n                                                                    \"MB\"\n                                                                ]\n                                                            }, plan.id, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 547,\n                                                                columnNumber: 21\n                                                            }, this))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                    lineNumber: 544,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4 text-sm text-gray-900\",\n                                                            children: \"Document Analysis\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                            lineNumber: 553,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        plans.map((plan)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"text-center py-3 px-4\",\n                                                                children: plan.features.documentAnalysis ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-5 h-5 text-green-500 mx-auto\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M5 13l4 4L19 7\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                        lineNumber: 558,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                    lineNumber: 557,\n                                                                    columnNumber: 25\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-5 h-5 text-gray-300 mx-auto\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M6 18L18 6M6 6l12 12\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                        lineNumber: 562,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                    lineNumber: 561,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, plan.id, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 555,\n                                                                columnNumber: 21\n                                                            }, this))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                    lineNumber: 552,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4 text-sm text-gray-900\",\n                                                            children: \"Priority Support\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                            lineNumber: 569,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        plans.map((plan)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"text-center py-3 px-4\",\n                                                                children: plan.features.prioritySupport ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-5 h-5 text-green-500 mx-auto\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M5 13l4 4L19 7\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                        lineNumber: 574,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                    lineNumber: 573,\n                                                                    columnNumber: 25\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-5 h-5 text-gray-300 mx-auto\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M6 18L18 6M6 6l12 12\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                        lineNumber: 578,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                    lineNumber: 577,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, plan.id, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 571,\n                                                                columnNumber: 21\n                                                            }, this))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                    lineNumber: 568,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4 text-sm text-gray-900\",\n                                                            children: \"API Access\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                            lineNumber: 585,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        plans.map((plan)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"text-center py-3 px-4\",\n                                                                children: plan.features.apiAccess ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-5 h-5 text-green-500 mx-auto\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M5 13l4 4L19 7\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                        lineNumber: 590,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                    lineNumber: 589,\n                                                                    columnNumber: 25\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-5 h-5 text-gray-300 mx-auto\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M6 18L18 6M6 6l12 12\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                        lineNumber: 594,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                    lineNumber: 593,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, plan.id, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 587,\n                                                                columnNumber: 21\n                                                            }, this))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                    lineNumber: 584,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                            lineNumber: 535,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                    lineNumber: 524,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                lineNumber: 523,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                        lineNumber: 521,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                lineNumber: 291,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n        lineNumber: 285,\n        columnNumber: 5\n    }, this);\n}\n_s(SubscriptionPage, \"LOYJOYyij6YvjGWMxRhvseJEImk=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = SubscriptionPage;\nvar _c;\n$RefreshReg$(_c, \"SubscriptionPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/subscription/page.tsx\n"));

/***/ })

});