{"version": 3, "file": "seedComplianceData.js", "sourceRoot": "", "sources": ["../../src/scripts/seedComplianceData.ts"], "names": [], "mappings": ";;;;;AAAA,wDAAgC;AAChC,qDAA0E;AAC1E,0DAAkC;AAElC,8BAA8B;AAC9B,MAAM,iBAAiB,GAAG;IACxB;QACE,KAAK,EAAE,4BAA4B;QACnC,WAAW,EAAE,yCAAyC;QACtD,IAAI,EAAE,KAAK;QACX,QAAQ,EAAE,KAAK;QACf,OAAO,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;QAC/B,SAAS,EAAE,SAAS;QACpB,QAAQ,EAAE,MAAM;QAChB,aAAa,EAAE,CAAC,UAAU,EAAE,YAAY,CAAC;QACzC,WAAW,EAAE;YACX,iBAAiB,EAAE,2BAA2B;YAC9C,YAAY,EAAE,eAAe;SAC9B;QACD,SAAS,EAAE;YACT,YAAY,EAAE,yBAAyB;YACvC,UAAU,EAAE,QAAQ;SACrB;QACD,QAAQ,EAAE,IAAI;KACf;IACD;QACE,KAAK,EAAE,6BAA6B;QACpC,WAAW,EAAE,wBAAwB;QACrC,IAAI,EAAE,KAAK;QACX,QAAQ,EAAE,KAAK;QACf,OAAO,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;QAC/B,SAAS,EAAE,SAAS;QACpB,QAAQ,EAAE,MAAM;QAChB,aAAa,EAAE,CAAC,UAAU,EAAE,YAAY,CAAC;QACzC,WAAW,EAAE;YACX,iBAAiB,EAAE,wBAAwB;YAC3C,YAAY,EAAE,eAAe;SAC9B;QACD,SAAS,EAAE;YACT,YAAY,EAAE,yBAAyB;YACvC,UAAU,EAAE,SAAS;SACtB;QACD,QAAQ,EAAE,IAAI;KACf;IACD;QACE,KAAK,EAAE,yBAAyB;QAChC,WAAW,EAAE,0CAA0C;QACvD,IAAI,EAAE,KAAK;QACX,QAAQ,EAAE,KAAK;QACf,OAAO,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;QAC/B,SAAS,EAAE,WAAW;QACtB,QAAQ,EAAE,QAAQ;QAClB,aAAa,EAAE,CAAC,UAAU,CAAC;QAC3B,WAAW,EAAE;YACX,iBAAiB,EAAE,cAAc;YACjC,YAAY,EAAE,gBAAgB;SAC/B;QACD,SAAS,EAAE;YACT,YAAY,EAAE,gCAAgC;YAC9C,UAAU,EAAE,KAAK;SAClB;QACD,QAAQ,EAAE,IAAI;KACf;IACD;QACE,KAAK,EAAE,0BAA0B;QACjC,WAAW,EAAE,0BAA0B;QACvC,IAAI,EAAE,YAAY;QAClB,QAAQ,EAAE,KAAK;QACf,OAAO,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC;QAC/B,SAAS,EAAE,QAAQ;QACnB,QAAQ,EAAE,MAAM;QAChB,aAAa,EAAE,CAAC,YAAY,EAAE,UAAU,EAAE,YAAY,CAAC;QACvD,WAAW,EAAE;YACX,iBAAiB,EAAE,wDAAwD;YAC3E,YAAY,EAAE,cAAc;SAC7B;QACD,SAAS,EAAE;YACT,YAAY,EAAE,gCAAgC;YAC9C,UAAU,EAAE,aAAa;SAC1B;QACD,QAAQ,EAAE,IAAI;KACf;CACF,CAAC;AAEF,KAAK,UAAU,kBAAkB;IAC/B,IAAI,CAAC;QACH,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;QAEtD,qBAAqB;QACrB,MAAM,kBAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,WAAW,IAAI,oCAAoC,CAAC,CAAC;QACxF,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAC;QAEvC,iCAAiC;QACjC,MAAM,+BAAkB,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;QACxC,MAAM,2BAAc,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;QACpC,OAAO,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAC;QAEnD,8BAA8B;QAC9B,MAAM,kBAAkB,GAAG,MAAM,+BAAkB,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC;QAClF,OAAO,CAAC,GAAG,CAAC,aAAa,kBAAkB,CAAC,MAAM,uBAAuB,CAAC,CAAC;QAE3E,2CAA2C;QAC3C,MAAM,KAAK,GAAG,MAAM,cAAI,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;QAClD,OAAO,CAAC,GAAG,CAAC,YAAY,KAAK,CAAC,MAAM,eAAe,CAAC,CAAC;QAErD,+CAA+C;QAC/C,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACzB,MAAM,mBAAmB,GAAG,EAAE,CAAC;YAE/B,KAAK,MAAM,UAAU,IAAI,kBAAkB,EAAE,CAAC;gBAC5C,6CAA6C;gBAC7C,IAAI,WAAW,GAAG,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;gBAC/C,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;gBAEvB,4CAA4C;gBAC5C,IAAI,WAAW,GAAG,GAAG,EAAE,CAAC;oBACtB,QAAQ,UAAU,CAAC,SAAS,EAAE,CAAC;wBAC7B,KAAK,SAAS;4BACZ,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;4BACzC,MAAM;wBACR,KAAK,WAAW;4BACd,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;4BACzC,MAAM;wBACR,KAAK,QAAQ;4BACX,WAAW,CAAC,WAAW,CAAC,GAAG,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC,CAAC;4BAC/C,MAAM;oBACV,CAAC;gBACH,CAAC;gBAED,mBAAmB,CAAC,IAAI,CAAC;oBACvB,MAAM,EAAE,IAAI,CAAC,GAAG;oBAChB,YAAY,EAAE,UAAU,CAAC,GAAG;oBAC5B,SAAS,EAAE,IAAI;oBACf,WAAW,EAAE,KAAK;oBAClB,YAAY,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;oBACvB,WAAW,EAAE,WAAW;oBACxB,iBAAiB,EAAE,IAAI;oBACvB,aAAa,EAAE,IAAI;oBACnB,KAAK,EAAE,EAAE;iBACV,CAAC,CAAC;YACL,CAAC;YAED,MAAM,2BAAc,CAAC,UAAU,CAAC,mBAAmB,CAAC,CAAC;YACrD,OAAO,CAAC,GAAG,CAAC,aAAa,mBAAmB,CAAC,MAAM,+BAA+B,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;QACjG,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,oDAAoD,CAAC,CAAC;IAEpE,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;IAC3D,CAAC;YAAS,CAAC;QACT,MAAM,kBAAQ,CAAC,UAAU,EAAE,CAAC;QAC5B,OAAO,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;IAC9C,CAAC;AACH,CAAC;AAED,iBAAiB;AACjB,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;IAC5B,kBAAkB,EAAE,CAAC;AACvB,CAAC;AAED,kBAAe,kBAAkB,CAAC"}