{"version": 3, "file": "documentRoutes.js", "sourceRoot": "", "sources": ["../../src/routes/documentRoutes.ts"], "names": [], "mappings": ";;;;;AAAA,sDAA8B;AAC9B,6CAAkD;AAClD,+DAAkG;AAClG,0EAc2C;AAE3C,MAAM,MAAM,GAAG,iBAAO,CAAC,MAAM,EAAE,CAAC;AAEhC,oCAAoC;AACpC,MAAM,CAAC,GAAG,CAAC,mBAAY,CAAC,CAAC;AAEzB,8CAA8C;AAC9C,MAAM,CAAC,IAAI,CAAC,SAAS,EACnB,IAAA,+BAAe,EAAC,SAAS,CAAC,EAC1B,2BAAM,CAAC,MAAM,CAAC,UAAU,CAAC,EACzB,mCAAc,EACd,8BAAc,CACf,CAAC;AACF,MAAM,CAAC,IAAI,CAAC,kBAAkB,EAC5B,IAAA,+BAAe,EAAC,SAAS,CAAC,EAC1B,2BAAM,CAAC,KAAK,CAAC,WAAW,EAAE,EAAE,CAAC,EAC7B,4CAAuB,EACvB,8BAAc,CACf,CAAC;AAEF,6BAA6B;AAC7B,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,qCAAgB,CAAC,CAAC;AAClC,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAE,qCAAgB,CAAC,CAAC;AACvC,MAAM,CAAC,GAAG,CAAC,oBAAoB,EAAE,wCAAmB,CAAC,CAAC;AACtD,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE,gCAAW,CAAC,CAAC;AAChC,MAAM,CAAC,GAAG,CAAC,eAAe,EAAE,qCAAgB,CAAC,CAAC;AAC9C,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE,mCAAc,CAAC,CAAC;AACnC,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,mCAAc,CAAC,CAAC;AAEtC,2DAA2D;AAC3D,MAAM,CAAC,IAAI,CAAC,YAAY,EAAE,kCAAa,CAAC,CAAC;AACzC,MAAM,CAAC,IAAI,CAAC,wBAAwB,EAClC,IAAA,kCAAkB,EAAC,WAAW,CAAC,EAC/B,+CAA0B,CAC3B,CAAC;AACF,MAAM,CAAC,IAAI,CAAC,uBAAuB,EACjC,IAAA,kCAAkB,EAAC,WAAW,CAAC,EAC/B,8CAAyB,CAC1B,CAAC;AAEF,kBAAe,MAAM,CAAC"}