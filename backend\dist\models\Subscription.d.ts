import mongoose, { Document, Model } from 'mongoose';
export interface ISubscription extends Document {
    userId: mongoose.Types.ObjectId;
    planId: string;
    planName: string;
    status: 'active' | 'inactive' | 'cancelled' | 'past_due' | 'trialing';
    currentPeriodStart: Date;
    currentPeriodEnd: Date;
    trialStart?: Date;
    trialEnd?: Date;
    cancelledAt?: Date;
    razorpaySubscriptionId?: string;
    razorpayCustomerId?: string;
    amount: number;
    currency: string;
    interval: 'monthly' | 'yearly';
    features: {
        maxInvoices: number;
        maxStorage: number;
        documentAnalysis: boolean;
        prioritySupport: boolean;
        apiAccess: boolean;
        customBranding: boolean;
        multiUser: boolean;
        maxUsers: number;
        advancedReports: boolean;
    };
    usage: {
        invoicesUsed: number;
        storageUsed: number;
        lastResetDate: Date;
    };
    metadata?: Record<string, any>;
    createdAt: Date;
    updatedAt: Date;
}
interface ISubscriptionModel extends Model<ISubscription> {
    getPlanFeatures(planId: string): any;
    getPlanPricing(): any;
}
declare const _default: ISubscriptionModel;
export default _default;
//# sourceMappingURL=Subscription.d.ts.map