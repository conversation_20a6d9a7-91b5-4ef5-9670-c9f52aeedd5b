"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = __importStar(require("mongoose"));
const SubscriptionSchema = new mongoose_1.Schema({
    userId: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: 'User',
        required: true,
        unique: true // One subscription per user
    },
    planId: {
        type: String,
        required: true,
        enum: ['free', 'professional', 'business']
    },
    planName: {
        type: String,
        required: true
    },
    status: {
        type: String,
        enum: ['active', 'inactive', 'cancelled', 'past_due', 'trialing'],
        default: 'active'
    },
    currentPeriodStart: {
        type: Date,
        required: true,
        default: Date.now
    },
    currentPeriodEnd: {
        type: Date,
        required: true
    },
    trialStart: {
        type: Date
    },
    trialEnd: {
        type: Date
    },
    cancelledAt: {
        type: Date
    },
    razorpaySubscriptionId: {
        type: String,
        sparse: true // Allows multiple null values
    },
    razorpayCustomerId: {
        type: String,
        sparse: true
    },
    amount: {
        type: Number,
        required: true,
        default: 0
    },
    currency: {
        type: String,
        default: 'INR'
    },
    interval: {
        type: String,
        enum: ['monthly', 'yearly'],
        default: 'monthly'
    },
    features: {
        maxInvoices: {
            type: Number,
            required: true
        },
        maxStorage: {
            type: Number,
            required: true
        },
        documentAnalysis: {
            type: Boolean,
            default: false
        },
        prioritySupport: {
            type: Boolean,
            default: false
        },
        apiAccess: {
            type: Boolean,
            default: false
        },
        customBranding: {
            type: Boolean,
            default: false
        },
        multiUser: {
            type: Boolean,
            default: false
        },
        maxUsers: {
            type: Number,
            default: 1
        },
        advancedReports: {
            type: Boolean,
            default: false
        }
    },
    usage: {
        invoicesUsed: {
            type: Number,
            default: 0
        },
        storageUsed: {
            type: Number,
            default: 0
        },
        lastResetDate: {
            type: Date,
            default: Date.now
        }
    },
    metadata: {
        type: mongoose_1.Schema.Types.Mixed
    }
}, {
    timestamps: true
});
// Indexes for better query performance
SubscriptionSchema.index({ userId: 1 });
SubscriptionSchema.index({ status: 1 });
SubscriptionSchema.index({ planId: 1 });
SubscriptionSchema.index({ currentPeriodEnd: 1 });
// Instance methods
SubscriptionSchema.methods.isActive = function () {
    return this.status === 'active' || this.status === 'trialing';
};
SubscriptionSchema.methods.isTrialing = function () {
    return this.status === 'trialing' &&
        this.trialEnd &&
        new Date() < this.trialEnd;
};
SubscriptionSchema.methods.hasFeature = function (feature) {
    return this.features[feature] === true;
};
SubscriptionSchema.methods.canCreateInvoice = function () {
    return this.usage.invoicesUsed < this.features.maxInvoices;
};
SubscriptionSchema.methods.incrementUsage = function (type, amount = 1) {
    switch (type) {
        case 'invoices':
            this.usage.invoicesUsed += amount;
            break;
        case 'storage':
            this.usage.storageUsed += amount;
            break;
    }
    return this.save();
};
SubscriptionSchema.methods.resetMonthlyUsage = function () {
    this.usage.invoicesUsed = 0;
    this.usage.storageUsed = 0;
    return this.save();
};
// Static methods for plan definitions
SubscriptionSchema.statics.getPlanFeatures = function (planId) {
    const plans = {
        free: {
            maxInvoices: 5,
            maxStorage: 100, // 100MB
            documentAnalysis: false,
            prioritySupport: false,
            apiAccess: false,
            customBranding: false,
            multiUser: false,
            maxUsers: 1,
            advancedReports: false
        },
        professional: {
            maxInvoices: -1, // Unlimited
            maxStorage: 5120, // 5GB
            documentAnalysis: true,
            prioritySupport: true,
            apiAccess: false,
            customBranding: false,
            multiUser: false,
            maxUsers: 1,
            advancedReports: true
        },
        business: {
            maxInvoices: -1, // Unlimited
            maxStorage: 20480, // 20GB
            documentAnalysis: true,
            prioritySupport: true,
            apiAccess: true,
            customBranding: true,
            multiUser: true,
            maxUsers: 5,
            advancedReports: true
        }
    };
    return plans[planId];
};
SubscriptionSchema.statics.getPlanPricing = function () {
    return {
        free: {
            monthly: 0,
            yearly: 0
        },
        professional: {
            monthly: 99900, // ₹999 in paise
            yearly: 999000 // ₹9990 (2 months free)
        },
        business: {
            monthly: 299900, // ₹2999 in paise
            yearly: 2999000 // ₹29990 (2 months free)
        }
    };
};
// Instance methods
SubscriptionSchema.methods.hasFeatureAccess = function (feature) {
    return this.features[feature] === true;
};
SubscriptionSchema.methods.isActive = function () {
    return this.status === 'active' || this.status === 'trialing';
};
exports.default = mongoose_1.default.model('Subscription', SubscriptionSchema);
//# sourceMappingURL=Subscription.js.map