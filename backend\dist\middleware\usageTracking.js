"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.addSubscriptionInfo = exports.checkFeatureAccess = exports.incrementUsage = exports.checkUsageLimit = void 0;
const subscriptionService_1 = __importDefault(require("../services/subscriptionService"));
// Middleware to check and track usage limits
const checkUsageLimit = (action) => {
    return async (req, res, next) => {
        try {
            const userId = req.user?._id;
            if (!userId) {
                return res.status(401).json({
                    success: false,
                    message: 'User not authenticated'
                });
            }
            // Get amount from request body or default to 1
            let amount = 1;
            if (action === 'storage') {
                if (req.file) {
                    amount = Math.ceil(req.file.size / (1024 * 1024)); // Convert bytes to MB
                }
                else if (req.files && Array.isArray(req.files)) {
                    // Handle multiple files
                    amount = req.files.reduce((total, file) => {
                        return total + Math.ceil(file.size / (1024 * 1024));
                    }, 0);
                }
            }
            // Check if user can perform the action
            const canPerform = await subscriptionService_1.default.checkUsageLimit(userId.toString(), action, amount);
            if (!canPerform) {
                const subscription = await subscriptionService_1.default.getSubscriptionWithUsage(userId.toString());
                let message = '';
                let upgradeRequired = false;
                switch (action) {
                    case 'invoice':
                        message = `Invoice limit reached. You have used ${subscription?.usage.invoicesUsed}/${subscription?.features.maxInvoices} invoices this month.`;
                        upgradeRequired = subscription?.planId === 'free';
                        break;
                    case 'storage':
                        message = `Storage limit reached. You have used ${subscription?.usage.storageUsed}MB/${subscription?.features.maxStorage}MB.`;
                        upgradeRequired = true;
                        break;
                }
                return res.status(403).json({
                    success: false,
                    message,
                    code: 'USAGE_LIMIT_EXCEEDED',
                    data: {
                        action,
                        currentUsage: subscription?.usage,
                        limits: subscription?.features,
                        upgradeRequired,
                        currentPlan: subscription?.planId
                    }
                });
            }
            // Store action type for post-processing (map to service types)
            const serviceTypeMap = {
                'invoice': 'invoices',
                'storage': 'storage'
            };
            req.usageAction = { type: serviceTypeMap[action], amount };
            next();
        }
        catch (error) {
            console.error('Usage limit check error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to check usage limits'
            });
        }
    };
};
exports.checkUsageLimit = checkUsageLimit;
// Middleware to increment usage after successful action
const incrementUsage = async (req, res, next) => {
    try {
        const userId = req.user?._id;
        const usageAction = req.usageAction;
        if (userId && usageAction) {
            await subscriptionService_1.default.incrementUsage(userId.toString(), usageAction.type, usageAction.amount);
        }
        next();
    }
    catch (error) {
        console.error('Usage increment error:', error);
        // Don't fail the request if usage tracking fails
        next();
    }
};
exports.incrementUsage = incrementUsage;
// Middleware to check feature access
const checkFeatureAccess = (feature) => {
    return async (req, res, next) => {
        try {
            const userId = req.user?._id;
            if (!userId) {
                return res.status(401).json({
                    success: false,
                    message: 'User not authenticated'
                });
            }
            const subscription = await subscriptionService_1.default.getSubscriptionWithUsage(userId.toString());
            if (!subscription) {
                return res.status(403).json({
                    success: false,
                    message: 'No subscription found',
                    code: 'NO_SUBSCRIPTION'
                });
            }
            if (!subscriptionService_1.default.isSubscriptionActive(subscription)) {
                return res.status(403).json({
                    success: false,
                    message: 'No active subscription found',
                    code: 'NO_ACTIVE_SUBSCRIPTION'
                });
            }
            // Check if user has access to the feature
            const hasAccess = subscription.features && subscription.features[feature];
            if (!hasAccess) {
                return res.status(403).json({
                    success: false,
                    message: `Feature '${feature}' not available in your current plan`,
                    code: 'FEATURE_NOT_AVAILABLE',
                    data: {
                        feature,
                        currentPlan: subscription.planId,
                        upgradeRequired: true
                    }
                });
            }
            next();
        }
        catch (error) {
            console.error('Feature access check error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to check feature access'
            });
        }
    };
};
exports.checkFeatureAccess = checkFeatureAccess;
// Middleware to add subscription info to response
const addSubscriptionInfo = async (req, res, next) => {
    try {
        const userId = req.user?._id;
        if (userId) {
            const subscription = await subscriptionService_1.default.getSubscriptionWithUsage(userId.toString());
            req.userSubscription = subscription;
        }
        next();
    }
    catch (error) {
        console.error('Add subscription info error:', error);
        next(); // Continue without subscription info
    }
};
exports.addSubscriptionInfo = addSubscriptionInfo;
//# sourceMappingURL=usageTracking.js.map