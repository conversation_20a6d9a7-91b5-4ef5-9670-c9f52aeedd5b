"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const dotenv_1 = __importDefault(require("dotenv"));
const path_1 = __importDefault(require("path"));
const database_1 = __importDefault(require("./config/database"));
// Temporarily commented out for compilation
// import { getSecurityConfig } from './config/security';
// import {
//   rateLimiters,
//   speedLimiter,
//   securityHeaders,
//   requestLogger,
//   suspiciousActivityDetection
// } from './middleware/security';
// import { sanitizeInput } from './middleware/inputSanitization';
// import { securityMonitoring } from './services/securityMonitoringService';
// Load environment variables FIRST
dotenv_1.default.config({ path: path_1.default.join(__dirname, '../.env') });
// Import routes AFTER environment variables are loaded
const authRoutes_1 = __importDefault(require("./routes/authRoutes"));
const adminRoutes_1 = __importDefault(require("./routes/adminRoutes"));
const invoiceRoutes_1 = __importDefault(require("./routes/invoiceRoutes"));
// Removed: import chatRoutes from './routes/chatRoutes';
const complianceRoutes_1 = __importDefault(require("./routes/complianceRoutes"));
const notificationRoutes_1 = __importDefault(require("./routes/notificationRoutes"));
const documentRoutes_1 = __importDefault(require("./routes/documentRoutes"));
const subscriptionRoutes_1 = __importDefault(require("./routes/subscriptionRoutes"));
const uploadRoutes_1 = __importDefault(require("./routes/uploadRoutes"));
const paymentRoutes_1 = __importDefault(require("./routes/paymentRoutes"));
const securePaymentRoutes_1 = __importDefault(require("./routes/securePaymentRoutes"));
const otpRoutes_1 = __importDefault(require("./routes/otpRoutes"));
const paymentReminders_1 = __importDefault(require("./routes/paymentReminders"));
const analytics_1 = __importDefault(require("./routes/analytics"));
const team_1 = __importDefault(require("./routes/team"));
const apiKeys_1 = __importDefault(require("./routes/apiKeys"));
const invoices_1 = __importDefault(require("./routes/api/v1/invoices"));
const recurringInvoiceRoutes_1 = __importDefault(require("./routes/recurringInvoiceRoutes"));
const invoiceTemplateRoutes_1 = __importDefault(require("./routes/invoiceTemplateRoutes"));
const batchOperationsRoutes_1 = __importDefault(require("./routes/batchOperationsRoutes"));
// Removed: import modelTrainingRoutes from './routes/modelTrainingRoutes';
// import securityRoutes from './routes/securityRoutes';
// Import services AFTER environment variables are loaded
const schedulerService_1 = __importDefault(require("./services/schedulerService"));
const paymentReminderService_1 = __importDefault(require("./services/paymentReminderService"));
const recurringInvoiceService_1 = __importDefault(require("./services/recurringInvoiceService"));
const enhancedAnalyticsService_1 = require("./services/enhancedAnalyticsService");
const emailQueueService_1 = __importDefault(require("./services/emailQueueService"));
const securePaymentService_1 = __importDefault(require("./services/securePaymentService"));
// Debug: Log environment loading
console.log('=== ENVIRONMENT LOADING DEBUG ===');
console.log('Current working directory:', process.cwd());
console.log('__dirname:', __dirname);
console.log('Trying to load .env from:', path_1.default.join(__dirname, '../.env'));
console.log('NODE_ENV:', process.env.NODE_ENV);
console.log('MAIL_USER loaded:', process.env.MAIL_USER ? 'YES' : 'NO');
console.log('MAIL_PASS loaded:', process.env.MAIL_PASS ? 'YES' : 'NO');
console.log('EMAIL_USER loaded:', process.env.EMAIL_USER ? 'YES' : 'NO');
console.log('EMAIL_PASS loaded:', process.env.EMAIL_PASS ? 'YES' : 'NO');
console.log('RAZORPAY_KEY_ID loaded:', process.env.RAZORPAY_KEY_ID ? 'YES' : 'NO');
console.log('RAZORPAY_KEY_SECRET loaded:', process.env.RAZORPAY_KEY_SECRET ? 'YES' : 'NO');
if (process.env.RAZORPAY_KEY_ID) {
    console.log('RAZORPAY_KEY_ID value:', process.env.RAZORPAY_KEY_ID);
}
console.log('==================================');
const app = (0, express_1.default)();
const PORT = process.env.PORT || 5000;
// const securityConfig = getSecurityConfig();
// Connect to MongoDB
(0, database_1.default)();
// Temporarily disabled security middleware for compilation
// Security Middleware (Applied First)
// app.use(securityHeaders);
// app.use(requestLogger);
// app.use(suspiciousActivityDetection);
// app.use(sanitizeInput);
// Rate limiting
// app.use('/api/auth', rateLimiters.auth);
// app.use('/api/otp', rateLimiters.otp);
// app.use('/api/v1', rateLimiters.api);
// app.use(rateLimiters.general);
// app.use(speedLimiter);
// Enhanced Helmet configuration
app.use((0, helmet_1.default)({
    crossOriginResourcePolicy: { policy: "cross-origin" },
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'"],
            scriptSrc: ["'self'"],
            imgSrc: ["'self'", "data:", "https:"],
            connectSrc: ["'self'"],
            fontSrc: ["'self'"],
            objectSrc: ["'none'"],
            mediaSrc: ["'self'"],
            frameSrc: ["'none'"],
        },
    },
    hsts: {
        maxAge: 31536000,
        includeSubDomains: true,
        preload: true
    }
}));
// Enhanced CORS configuration
app.use((0, cors_1.default)({
    origin: process.env.FRONTEND_URL || 'http://localhost:3000',
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization']
}));
// Body parsing with size limits
app.use(express_1.default.json({ limit: '10mb' }));
app.use(express_1.default.urlencoded({ extended: true, limit: '10mb' }));
// Routes
app.get('/api/health', (req, res) => {
    res.json({
        message: 'InvoNest API is running!',
        timestamp: new Date().toISOString(),
        version: '1.0.0'
    });
});
// Authentication routes
app.use('/api/auth', authRoutes_1.default);
// Admin routes
app.use('/api/admin', adminRoutes_1.default);
// OTP routes
app.use('/api/otp', otpRoutes_1.default);
// Invoice routes
app.use('/api/invoices', invoiceRoutes_1.default);
// Compliance routes
app.use('/api/compliance', complianceRoutes_1.default);
// Notification routes
app.use('/api/notifications', notificationRoutes_1.default);
// Document routes
app.use('/api/documents', documentRoutes_1.default);
// Subscription routes
app.use('/api/subscriptions', subscriptionRoutes_1.default);
// Payment reminder routes
app.use('/api/payment-reminders', paymentReminders_1.default);
// Invoice payment routes
app.use('/api/payments', paymentRoutes_1.default);
// Secure payment routes
app.use('/api/secure-payments', securePaymentRoutes_1.default);
// Analytics routes
app.use('/api/analytics', analytics_1.default);
// Team management routes
app.use('/api/team', team_1.default);
// API key management routes
app.use('/api/api-keys', apiKeys_1.default);
// Public API v1 routes
app.use('/api/v1/invoices', invoices_1.default);
// Automation routes
app.use('/api/recurring-invoices', recurringInvoiceRoutes_1.default);
app.use('/api/invoice-templates', invoiceTemplateRoutes_1.default);
app.use('/api/batch-operations', batchOperationsRoutes_1.default);
// Security management routes (temporarily disabled)
// app.use('/api/security', securityRoutes);
// Serve static files from uploads directory (before upload routes to avoid conflicts)
// Serve at both /api/uploads/ and /uploads/ for compatibility
app.use('/api/uploads/logos', (req, res, next) => {
    res.header('Access-Control-Allow-Origin', process.env.FRONTEND_URL || 'http://localhost:3000');
    res.header('Access-Control-Allow-Methods', 'GET');
    res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    next();
}, express_1.default.static(path_1.default.join(process.cwd(), 'uploads', 'logos')));
app.use('/uploads/logos', (req, res, next) => {
    res.header('Access-Control-Allow-Origin', process.env.FRONTEND_URL || 'http://localhost:3000');
    res.header('Access-Control-Allow-Methods', 'GET');
    res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    next();
}, express_1.default.static(path_1.default.join(process.cwd(), 'uploads', 'logos')));
app.use('/api/uploads/documents', (req, res, next) => {
    res.header('Access-Control-Allow-Origin', process.env.FRONTEND_URL || 'http://localhost:3000');
    res.header('Access-Control-Allow-Methods', 'GET');
    res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    next();
}, express_1.default.static(path_1.default.join(process.cwd(), 'uploads', 'documents')));
app.use('/uploads/documents', (req, res, next) => {
    res.header('Access-Control-Allow-Origin', process.env.FRONTEND_URL || 'http://localhost:3000');
    res.header('Access-Control-Allow-Methods', 'GET');
    res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    next();
}, express_1.default.static(path_1.default.join(process.cwd(), 'uploads', 'documents')));
// Upload routes
app.use('/api/uploads', uploadRoutes_1.default);
// Error handling middleware
app.use((err, req, res, next) => {
    console.error(err.stack);
    res.status(500).json({
        message: 'Something went wrong!',
        error: process.env.NODE_ENV === 'development' ? err.message : 'Internal server error'
    });
});
// 404 handler
app.use('*', (req, res) => {
    res.status(404).json({ message: 'Route not found' });
});
app.listen(PORT, () => {
    console.log(`🚀 InvoNest server running on port ${PORT}`);
    console.log(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);
    // Start the scheduler service
    schedulerService_1.default.start();
    // Initialize payment reminder service
    paymentReminderService_1.default.getInstance();
    console.log('💰 Payment reminder service initialized');
    // Initialize recurring invoice service
    recurringInvoiceService_1.default.getInstance();
    console.log('🔄 Recurring invoice service initialized');
    // Initialize enhanced analytics service
    enhancedAnalyticsService_1.EnhancedAnalyticsService.getInstance();
    console.log('📊 Enhanced analytics service initialized');
    // Initialize email queue service
    emailQueueService_1.default.getInstance();
    console.log('📮 Email queue service initialized');
    // Schedule cleanup of expired payment tokens (every hour)
    setInterval(async () => {
        try {
            await securePaymentService_1.default.cleanupExpiredTokens();
        }
        catch (error) {
            console.error('Error cleaning up expired payment tokens:', error);
        }
    }, 60 * 60 * 1000); // 1 hour
    console.log('🔐 Secure payment token cleanup scheduled');
});
exports.default = app;
//# sourceMappingURL=index.js.map