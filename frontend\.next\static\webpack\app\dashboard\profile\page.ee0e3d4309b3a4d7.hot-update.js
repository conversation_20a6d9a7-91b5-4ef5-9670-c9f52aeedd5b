"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/profile/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/profile/page.tsx":
/*!********************************************!*\
  !*** ./src/app/dashboard/profile/page.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ProfilePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../lib/auth */ \"(app-pages-browser)/./src/lib/auth.ts\");\n/* harmony import */ var _components_dashboard_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../components/dashboard/DashboardLayout */ \"(app-pages-browser)/./src/components/dashboard/DashboardLayout.tsx\");\n/* harmony import */ var _components_FileUpload__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../components/FileUpload */ \"(app-pages-browser)/./src/components/FileUpload.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction ProfilePage() {\n    var _formData_address, _formData_address1, _formData_address2, _formData_address3, _formData_address4;\n    _s();\n    const { user, updateUser } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [editing, setEditing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [saving, setSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        type: \"\",\n        text: \"\"\n    });\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [verificationLoading, setVerificationLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [verificationMessage, setVerificationMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [logoUploading, setLogoUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [logoPreview, setLogoPreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [signatureUploading, setSignatureUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [signaturePreview, setSignaturePreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (user) {\n            setFormData({\n                ...user,\n                address: user.address || {\n                    street: \"\",\n                    city: \"\",\n                    state: \"\",\n                    pincode: \"\",\n                    country: \"India\"\n                }\n            });\n            // Set logo preview if user has a logo\n            if (user.logo) {\n                // Handle both full paths and just filenames\n                const logoPath = user.logo.includes(\"/\") ? user.logo : \"uploads/logos/\".concat(user.logo);\n                setLogoPreview(\"http://localhost:5000/\".concat(logoPath));\n            }\n            // Set signature preview if user has a signature\n            if (user.signature) {\n                // Handle both full paths and just filenames\n                const signaturePath = user.signature.includes(\"/\") ? user.signature : \"uploads/signatures/\".concat(user.signature);\n                setSignaturePreview(\"http://localhost:5000/\".concat(signaturePath));\n            }\n        }\n    }, [\n        user\n    ]);\n    const handleInputChange = (e)=>{\n        const { name, value } = e.target;\n        // Handle nested address fields\n        if ([\n            \"street\",\n            \"city\",\n            \"state\",\n            \"pincode\",\n            \"country\"\n        ].includes(name)) {\n            setFormData((prev)=>({\n                    ...prev,\n                    address: {\n                        ...prev.address,\n                        [name]: value\n                    }\n                }));\n        } else {\n            setFormData((prev)=>({\n                    ...prev,\n                    [name]: value\n                }));\n        }\n    };\n    const handleLogoUpload = async (files)=>{\n        if (files.length === 0) return;\n        const file = files[0];\n        setLogoUploading(true);\n        setMessage({\n            type: \"\",\n            text: \"\"\n        });\n        try {\n            const formData = new FormData();\n            formData.append(\"logo\", file);\n            const token = localStorage.getItem(\"token\");\n            const response = await fetch(\"http://localhost:5000/api/uploads/logo\", {\n                method: \"POST\",\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(token)\n                },\n                body: formData\n            });\n            const data = await response.json();\n            if (data.success) {\n                // Update form data with new logo path\n                setFormData((prev)=>({\n                        ...prev,\n                        logo: data.data.logoPath\n                    }));\n                // Set preview\n                setLogoPreview(\"http://localhost:5000/\".concat(data.data.logoPath));\n                // Update user context with new logo\n                if (user) {\n                    updateUser({\n                        ...user,\n                        logo: data.data.logoPath\n                    });\n                }\n                setMessage({\n                    type: \"success\",\n                    text: \"Logo uploaded successfully!\"\n                });\n            } else {\n                setMessage({\n                    type: \"error\",\n                    text: data.message || \"Failed to upload logo\"\n                });\n            }\n        } catch (error) {\n            console.error(\"Logo upload error:\", error);\n            setMessage({\n                type: \"error\",\n                text: \"Failed to upload logo\"\n            });\n        } finally{\n            setLogoUploading(false);\n        }\n    };\n    const handleRemoveLogo = async ()=>{\n        setLogoUploading(true);\n        try {\n            const token = localStorage.getItem(\"token\");\n            const response = await fetch(\"http://localhost:5000/api/uploads/logo\", {\n                method: \"DELETE\",\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(token)\n                }\n            });\n            const data = await response.json();\n            if (data.success) {\n                setFormData((prev)=>({\n                        ...prev,\n                        logo: null\n                    }));\n                setLogoPreview(null);\n                // Update user context to remove logo\n                if (user) {\n                    updateUser({\n                        ...user,\n                        logo: undefined\n                    });\n                }\n                setMessage({\n                    type: \"success\",\n                    text: \"Logo removed successfully!\"\n                });\n            } else {\n                setMessage({\n                    type: \"error\",\n                    text: data.message || \"Failed to remove logo\"\n                });\n            }\n        } catch (error) {\n            console.error(\"Logo remove error:\", error);\n            setMessage({\n                type: \"error\",\n                text: \"Failed to remove logo\"\n            });\n        } finally{\n            setLogoUploading(false);\n        }\n    };\n    const handleSignatureUpload = async (files)=>{\n        if (files.length === 0) return;\n        const file = files[0];\n        setSignatureUploading(true);\n        setMessage({\n            type: \"\",\n            text: \"\"\n        });\n        try {\n            const formData = new FormData();\n            formData.append(\"signature\", file);\n            const token = localStorage.getItem(\"token\");\n            const response = await fetch(\"http://localhost:5000/api/uploads/signature\", {\n                method: \"POST\",\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(token)\n                },\n                body: formData\n            });\n            const data = await response.json();\n            if (data.success) {\n                // Update form data with new signature path\n                setFormData((prev)=>({\n                        ...prev,\n                        signature: data.data.signaturePath\n                    }));\n                // Set preview\n                setSignaturePreview(\"http://localhost:5000/\".concat(data.data.signaturePath));\n                // Update user context with new signature\n                if (user) {\n                    updateUser({\n                        ...user,\n                        signature: data.data.signaturePath\n                    });\n                }\n                setMessage({\n                    type: \"success\",\n                    text: \"Signature uploaded successfully!\"\n                });\n            } else {\n                setMessage({\n                    type: \"error\",\n                    text: data.message || \"Failed to upload signature\"\n                });\n            }\n        } catch (error) {\n            console.error(\"Signature upload error:\", error);\n            setMessage({\n                type: \"error\",\n                text: \"Failed to upload signature\"\n            });\n        } finally{\n            setSignatureUploading(false);\n        }\n    };\n    const handleRemoveSignature = async ()=>{\n        setSignatureUploading(true);\n        try {\n            const token = localStorage.getItem(\"token\");\n            const response = await fetch(\"http://localhost:5000/api/uploads/signature\", {\n                method: \"DELETE\",\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(token)\n                }\n            });\n            const data = await response.json();\n            if (data.success) {\n                setFormData((prev)=>({\n                        ...prev,\n                        signature: null\n                    }));\n                setSignaturePreview(null);\n                // Update user context to remove signature\n                if (user) {\n                    updateUser({\n                        ...user,\n                        signature: undefined\n                    });\n                }\n                setMessage({\n                    type: \"success\",\n                    text: \"Signature removed successfully!\"\n                });\n            } else {\n                setMessage({\n                    type: \"error\",\n                    text: data.message || \"Failed to remove signature\"\n                });\n            }\n        } catch (error) {\n            console.error(\"Signature remove error:\", error);\n            setMessage({\n                type: \"error\",\n                text: \"Failed to remove signature\"\n            });\n        } finally{\n            setSignatureUploading(false);\n        }\n    };\n    const handleSave = async ()=>{\n        setSaving(true);\n        setMessage({\n            type: \"\",\n            text: \"\"\n        });\n        try {\n            var _formData_name, _formData_businessName, _formData_gstNumber, _formData_phone, _formData_address_street, _formData_address, _formData_address_city, _formData_address1, _formData_address_state, _formData_address2, _formData_address_pincode, _formData_address3, _formData_address_country, _formData_address4;\n            // Clean up the form data before sending\n            const cleanFormData = {};\n            // Only include non-empty fields\n            if ((_formData_name = formData.name) === null || _formData_name === void 0 ? void 0 : _formData_name.trim()) {\n                cleanFormData.name = formData.name.trim();\n            }\n            if ((_formData_businessName = formData.businessName) === null || _formData_businessName === void 0 ? void 0 : _formData_businessName.trim()) {\n                cleanFormData.businessName = formData.businessName.trim();\n            }\n            if ((_formData_gstNumber = formData.gstNumber) === null || _formData_gstNumber === void 0 ? void 0 : _formData_gstNumber.trim()) {\n                cleanFormData.gstNumber = formData.gstNumber.trim();\n            }\n            if ((_formData_phone = formData.phone) === null || _formData_phone === void 0 ? void 0 : _formData_phone.trim()) {\n                cleanFormData.phone = formData.phone.trim();\n            }\n            // Handle address separately\n            const address = {};\n            if ((_formData_address = formData.address) === null || _formData_address === void 0 ? void 0 : (_formData_address_street = _formData_address.street) === null || _formData_address_street === void 0 ? void 0 : _formData_address_street.trim()) {\n                address.street = formData.address.street.trim();\n            }\n            if ((_formData_address1 = formData.address) === null || _formData_address1 === void 0 ? void 0 : (_formData_address_city = _formData_address1.city) === null || _formData_address_city === void 0 ? void 0 : _formData_address_city.trim()) {\n                address.city = formData.address.city.trim();\n            }\n            if ((_formData_address2 = formData.address) === null || _formData_address2 === void 0 ? void 0 : (_formData_address_state = _formData_address2.state) === null || _formData_address_state === void 0 ? void 0 : _formData_address_state.trim()) {\n                address.state = formData.address.state.trim();\n            }\n            if ((_formData_address3 = formData.address) === null || _formData_address3 === void 0 ? void 0 : (_formData_address_pincode = _formData_address3.pincode) === null || _formData_address_pincode === void 0 ? void 0 : _formData_address_pincode.trim()) {\n                address.pincode = formData.address.pincode.trim();\n            }\n            if ((_formData_address4 = formData.address) === null || _formData_address4 === void 0 ? void 0 : (_formData_address_country = _formData_address4.country) === null || _formData_address_country === void 0 ? void 0 : _formData_address_country.trim()) {\n                address.country = formData.address.country.trim();\n            }\n            // Only include address if it has at least one field\n            if (Object.keys(address).length > 0) {\n                cleanFormData.address = address;\n            }\n            // Include logo if it exists\n            if (formData.logo) {\n                cleanFormData.logo = formData.logo;\n            }\n            console.log(\"Sending profile data:\", cleanFormData);\n            const response = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.updateProfile)(cleanFormData);\n            if (response.success && response.data) {\n                updateUser(response.data.user);\n                setEditing(false);\n                setMessage({\n                    type: \"success\",\n                    text: \"Profile updated successfully!\"\n                });\n            } else {\n                console.error(\"Profile update failed:\", response);\n                const errorMessage = response.errors ? response.errors.join(\", \") : response.message || \"Failed to update profile\";\n                setMessage({\n                    type: \"error\",\n                    text: errorMessage\n                });\n            }\n        } catch (error) {\n            console.error(\"Profile update error:\", error);\n            setMessage({\n                type: \"error\",\n                text: \"Failed to update profile\"\n            });\n        } finally{\n            setSaving(false);\n        }\n    };\n    const handleCancel = ()=>{\n        if (user) {\n            setFormData({\n                ...user,\n                address: user.address || {\n                    street: \"\",\n                    city: \"\",\n                    state: \"\",\n                    pincode: \"\",\n                    country: \"India\"\n                }\n            });\n        }\n        setEditing(false);\n        setMessage({\n            type: \"\",\n            text: \"\"\n        });\n    };\n    const sendVerificationEmail = async ()=>{\n        setVerificationLoading(true);\n        setVerificationMessage(\"\");\n        try {\n            const response = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.authenticatedFetch)(\"/api/auth/send-verification\", {\n                method: \"POST\"\n            });\n            const data = await response.json();\n            if (data.success) {\n                if (data.developmentToken) {\n                    // Development mode - show direct verification link\n                    const verificationUrl = \"\".concat(window.location.origin, \"/verify-email?token=\").concat(data.developmentToken);\n                    setVerificationMessage(\"development_link:\".concat(verificationUrl));\n                } else {\n                    setVerificationMessage(\"Verification email sent! Please check your inbox.\");\n                }\n            } else {\n                setVerificationMessage(data.message || \"Failed to send verification email.\");\n            }\n        } catch (error) {\n            console.error(\"Send verification error:\", error);\n            setVerificationMessage(\"Network error. Please try again.\");\n        } finally{\n            setVerificationLoading(false);\n        }\n    };\n    if (!user) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        title: \"Profile\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__.DashboardHeader, {\n                title: \"Profile\",\n                subtitle: \"Manage your account information and business details\",\n                actions: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [\n                        editing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleCancel,\n                            className: \"px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-800 bg-white hover:bg-gray-50 transition-colors\",\n                            disabled: saving,\n                            children: \"Cancel\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                            lineNumber: 341,\n                            columnNumber: 15\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>editing ? handleSave() : setEditing(true),\n                            className: \"px-4 py-2 bg-indigo-600 text-white rounded-lg text-sm font-medium hover:bg-indigo-700 transition-colors disabled:opacity-50\",\n                            disabled: saving,\n                            children: saving ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full mr-2 inline-block\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                        lineNumber: 356,\n                                        columnNumber: 19\n                                    }, void 0),\n                                    \"Saving...\"\n                                ]\n                            }, void 0, true) : editing ? \"Save Changes\" : \"Edit Profile\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                            lineNumber: 349,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                    lineNumber: 339,\n                    columnNumber: 11\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                lineNumber: 335,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-4xl mx-auto p-4 lg:p-8\",\n                children: [\n                    message.text && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6 p-4 rounded-lg \".concat(message.type === \"success\" ? \"bg-green-50 border border-green-200 text-green-800\" : \"bg-red-50 border border-red-200 text-red-800\"),\n                        children: message.text\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                        lineNumber: 368,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row items-center space-y-4 sm:space-y-0 sm:space-x-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-24 h-24 rounded-full overflow-hidden border-2 border-gray-200 bg-gradient-to-br from-indigo-500 to-purple-600 flex items-center justify-center\",\n                                    children: logoPreview ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: logoPreview,\n                                        alt: \"Business Logo\",\n                                        className: \"w-full h-full object-cover\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                        lineNumber: 382,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white text-3xl font-bold\",\n                                        children: user.name.charAt(0).toUpperCase()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                        lineNumber: 388,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                    lineNumber: 380,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center sm:text-left\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-2xl lg:text-3xl font-bold text-gray-900\",\n                                            children: user.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                            lineNumber: 394,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-800 mt-1\",\n                                            children: user.email\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                            lineNumber: 395,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col sm:flex-row items-center sm:items-start space-y-2 sm:space-y-0 sm:space-x-3 mt-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat(user.isEmailVerified ? \"bg-green-100 text-green-800\" : \"bg-yellow-100 text-yellow-800\"),\n                                                    children: user.isEmailVerified ? \"✓ Email Verified\" : \"⚠ Email Not Verified\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                    lineNumber: 397,\n                                                    columnNumber: 17\n                                                }, this),\n                                                !user.isEmailVerified && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: sendVerificationEmail,\n                                                    disabled: verificationLoading,\n                                                    className: \"inline-flex items-center px-3 py-1 text-xs font-medium text-indigo-600 bg-indigo-50 border border-indigo-200 rounded-full hover:bg-indigo-100 transition-colors disabled:opacity-50\",\n                                                    children: verificationLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"animate-spin h-3 w-3 border border-indigo-600 border-t-transparent rounded-full mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 408,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Sending...\"\n                                                        ]\n                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-3 h-3 mr-1\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                    lineNumber: 414,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 413,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Verify Email\"\n                                                        ]\n                                                    }, void 0, true)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                    lineNumber: 401,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                            lineNumber: 396,\n                                            columnNumber: 15\n                                        }, this),\n                                        verificationMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-2 p-2 rounded-md text-xs \".concat(verificationMessage.includes(\"sent\") || verificationMessage.includes(\"development_link\") ? \"bg-green-50 text-green-700 border border-green-200\" : \"bg-red-50 text-red-700 border border-red-200\"),\n                                            children: verificationMessage.startsWith(\"development_link:\") ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"mb-2\",\n                                                        children: \"\\uD83D\\uDCE7 Email service not configured (Development Mode)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 430,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: verificationMessage.replace(\"development_link:\", \"\"),\n                                                        className: \"inline-flex items-center px-3 py-1 bg-green-600 text-white text-xs rounded-md hover:bg-green-700 transition-colors\",\n                                                        target: \"_blank\",\n                                                        rel: \"noopener noreferrer\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-3 h-3 mr-1\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M5 13l4 4L19 7\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                    lineNumber: 438,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 437,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Click to Verify Email\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 431,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 429,\n                                                columnNumber: 21\n                                            }, this) : verificationMessage\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                            lineNumber: 423,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                    lineNumber: 393,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                            lineNumber: 379,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                        lineNumber: 378,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-xl shadow-sm border border-gray-200 p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold text-gray-900 mb-6\",\n                                children: \"Profile Information\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                lineNumber: 454,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-gray-900 border-b border-gray-200 pb-2\",\n                                                children: \"Personal Information\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 459,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-800 mb-2\",\n                                                        children: \"Full Name\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 464,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        name: \"name\",\n                                                        value: formData.name || \"\",\n                                                        onChange: handleInputChange,\n                                                        disabled: !editing,\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-gray-900 \".concat(!editing ? \"bg-gray-50\" : \"\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 465,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 463,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-800 mb-2\",\n                                                        children: \"Email Address\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 476,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"email\",\n                                                        name: \"email\",\n                                                        value: formData.email || \"\",\n                                                        onChange: handleInputChange,\n                                                        disabled: !editing,\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-gray-900 \".concat(!editing ? \"bg-gray-50\" : \"\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 477,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 475,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-800 mb-2\",\n                                                        children: \"Phone Number\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 488,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"tel\",\n                                                        name: \"phone\",\n                                                        value: formData.phone || \"\",\n                                                        onChange: handleInputChange,\n                                                        disabled: !editing,\n                                                        placeholder: \"Enter your phone number\",\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-gray-900 \".concat(!editing ? \"bg-gray-50\" : \"\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 489,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 487,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                        lineNumber: 458,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-gray-900 border-b border-gray-200 pb-2\",\n                                                children: \"Business Information\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 503,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-800 mb-2\",\n                                                        children: \"Business Name\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 508,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        name: \"businessName\",\n                                                        value: formData.businessName || \"\",\n                                                        onChange: handleInputChange,\n                                                        disabled: !editing,\n                                                        placeholder: \"Enter your business name\",\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-gray-900 \".concat(!editing ? \"bg-gray-50\" : \"\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 509,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 507,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-800 mb-2\",\n                                                        children: \"GST Number\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 521,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        name: \"gstNumber\",\n                                                        value: formData.gstNumber || \"\",\n                                                        onChange: handleInputChange,\n                                                        disabled: !editing,\n                                                        placeholder: \"Enter GST number (optional)\",\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-gray-900 \".concat(!editing ? \"bg-gray-50\" : \"\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 522,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 520,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-800 mb-2\",\n                                                        children: \"Business Logo\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 535,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    logoPreview ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-32 h-32 border-2 border-gray-200 rounded-lg overflow-hidden bg-gray-50 flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                    src: logoPreview,\n                                                                    alt: \"Business Logo\",\n                                                                    className: \"w-full h-full object-contain\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                    lineNumber: 539,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 538,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            editing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FileUpload__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                        onUpload: handleLogoUpload,\n                                                                        accept: \".jpg,.jpeg,.png,.gif,.svg,.webp\",\n                                                                        maxSize: 5,\n                                                                        maxFiles: 1,\n                                                                        className: \"flex-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                        lineNumber: 547,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: handleRemoveLogo,\n                                                                        disabled: logoUploading,\n                                                                        className: \"px-3 py-2 text-sm font-medium text-red-600 bg-red-50 border border-red-200 rounded-md hover:bg-red-100 transition-colors disabled:opacity-50\",\n                                                                        children: logoUploading ? \"Removing...\" : \"Remove\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                        lineNumber: 554,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 546,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 537,\n                                                        columnNumber: 19\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-32 h-32 border-2 border-dashed border-gray-300 rounded-lg bg-gray-50 flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-8 h-8 text-gray-400 mx-auto mb-2\",\n                                                                            fill: \"none\",\n                                                                            stroke: \"currentColor\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\",\n                                                                                strokeWidth: 2,\n                                                                                d: \"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                                lineNumber: 569,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                            lineNumber: 568,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-gray-500\",\n                                                                            children: \"No logo\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                            lineNumber: 571,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                    lineNumber: 567,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 566,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            editing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FileUpload__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                onUpload: handleLogoUpload,\n                                                                accept: \".jpg,.jpeg,.png,.gif,.svg,.webp\",\n                                                                maxSize: 5,\n                                                                maxFiles: 1,\n                                                                className: \"w-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 575,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 565,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    logoUploading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-2 flex items-center text-sm text-gray-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"animate-spin h-4 w-4 border-2 border-indigo-600 border-t-transparent rounded-full mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 587,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Uploading logo...\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 586,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 534,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                        lineNumber: 502,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                lineNumber: 456,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 border-b border-gray-200 pb-2 mb-4\",\n                                        children: \"Address Information\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                        lineNumber: 597,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"md:col-span-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-800 mb-2\",\n                                                        children: \"Street Address\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 603,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        name: \"street\",\n                                                        value: ((_formData_address = formData.address) === null || _formData_address === void 0 ? void 0 : _formData_address.street) || \"\",\n                                                        onChange: handleInputChange,\n                                                        disabled: !editing,\n                                                        placeholder: \"Enter your street address\",\n                                                        rows: 3,\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-gray-900 \".concat(!editing ? \"bg-gray-50\" : \"\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 604,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 602,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-800 mb-2\",\n                                                        children: \"City\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 616,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        name: \"city\",\n                                                        value: ((_formData_address1 = formData.address) === null || _formData_address1 === void 0 ? void 0 : _formData_address1.city) || \"\",\n                                                        onChange: handleInputChange,\n                                                        disabled: !editing,\n                                                        placeholder: \"Enter city\",\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-gray-900 \".concat(!editing ? \"bg-gray-50\" : \"\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 617,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 615,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-800 mb-2\",\n                                                        children: \"State\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 629,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        name: \"state\",\n                                                        value: ((_formData_address2 = formData.address) === null || _formData_address2 === void 0 ? void 0 : _formData_address2.state) || \"\",\n                                                        onChange: handleInputChange,\n                                                        disabled: !editing,\n                                                        placeholder: \"Enter state\",\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-gray-900 \".concat(!editing ? \"bg-gray-50\" : \"\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 630,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 628,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-800 mb-2\",\n                                                        children: \"PIN Code\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 642,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        name: \"pincode\",\n                                                        value: ((_formData_address3 = formData.address) === null || _formData_address3 === void 0 ? void 0 : _formData_address3.pincode) || \"\",\n                                                        onChange: handleInputChange,\n                                                        disabled: !editing,\n                                                        placeholder: \"Enter PIN code\",\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-gray-900 \".concat(!editing ? \"bg-gray-50\" : \"\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 643,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 641,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-800 mb-2\",\n                                                        children: \"Country\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 655,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        name: \"country\",\n                                                        value: ((_formData_address4 = formData.address) === null || _formData_address4 === void 0 ? void 0 : _formData_address4.country) || \"India\",\n                                                        onChange: handleInputChange,\n                                                        disabled: !editing,\n                                                        placeholder: \"Enter country\",\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-gray-900 \".concat(!editing ? \"bg-gray-50\" : \"\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 656,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 654,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                        lineNumber: 601,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                lineNumber: 596,\n                                columnNumber: 11\n                            }, this),\n                            editing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:hidden mt-6 flex space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleCancel,\n                                        className: \"flex-1 px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-800 bg-white hover:bg-gray-50 transition-colors\",\n                                        disabled: saving,\n                                        children: \"Cancel\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                        lineNumber: 672,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleSave,\n                                        className: \"flex-1 px-4 py-2 bg-indigo-600 text-white rounded-lg text-sm font-medium hover:bg-indigo-700 transition-colors disabled:opacity-50\",\n                                        disabled: saving,\n                                        children: saving ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full mr-2 inline-block\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                    lineNumber: 686,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Saving...\"\n                                            ]\n                                        }, void 0, true) : \"Save Changes\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                        lineNumber: 679,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                lineNumber: 671,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                        lineNumber: 453,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-xl shadow-sm border border-gray-200 p-6 mt-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900 mb-4\",\n                                children: \"Account Information\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                lineNumber: 697,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 sm:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center py-2 border-b border-gray-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-gray-800\",\n                                                children: \"Account Type\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 700,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-900 capitalize\",\n                                                children: user.role\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 701,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                        lineNumber: 699,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center py-2 border-b border-gray-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-gray-800\",\n                                                children: \"Member Since\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 704,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-900\",\n                                                children: user.createdAt ? new Date(user.createdAt).toLocaleDateString(\"en-IN\") : \"N/A\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 705,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                        lineNumber: 703,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center py-2 border-b border-gray-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-gray-800\",\n                                                children: \"Last Login\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 710,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-900\",\n                                                children: user.lastLogin ? new Date(user.lastLogin).toLocaleDateString(\"en-IN\") : \"N/A\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 711,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                        lineNumber: 709,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center py-2 border-b border-gray-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-gray-800\",\n                                                children: \"Email Status\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 716,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat(user.isEmailVerified ? \"bg-green-100 text-green-800\" : \"bg-yellow-100 text-yellow-800\"),\n                                                        children: user.isEmailVerified ? \"Verified\" : \"Not Verified\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 718,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    !user.isEmailVerified && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: sendVerificationEmail,\n                                                        disabled: verificationLoading,\n                                                        className: \"text-xs text-indigo-600 hover:text-indigo-800 font-medium disabled:opacity-50\",\n                                                        children: verificationLoading ? \"Sending...\" : \"Send Verification\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 722,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 717,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                        lineNumber: 715,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                lineNumber: 698,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                        lineNumber: 696,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                lineNumber: 365,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n        lineNumber: 334,\n        columnNumber: 5\n    }, this);\n}\n_s(ProfilePage, \"mLWr/FK64trBTG7PQjmr6/dSb6o=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = ProfilePage;\nvar _c;\n$RefreshReg$(_c, \"ProfilePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/profile/page.tsx\n"));

/***/ })

});