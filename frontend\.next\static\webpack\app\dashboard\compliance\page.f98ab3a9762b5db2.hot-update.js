"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/compliance/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/compliance/page.tsx":
/*!***********************************************!*\
  !*** ./src/app/dashboard/compliance/page.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ CompliancePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _components_dashboard_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../components/dashboard/DashboardLayout */ \"(app-pages-browser)/./src/components/dashboard/DashboardLayout.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction CompliancePage() {\n    _s();\n    const [complianceItems, setComplianceItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [overdueActivity, setOverdueActivity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [selectedType, setSelectedType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [selectedMonth, setSelectedMonth] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date().getMonth() + 1);\n    const [selectedYear, setSelectedYear] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date().getFullYear());\n    const [showOverdueSection, setShowOverdueSection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchComplianceData();\n        fetchComplianceStats();\n        fetchOverdueActivity();\n    }, [\n        selectedType,\n        selectedMonth,\n        selectedYear\n    ]);\n    const fetchComplianceData = async ()=>{\n        try {\n            const token = localStorage.getItem(\"token\");\n            if (!token) {\n                router.push(\"/login\");\n                return;\n            }\n            const params = new URLSearchParams({\n                type: selectedType,\n                month: selectedMonth.toString(),\n                year: selectedYear.toString()\n            });\n            const response = await fetch(\"http://localhost:5000/api/compliance/calendar?\".concat(params), {\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(token),\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            if (response.ok) {\n                const data = await response.json();\n                setComplianceItems(data.data);\n            } else {\n                console.error(\"Failed to fetch compliance data\");\n            }\n        } catch (error) {\n            console.error(\"Error fetching compliance data:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const fetchComplianceStats = async ()=>{\n        try {\n            const token = localStorage.getItem(\"token\");\n            const response = await fetch(\"http://localhost:5000/api/compliance/stats\", {\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(token),\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            if (response.ok) {\n                const data = await response.json();\n                setStats(data.data);\n            }\n        } catch (error) {\n            console.error(\"Error fetching compliance stats:\", error);\n        }\n    };\n    const fetchOverdueActivity = async ()=>{\n        try {\n            const token = localStorage.getItem(\"token\");\n            const response = await fetch(\"http://localhost:5000/api/compliance/overdue-activity\", {\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(token),\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            if (response.ok) {\n                const data = await response.json();\n                setOverdueActivity(data.data || []);\n            }\n        } catch (error) {\n            console.error(\"Error fetching overdue activity:\", error);\n            // Set empty array on error to prevent UI issues\n            setOverdueActivity([]);\n        }\n    };\n    const markAsCompleted = async (complianceId)=>{\n        try {\n            const token = localStorage.getItem(\"token\");\n            const response = await fetch(\"http://localhost:5000/api/compliance/\".concat(complianceId, \"/complete\"), {\n                method: \"PATCH\",\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(token),\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    notes: \"Marked as completed from dashboard\"\n                })\n            });\n            if (response.ok) {\n                fetchComplianceData();\n                fetchComplianceStats();\n                fetchOverdueActivity();\n            } else {\n                console.error(\"Failed to mark compliance as completed\");\n            }\n        } catch (error) {\n            console.error(\"Error marking compliance as completed:\", error);\n        }\n    };\n    const getPriorityColor = (priority)=>{\n        switch(priority){\n            case \"critical\":\n                return \"text-red-600 bg-red-50 border-red-200\";\n            case \"high\":\n                return \"text-orange-600 bg-orange-50 border-orange-200\";\n            case \"medium\":\n                return \"text-yellow-600 bg-yellow-50 border-yellow-200\";\n            case \"low\":\n                return \"text-green-600 bg-green-50 border-green-200\";\n            default:\n                return \"text-gray-800 bg-gray-50 border-gray-200\";\n        }\n    };\n    const getTypeColor = (type)=>{\n        switch(type){\n            case \"gst\":\n                return \"bg-blue-100 text-blue-800\";\n            case \"tds\":\n                return \"bg-purple-100 text-purple-800\";\n            case \"income_tax\":\n                return \"bg-green-100 text-green-800\";\n            case \"pf\":\n                return \"bg-orange-100 text-orange-800\";\n            case \"esi\":\n                return \"bg-pink-100 text-pink-800\";\n            default:\n                return \"bg-gray-100 text-gray-800\";\n        }\n    };\n    const getDaysUntilDue = (dueDate)=>{\n        const due = new Date(dueDate);\n        const today = new Date();\n        const diffTime = due.getTime() - today.getTime();\n        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n        return diffDays;\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                lineNumber: 199,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n            lineNumber: 198,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        title: \"Compliance Calendar\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"flex\",\n                    \"aria-label\": \"Breadcrumb\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                        className: \"flex items-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    href: \"/dashboard\",\n                                    className: \"text-gray-500 hover:text-gray-700 text-sm font-medium\",\n                                    children: \"Dashboard\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                    lineNumber: 211,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"flex-shrink-0 h-4 w-4 text-gray-400\",\n                                    fill: \"currentColor\",\n                                    viewBox: \"0 0 20 20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        fillRule: \"evenodd\",\n                                        d: \"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z\",\n                                        clipRule: \"evenodd\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                lineNumber: 215,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gray-900 text-sm font-medium\",\n                                    children: \"Compliance Calendar\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                    lineNumber: 208,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white shadow rounded-lg\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-6 py-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: \"Compliance Calendar\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                            lineNumber: 231,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-sm text-gray-600\",\n                                            children: \"Track and manage your tax compliance deadlines\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                            lineNumber: 232,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                    lineNumber: 230,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            href: \"/dashboard/compliance/settings\",\n                                            className: \"bg-white border border-gray-300 text-gray-800 px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-50 transition-colors\",\n                                            children: \"Settings\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                            lineNumber: 237,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            href: \"/dashboard/compliance/custom\",\n                                            className: \"bg-indigo-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-indigo-700 transition-colors\",\n                                            children: \"Add Custom\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                            lineNumber: 229,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                        lineNumber: 228,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                    lineNumber: 227,\n                    columnNumber: 9\n                }, this),\n                stats && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-4 gap-6 mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white text-sm font-medium\",\n                                                children: stats.totalActive\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                lineNumber: 261,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                        lineNumber: 259,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-900\",\n                                                children: \"Total Active\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                lineNumber: 265,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-semibold text-gray-900\",\n                                                children: stats.totalActive\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                        lineNumber: 264,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                lineNumber: 258,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                            lineNumber: 257,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 bg-green-500 rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white text-sm font-medium\",\n                                                children: stats.completedThisMonth\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                lineNumber: 275,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                            lineNumber: 274,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                        lineNumber: 273,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-900\",\n                                                children: \"Completed This Month\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                lineNumber: 279,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-semibold text-gray-900\",\n                                                children: stats.completedThisMonth\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                lineNumber: 272,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                            lineNumber: 271,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white text-sm font-medium\",\n                                                children: stats.upcomingCount\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                lineNumber: 289,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-900\",\n                                                children: \"Upcoming (30 days)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                lineNumber: 293,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-semibold text-gray-900\",\n                                                children: stats.upcomingCount\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                lineNumber: 294,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                        lineNumber: 292,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                lineNumber: 286,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                            lineNumber: 285,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 bg-red-500 rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white text-sm font-medium\",\n                                                children: stats.overdueCount\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                lineNumber: 303,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                            lineNumber: 302,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                        lineNumber: 301,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-900\",\n                                                children: \"Overdue\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                lineNumber: 307,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-semibold text-gray-900\",\n                                                children: stats.overdueCount\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                lineNumber: 308,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                        lineNumber: 306,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                lineNumber: 300,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                            lineNumber: 299,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                    lineNumber: 256,\n                    columnNumber: 11\n                }, this),\n                stats && stats.overdueCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-6 py-4 border-b border-gray-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-3 h-3 bg-red-500 rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                lineNumber: 321,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-lg font-medium text-gray-900\",\n                                                children: [\n                                                    \"Overdue Activity (\",\n                                                    stats.overdueCount,\n                                                    \")\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                lineNumber: 322,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                        lineNumber: 320,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowOverdueSection(!showOverdueSection),\n                                        className: \"text-sm text-indigo-600 hover:text-indigo-800 font-medium\",\n                                        children: showOverdueSection ? \"Hide Details\" : \"View Details\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                lineNumber: 319,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                            lineNumber: 318,\n                            columnNumber: 13\n                        }, this),\n                        showOverdueSection && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-6 py-4\",\n                            children: overdueActivity.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-6 h-6 text-red-600\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                lineNumber: 341,\n                                                columnNumber: 25\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                            lineNumber: 340,\n                                            columnNumber: 23\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                        lineNumber: 339,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Loading overdue activity...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                        lineNumber: 344,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                lineNumber: 338,\n                                columnNumber: 19\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    overdueActivity.slice(0, 5).map((activity)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between p-4 bg-red-50 rounded-lg border border-red-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-sm font-medium text-gray-900\",\n                                                                    children: activity.complianceId.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                                    lineNumber: 352,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"px-2 py-1 text-xs font-medium rounded-full \".concat(activity.complianceId.priority === \"critical\" ? \"bg-red-100 text-red-800\" : activity.complianceId.priority === \"high\" ? \"bg-orange-100 text-orange-800\" : activity.complianceId.priority === \"medium\" ? \"bg-yellow-100 text-yellow-800\" : \"bg-gray-100 text-gray-800\"),\n                                                                    children: activity.complianceId.priority\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                                    lineNumber: 355,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                            lineNumber: 351,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-1 flex items-center space-x-4 text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-600\",\n                                                                    children: [\n                                                                        \"Due: \",\n                                                                        new Date(activity.nextDueDate).toLocaleDateString(\"en-IN\")\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                                    lineNumber: 365,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-red-600 font-medium\",\n                                                                    children: [\n                                                                        activity.daysOverdue,\n                                                                        \" days overdue\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                                    lineNumber: 368,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-600\",\n                                                                    children: [\n                                                                        \"Type: \",\n                                                                        activity.complianceId.type.toUpperCase()\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                                    lineNumber: 371,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                            lineNumber: 364,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        activity.notes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"mt-2 text-sm text-gray-600 italic\",\n                                                            children: [\n                                                                \"Note: \",\n                                                                activity.notes\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                            lineNumber: 376,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                    lineNumber: 350,\n                                                    columnNumber: 25\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"ml-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>markAsCompleted(activity._id),\n                                                        className: \"px-3 py-1 bg-green-600 text-white text-xs font-medium rounded hover:bg-green-700 transition-colors\",\n                                                        children: \"Mark Complete\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                        lineNumber: 382,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                    lineNumber: 381,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, activity._id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                            lineNumber: 349,\n                                            columnNumber: 23\n                                        }, this)),\n                                    overdueActivity.length > 5 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center pt-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: [\n                                                \"Showing 5 of \",\n                                                overdueActivity.length,\n                                                \" overdue items\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                            lineNumber: 394,\n                                            columnNumber: 25\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                        lineNumber: 393,\n                                        columnNumber: 23\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                lineNumber: 347,\n                                columnNumber: 19\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                            lineNumber: 336,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                    lineNumber: 317,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow mb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-6 py-4 border-b border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-900 mb-2\",\n                                            children: \"Type\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                            lineNumber: 411,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: selectedType,\n                                            onChange: (e)=>setSelectedType(e.target.value),\n                                            className: \"bg-white border border-gray-300 rounded-md px-3 py-2 text-sm text-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 hover:border-gray-400 transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"all\",\n                                                    className: \"text-gray-900\",\n                                                    children: \"All Types\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                    lineNumber: 417,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"gst\",\n                                                    className: \"text-gray-900\",\n                                                    children: \"GST\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                    lineNumber: 418,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"tds\",\n                                                    className: \"text-gray-900\",\n                                                    children: \"TDS\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                    lineNumber: 419,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"income_tax\",\n                                                    className: \"text-gray-900\",\n                                                    children: \"Income Tax\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                    lineNumber: 420,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"pf\",\n                                                    className: \"text-gray-900\",\n                                                    children: \"PF\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                    lineNumber: 421,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"esi\",\n                                                    className: \"text-gray-900\",\n                                                    children: \"ESI\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                    lineNumber: 422,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"custom\",\n                                                    className: \"text-gray-900\",\n                                                    children: \"Custom\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                    lineNumber: 423,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                            lineNumber: 412,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                    lineNumber: 410,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-900 mb-2\",\n                                            children: \"Month\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                            lineNumber: 428,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: selectedMonth,\n                                            onChange: (e)=>setSelectedMonth(parseInt(e.target.value)),\n                                            className: \"bg-white border border-gray-300 rounded-md px-3 py-2 text-sm text-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 hover:border-gray-400 transition-colors\",\n                                            children: Array.from({\n                                                length: 12\n                                            }, (_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: i + 1,\n                                                    className: \"text-gray-900\",\n                                                    children: new Date(2024, i).toLocaleDateString(\"en-US\", {\n                                                        month: \"long\"\n                                                    })\n                                                }, i + 1, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                    lineNumber: 435,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                            lineNumber: 429,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                    lineNumber: 427,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-900 mb-2\",\n                                            children: \"Year\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                            lineNumber: 443,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: selectedYear,\n                                            onChange: (e)=>setSelectedYear(parseInt(e.target.value)),\n                                            className: \"bg-white border border-gray-300 rounded-md px-3 py-2 text-sm text-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 hover:border-gray-400 transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: 2024,\n                                                    className: \"text-gray-900\",\n                                                    children: \"2024\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                    lineNumber: 449,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: 2025,\n                                                    className: \"text-gray-900\",\n                                                    children: \"2025\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                    lineNumber: 450,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                            lineNumber: 444,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                    lineNumber: 442,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                            lineNumber: 409,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                        lineNumber: 408,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                    lineNumber: 407,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-6 py-4 border-b border-gray-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-lg font-medium text-gray-900\",\n                                children: [\n                                    \"Compliance Items (\",\n                                    complianceItems.length,\n                                    \")\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                lineNumber: 460,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                            lineNumber: 459,\n                            columnNumber: 11\n                        }, this),\n                        complianceItems.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-6 py-12 text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-900 text-base\",\n                                children: \"No compliance items found for the selected filters.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                lineNumber: 467,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                            lineNumber: 466,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"divide-y divide-gray-200\",\n                            children: complianceItems.map((item)=>{\n                                var _item_complianceId_resources, _item_complianceId_penaltyInfo, _item_complianceId_resources1;\n                                const daysUntilDue = getDaysUntilDue(item.nextDueDate);\n                                const isOverdue = daysUntilDue < 0;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-6 py-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-medium text-gray-900\",\n                                                                children: item.complianceId.title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                                lineNumber: 480,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"px-2 py-1 text-xs font-medium rounded-full \".concat(getTypeColor(item.complianceId.type)),\n                                                                children: item.complianceId.type.toUpperCase()\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                                lineNumber: 483,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"px-2 py-1 text-xs font-medium rounded border \".concat(getPriorityColor(item.complianceId.priority)),\n                                                                children: item.complianceId.priority\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                                lineNumber: 486,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                        lineNumber: 479,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"mt-1 text-sm text-gray-900\",\n                                                        children: item.complianceId.description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                        lineNumber: 491,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-2 flex items-center space-x-4 text-sm text-gray-900\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: [\n                                                                    \"Due: \",\n                                                                    new Date(item.nextDueDate).toLocaleDateString(\"en-IN\")\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                                lineNumber: 496,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: isOverdue ? \"text-red-600 font-medium\" : daysUntilDue <= 7 ? \"text-orange-600 font-medium\" : \"text-gray-900 font-medium\",\n                                                                children: isOverdue ? \"\".concat(Math.abs(daysUntilDue), \" days overdue\") : \"\".concat(daysUntilDue, \" days left\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                                lineNumber: 499,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            ((_item_complianceId_resources = item.complianceId.resources) === null || _item_complianceId_resources === void 0 ? void 0 : _item_complianceId_resources.formNumber) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-900\",\n                                                                children: [\n                                                                    \"Form: \",\n                                                                    item.complianceId.resources.formNumber\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                                lineNumber: 503,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                        lineNumber: 495,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    ((_item_complianceId_penaltyInfo = item.complianceId.penaltyInfo) === null || _item_complianceId_penaltyInfo === void 0 ? void 0 : _item_complianceId_penaltyInfo.lateFilingPenalty) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-2 text-sm text-red-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: \"Penalty:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                                lineNumber: 509,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            \" \",\n                                                            item.complianceId.penaltyInfo.lateFilingPenalty\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                        lineNumber: 508,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                lineNumber: 478,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    ((_item_complianceId_resources1 = item.complianceId.resources) === null || _item_complianceId_resources1 === void 0 ? void 0 : _item_complianceId_resources1.officialLink) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: item.complianceId.resources.officialLink,\n                                                        target: \"_blank\",\n                                                        rel: \"noopener noreferrer\",\n                                                        className: \"text-indigo-600 hover:text-indigo-900 text-sm font-medium\",\n                                                        children: \"Official Link\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                        lineNumber: 516,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    !item.isCompleted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>markAsCompleted(item._id),\n                                                        className: \"bg-green-600 text-white px-3 py-1 rounded text-sm font-medium hover:bg-green-700\",\n                                                        children: \"Mark Complete\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                        lineNumber: 527,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    item.isCompleted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-green-600 text-sm font-medium\",\n                                                        children: \"✓ Completed\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                        lineNumber: 536,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                lineNumber: 514,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                        lineNumber: 477,\n                                        columnNumber: 21\n                                    }, this)\n                                }, item._id, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                    lineNumber: 476,\n                                    columnNumber: 19\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                            lineNumber: 470,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                    lineNumber: 458,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n            lineNumber: 206,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n        lineNumber: 205,\n        columnNumber: 5\n    }, this);\n}\n_s(CompliancePage, \"dzZCZkPsrCCGz5KrO0x+ATtcB1k=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = CompliancePage;\nvar _c;\n$RefreshReg$(_c, \"CompliancePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/compliance/page.tsx\n"));

/***/ })

});