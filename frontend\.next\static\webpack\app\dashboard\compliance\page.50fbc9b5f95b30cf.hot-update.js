"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/compliance/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/compliance/page.tsx":
/*!***********************************************!*\
  !*** ./src/app/dashboard/compliance/page.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ CompliancePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _components_dashboard_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../components/dashboard/DashboardLayout */ \"(app-pages-browser)/./src/components/dashboard/DashboardLayout.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction CompliancePage() {\n    _s();\n    const [complianceItems, setComplianceItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [overdueActivity, setOverdueActivity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [selectedType, setSelectedType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [selectedMonth, setSelectedMonth] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date().getMonth() + 1);\n    const [selectedYear, setSelectedYear] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date().getFullYear());\n    const [showOverdueSection, setShowOverdueSection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [completingItems, setCompletingItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [clearingData, setClearingData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchComplianceData();\n        fetchComplianceStats();\n        fetchOverdueActivity();\n    }, [\n        selectedType,\n        selectedMonth,\n        selectedYear\n    ]);\n    const fetchComplianceData = async ()=>{\n        try {\n            const token = localStorage.getItem(\"token\");\n            if (!token) {\n                router.push(\"/login\");\n                return;\n            }\n            const params = new URLSearchParams({\n                type: selectedType,\n                month: selectedMonth.toString(),\n                year: selectedYear.toString()\n            });\n            const response = await fetch(\"http://localhost:5000/api/compliance/calendar?\".concat(params), {\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(token),\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            if (response.ok) {\n                const data = await response.json();\n                setComplianceItems(data.data);\n            } else {\n                console.error(\"Failed to fetch compliance data\");\n            }\n        } catch (error) {\n            console.error(\"Error fetching compliance data:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const fetchComplianceStats = async ()=>{\n        try {\n            const token = localStorage.getItem(\"token\");\n            const response = await fetch(\"http://localhost:5000/api/compliance/stats\", {\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(token),\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            if (response.ok) {\n                const data = await response.json();\n                setStats(data.data);\n            }\n        } catch (error) {\n            console.error(\"Error fetching compliance stats:\", error);\n        }\n    };\n    const fetchOverdueActivity = async ()=>{\n        try {\n            const token = localStorage.getItem(\"token\");\n            const response = await fetch(\"http://localhost:5000/api/compliance/overdue-activity\", {\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(token),\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            if (response.ok) {\n                const data = await response.json();\n                setOverdueActivity(data.data || []);\n            }\n        } catch (error) {\n            console.error(\"Error fetching overdue activity:\", error);\n            // Set empty array on error to prevent UI issues\n            setOverdueActivity([]);\n        }\n    };\n    const markAsCompleted = async (complianceId)=>{\n        try {\n            // Add to completing items set\n            setCompletingItems((prev)=>new Set(prev).add(complianceId));\n            const token = localStorage.getItem(\"token\");\n            const response = await fetch(\"http://localhost:5000/api/compliance/\".concat(complianceId, \"/complete\"), {\n                method: \"PATCH\",\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(token),\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    notes: \"Marked as completed from dashboard\"\n                })\n            });\n            if (response.ok) {\n                // Immediately update the overdue activity list to remove the completed item\n                setOverdueActivity((prev)=>prev.filter((item)=>item._id !== complianceId));\n                // Refresh all data\n                await Promise.all([\n                    fetchComplianceData(),\n                    fetchComplianceStats(),\n                    fetchOverdueActivity()\n                ]);\n                console.log(\"✅ Compliance item marked as completed successfully\");\n            } else {\n                const errorData = await response.json();\n                console.error(\"Failed to mark compliance as completed:\", errorData.message);\n                alert(\"Failed to mark compliance as completed. Please try again.\");\n            }\n        } catch (error) {\n            console.error(\"Error marking compliance as completed:\", error);\n            alert(\"Error marking compliance as completed. Please check your connection and try again.\");\n        } finally{\n            // Remove from completing items set\n            setCompletingItems((prev)=>{\n                const newSet = new Set(prev);\n                newSet.delete(complianceId);\n                return newSet;\n            });\n        }\n    };\n    const getPriorityColor = (priority)=>{\n        switch(priority){\n            case \"critical\":\n                return \"text-red-600 bg-red-50 border-red-200\";\n            case \"high\":\n                return \"text-orange-600 bg-orange-50 border-orange-200\";\n            case \"medium\":\n                return \"text-yellow-600 bg-yellow-50 border-yellow-200\";\n            case \"low\":\n                return \"text-green-600 bg-green-50 border-green-200\";\n            default:\n                return \"text-gray-800 bg-gray-50 border-gray-200\";\n        }\n    };\n    const getTypeColor = (type)=>{\n        switch(type){\n            case \"gst\":\n                return \"bg-blue-100 text-blue-800\";\n            case \"tds\":\n                return \"bg-purple-100 text-purple-800\";\n            case \"income_tax\":\n                return \"bg-green-100 text-green-800\";\n            case \"pf\":\n                return \"bg-orange-100 text-orange-800\";\n            case \"esi\":\n                return \"bg-pink-100 text-pink-800\";\n            default:\n                return \"bg-gray-100 text-gray-800\";\n        }\n    };\n    const getDaysUntilDue = (dueDate)=>{\n        const due = new Date(dueDate);\n        const today = new Date();\n        const diffTime = due.getTime() - today.getTime();\n        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n        return diffDays;\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                lineNumber: 222,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n            lineNumber: 221,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        title: \"Compliance Calendar\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"flex\",\n                    \"aria-label\": \"Breadcrumb\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                        className: \"flex items-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    href: \"/dashboard\",\n                                    className: \"text-gray-500 hover:text-gray-700 text-sm font-medium\",\n                                    children: \"Dashboard\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                    lineNumber: 234,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                lineNumber: 233,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"flex-shrink-0 h-4 w-4 text-gray-400\",\n                                    fill: \"currentColor\",\n                                    viewBox: \"0 0 20 20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        fillRule: \"evenodd\",\n                                        d: \"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z\",\n                                        clipRule: \"evenodd\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                    lineNumber: 239,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gray-900 text-sm font-medium\",\n                                    children: \"Compliance Calendar\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                    lineNumber: 244,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                        lineNumber: 232,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                    lineNumber: 231,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white shadow rounded-lg\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-6 py-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: \"Compliance Calendar\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-sm text-gray-600\",\n                                            children: \"Track and manage your tax compliance deadlines\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                            lineNumber: 255,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                    lineNumber: 253,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            href: \"/dashboard/compliance/settings\",\n                                            className: \"bg-white border border-gray-300 text-gray-800 px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-50 transition-colors\",\n                                            children: \"Settings\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            href: \"/dashboard/compliance/custom\",\n                                            className: \"bg-indigo-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-indigo-700 transition-colors\",\n                                            children: \"Add Custom\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                            lineNumber: 266,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                    lineNumber: 259,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                            lineNumber: 252,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                        lineNumber: 251,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                    lineNumber: 250,\n                    columnNumber: 9\n                }, this),\n                stats && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-4 gap-6 mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white text-sm font-medium\",\n                                                children: stats.totalActive\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                            lineNumber: 283,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-900\",\n                                                children: \"Total Active\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-semibold text-gray-900\",\n                                                children: stats.totalActive\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                lineNumber: 289,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                lineNumber: 281,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                            lineNumber: 280,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 bg-green-500 rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white text-sm font-medium\",\n                                                children: stats.completedThisMonth\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                lineNumber: 298,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                            lineNumber: 297,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                        lineNumber: 296,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-900\",\n                                                children: \"Completed This Month\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                lineNumber: 302,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-semibold text-gray-900\",\n                                                children: stats.completedThisMonth\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                lineNumber: 303,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                        lineNumber: 301,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                lineNumber: 295,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                            lineNumber: 294,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white text-sm font-medium\",\n                                                children: stats.upcomingCount\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                lineNumber: 312,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                            lineNumber: 311,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                        lineNumber: 310,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-900\",\n                                                children: \"Upcoming (30 days)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                lineNumber: 316,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-semibold text-gray-900\",\n                                                children: stats.upcomingCount\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                lineNumber: 317,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                        lineNumber: 315,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                lineNumber: 309,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                            lineNumber: 308,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow p-6 transition-all duration-200 \".concat(stats.overdueCount > 0 ? \"cursor-pointer hover:shadow-lg hover:bg-red-50\" : \"\"),\n                            onClick: ()=>stats.overdueCount > 0 && setShowOverdueSection(!showOverdueSection),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 bg-red-500 rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white text-sm font-medium\",\n                                                children: stats.overdueCount\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                lineNumber: 331,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                            lineNumber: 330,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                        lineNumber: 329,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-4 flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-900\",\n                                                children: \"Overdue\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                lineNumber: 335,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-semibold text-gray-900\",\n                                                children: stats.overdueCount\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                lineNumber: 336,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                        lineNumber: 334,\n                                        columnNumber: 17\n                                    }, this),\n                                    stats.overdueCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-5 h-5 text-gray-400 transition-transform duration-200 \".concat(showOverdueSection ? \"transform rotate-180\" : \"\"),\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M19 9l-7 7-7-7\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                lineNumber: 348,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                            lineNumber: 340,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                        lineNumber: 339,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                lineNumber: 328,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                            lineNumber: 322,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                    lineNumber: 279,\n                    columnNumber: 11\n                }, this),\n                stats && stats.overdueCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-6 py-4 border-b border-gray-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-3 h-3 bg-red-500 rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                lineNumber: 363,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-lg font-medium text-gray-900\",\n                                                children: [\n                                                    \"Overdue Activity (\",\n                                                    stats.overdueCount,\n                                                    \")\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                lineNumber: 364,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                        lineNumber: 362,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowOverdueSection(!showOverdueSection),\n                                        className: \"text-sm text-indigo-600 hover:text-indigo-800 font-medium\",\n                                        children: showOverdueSection ? \"Hide Details\" : \"View Details\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                        lineNumber: 368,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                lineNumber: 361,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                            lineNumber: 360,\n                            columnNumber: 13\n                        }, this),\n                        showOverdueSection && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-6 py-4\",\n                            children: overdueActivity.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-6 h-6 text-red-600\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                lineNumber: 383,\n                                                columnNumber: 25\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                            lineNumber: 382,\n                                            columnNumber: 23\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                        lineNumber: 381,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Loading overdue activity...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                        lineNumber: 386,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                lineNumber: 380,\n                                columnNumber: 19\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    overdueActivity.slice(0, 5).map((activity)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between p-4 bg-red-50 rounded-lg border border-red-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-sm font-medium text-gray-900\",\n                                                                    children: activity.complianceId.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                                    lineNumber: 394,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"px-2 py-1 text-xs font-medium rounded-full \".concat(activity.complianceId.priority === \"critical\" ? \"bg-red-100 text-red-800\" : activity.complianceId.priority === \"high\" ? \"bg-orange-100 text-orange-800\" : activity.complianceId.priority === \"medium\" ? \"bg-yellow-100 text-yellow-800\" : \"bg-gray-100 text-gray-800\"),\n                                                                    children: activity.complianceId.priority\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                                    lineNumber: 397,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                            lineNumber: 393,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-1 flex items-center space-x-4 text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-600\",\n                                                                    children: [\n                                                                        \"Due: \",\n                                                                        new Date(activity.nextDueDate).toLocaleDateString(\"en-IN\")\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                                    lineNumber: 407,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-red-600 font-medium\",\n                                                                    children: [\n                                                                        activity.daysOverdue,\n                                                                        \" days overdue\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                                    lineNumber: 410,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-600\",\n                                                                    children: [\n                                                                        \"Type: \",\n                                                                        activity.complianceId.type.toUpperCase()\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                                    lineNumber: 413,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                            lineNumber: 406,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        activity.notes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"mt-2 text-sm text-gray-600 italic\",\n                                                            children: [\n                                                                \"Note: \",\n                                                                activity.notes\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                            lineNumber: 418,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                    lineNumber: 392,\n                                                    columnNumber: 25\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"ml-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>markAsCompleted(activity._id),\n                                                        disabled: completingItems.has(activity._id),\n                                                        className: \"px-3 py-1 text-xs font-medium rounded transition-colors flex items-center space-x-1 \".concat(completingItems.has(activity._id) ? \"bg-gray-400 text-white cursor-not-allowed\" : \"bg-green-600 text-white hover:bg-green-700\"),\n                                                        children: completingItems.has(activity._id) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-3 h-3 animate-spin\",\n                                                                    fill: \"none\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                            className: \"opacity-25\",\n                                                                            cx: \"12\",\n                                                                            cy: \"12\",\n                                                                            r: \"10\",\n                                                                            stroke: \"currentColor\",\n                                                                            strokeWidth: \"4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                                            lineNumber: 436,\n                                                                            columnNumber: 35\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            className: \"opacity-75\",\n                                                                            fill: \"currentColor\",\n                                                                            d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                                            lineNumber: 437,\n                                                                            columnNumber: 35\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                                    lineNumber: 435,\n                                                                    columnNumber: 33\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Completing...\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                                    lineNumber: 439,\n                                                                    columnNumber: 33\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Mark Complete\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                            lineNumber: 442,\n                                                            columnNumber: 31\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                        lineNumber: 424,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                    lineNumber: 423,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, activity._id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                            lineNumber: 391,\n                                            columnNumber: 23\n                                        }, this)),\n                                    overdueActivity.length > 5 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center pt-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: [\n                                                \"Showing 5 of \",\n                                                overdueActivity.length,\n                                                \" overdue items\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                            lineNumber: 451,\n                                            columnNumber: 25\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                        lineNumber: 450,\n                                        columnNumber: 23\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                lineNumber: 389,\n                                columnNumber: 19\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                            lineNumber: 378,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                    lineNumber: 359,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow mb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-6 py-4 border-b border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-900 mb-2\",\n                                            children: \"Type\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                            lineNumber: 468,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: selectedType,\n                                            onChange: (e)=>setSelectedType(e.target.value),\n                                            className: \"bg-white border border-gray-300 rounded-md px-3 py-2 text-sm text-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 hover:border-gray-400 transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"all\",\n                                                    className: \"text-gray-900\",\n                                                    children: \"All Types\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                    lineNumber: 474,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"gst\",\n                                                    className: \"text-gray-900\",\n                                                    children: \"GST\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                    lineNumber: 475,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"tds\",\n                                                    className: \"text-gray-900\",\n                                                    children: \"TDS\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                    lineNumber: 476,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"income_tax\",\n                                                    className: \"text-gray-900\",\n                                                    children: \"Income Tax\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                    lineNumber: 477,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"pf\",\n                                                    className: \"text-gray-900\",\n                                                    children: \"PF\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                    lineNumber: 478,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"esi\",\n                                                    className: \"text-gray-900\",\n                                                    children: \"ESI\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                    lineNumber: 479,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"custom\",\n                                                    className: \"text-gray-900\",\n                                                    children: \"Custom\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                    lineNumber: 480,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                            lineNumber: 469,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                    lineNumber: 467,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-900 mb-2\",\n                                            children: \"Month\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                            lineNumber: 485,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: selectedMonth,\n                                            onChange: (e)=>setSelectedMonth(parseInt(e.target.value)),\n                                            className: \"bg-white border border-gray-300 rounded-md px-3 py-2 text-sm text-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 hover:border-gray-400 transition-colors\",\n                                            children: Array.from({\n                                                length: 12\n                                            }, (_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: i + 1,\n                                                    className: \"text-gray-900\",\n                                                    children: new Date(2024, i).toLocaleDateString(\"en-US\", {\n                                                        month: \"long\"\n                                                    })\n                                                }, i + 1, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                    lineNumber: 492,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                            lineNumber: 486,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                    lineNumber: 484,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-900 mb-2\",\n                                            children: \"Year\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                            lineNumber: 500,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: selectedYear,\n                                            onChange: (e)=>setSelectedYear(parseInt(e.target.value)),\n                                            className: \"bg-white border border-gray-300 rounded-md px-3 py-2 text-sm text-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 hover:border-gray-400 transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: 2024,\n                                                    className: \"text-gray-900\",\n                                                    children: \"2024\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                    lineNumber: 506,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: 2025,\n                                                    className: \"text-gray-900\",\n                                                    children: \"2025\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                    lineNumber: 507,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                            lineNumber: 501,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                    lineNumber: 499,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                            lineNumber: 466,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                        lineNumber: 465,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                    lineNumber: 464,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-6 py-4 border-b border-gray-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-lg font-medium text-gray-900\",\n                                children: [\n                                    \"Compliance Items (\",\n                                    complianceItems.length,\n                                    \")\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                lineNumber: 517,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                            lineNumber: 516,\n                            columnNumber: 11\n                        }, this),\n                        complianceItems.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-6 py-12 text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-900 text-base\",\n                                children: \"No compliance items found for the selected filters.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                lineNumber: 524,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                            lineNumber: 523,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"divide-y divide-gray-200\",\n                            children: complianceItems.map((item)=>{\n                                var _item_complianceId_resources, _item_complianceId_penaltyInfo, _item_complianceId_resources1;\n                                const daysUntilDue = getDaysUntilDue(item.nextDueDate);\n                                const isOverdue = daysUntilDue < 0;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-6 py-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-medium text-gray-900\",\n                                                                children: item.complianceId.title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                                lineNumber: 537,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"px-2 py-1 text-xs font-medium rounded-full \".concat(getTypeColor(item.complianceId.type)),\n                                                                children: item.complianceId.type.toUpperCase()\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                                lineNumber: 540,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"px-2 py-1 text-xs font-medium rounded border \".concat(getPriorityColor(item.complianceId.priority)),\n                                                                children: item.complianceId.priority\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                                lineNumber: 543,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                        lineNumber: 536,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"mt-1 text-sm text-gray-900\",\n                                                        children: item.complianceId.description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                        lineNumber: 548,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-2 flex items-center space-x-4 text-sm text-gray-900\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: [\n                                                                    \"Due: \",\n                                                                    new Date(item.nextDueDate).toLocaleDateString(\"en-IN\")\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                                lineNumber: 553,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: isOverdue ? \"text-red-600 font-medium\" : daysUntilDue <= 7 ? \"text-orange-600 font-medium\" : \"text-gray-900 font-medium\",\n                                                                children: isOverdue ? \"\".concat(Math.abs(daysUntilDue), \" days overdue\") : \"\".concat(daysUntilDue, \" days left\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                                lineNumber: 556,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            ((_item_complianceId_resources = item.complianceId.resources) === null || _item_complianceId_resources === void 0 ? void 0 : _item_complianceId_resources.formNumber) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-900\",\n                                                                children: [\n                                                                    \"Form: \",\n                                                                    item.complianceId.resources.formNumber\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                                lineNumber: 560,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                        lineNumber: 552,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    ((_item_complianceId_penaltyInfo = item.complianceId.penaltyInfo) === null || _item_complianceId_penaltyInfo === void 0 ? void 0 : _item_complianceId_penaltyInfo.lateFilingPenalty) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-2 text-sm text-red-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: \"Penalty:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                                lineNumber: 566,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            \" \",\n                                                            item.complianceId.penaltyInfo.lateFilingPenalty\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                        lineNumber: 565,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                lineNumber: 535,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    ((_item_complianceId_resources1 = item.complianceId.resources) === null || _item_complianceId_resources1 === void 0 ? void 0 : _item_complianceId_resources1.officialLink) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: item.complianceId.resources.officialLink,\n                                                        target: \"_blank\",\n                                                        rel: \"noopener noreferrer\",\n                                                        className: \"text-indigo-600 hover:text-indigo-900 text-sm font-medium\",\n                                                        children: \"Official Link\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                        lineNumber: 573,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    !item.isCompleted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>markAsCompleted(item._id),\n                                                        disabled: completingItems.has(item._id),\n                                                        className: \"px-3 py-1 text-sm font-medium rounded transition-colors flex items-center space-x-1 \".concat(completingItems.has(item._id) ? \"bg-gray-400 text-white cursor-not-allowed\" : \"bg-green-600 text-white hover:bg-green-700\"),\n                                                        children: completingItems.has(item._id) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-4 h-4 animate-spin\",\n                                                                    fill: \"none\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                            className: \"opacity-25\",\n                                                                            cx: \"12\",\n                                                                            cy: \"12\",\n                                                                            r: \"10\",\n                                                                            stroke: \"currentColor\",\n                                                                            strokeWidth: \"4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                                            lineNumber: 596,\n                                                                            columnNumber: 35\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            className: \"opacity-75\",\n                                                                            fill: \"currentColor\",\n                                                                            d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                                            lineNumber: 597,\n                                                                            columnNumber: 35\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                                    lineNumber: 595,\n                                                                    columnNumber: 33\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Completing...\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                                    lineNumber: 599,\n                                                                    columnNumber: 33\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Mark Complete\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                            lineNumber: 602,\n                                                            columnNumber: 31\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                        lineNumber: 584,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    item.isCompleted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-green-600 text-sm font-medium\",\n                                                        children: \"✓ Completed\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                        lineNumber: 608,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                lineNumber: 571,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                        lineNumber: 534,\n                                        columnNumber: 21\n                                    }, this)\n                                }, item._id, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                    lineNumber: 533,\n                                    columnNumber: 19\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                            lineNumber: 527,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                    lineNumber: 515,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n            lineNumber: 229,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n        lineNumber: 228,\n        columnNumber: 5\n    }, this);\n}\n_s(CompliancePage, \"j0jR2F2OKU2CNyslgsmQ1W7mQFU=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = CompliancePage;\nvar _c;\n$RefreshReg$(_c, \"CompliancePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/compliance/page.tsx\n"));

/***/ })

});