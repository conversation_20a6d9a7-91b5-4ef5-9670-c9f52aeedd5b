import { Request, Response, NextFunction } from 'express';
export declare const checkUsageLimit: (action: "invoice" | "storage") => (req: Request, res: Response, next: NextFunction) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const incrementUsage: (req: Request, res: Response, next: NextFunction) => Promise<void>;
export declare const checkFeatureAccess: (feature: string) => (req: Request, res: Response, next: NextFunction) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const addSubscriptionInfo: (req: Request, res: Response, next: NextFunction) => Promise<void>;
declare global {
    namespace Express {
        interface Request {
            usageAction?: {
                type: 'invoices' | 'storage';
                amount: number;
            };
            userSubscription?: any;
        }
    }
}
//# sourceMappingURL=usageTracking.d.ts.map