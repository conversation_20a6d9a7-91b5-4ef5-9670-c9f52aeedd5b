"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/subscription/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/subscription/page.tsx":
/*!*************************************************!*\
  !*** ./src/app/dashboard/subscription/page.tsx ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SubscriptionPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../lib/auth */ \"(app-pages-browser)/./src/lib/auth.ts\");\n/* harmony import */ var _components_dashboard_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../components/dashboard/DashboardLayout */ \"(app-pages-browser)/./src/components/dashboard/DashboardLayout.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction SubscriptionPage() {\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [plans, setPlans] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentSubscription, setCurrentSubscription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [upgrading, setUpgrading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [billingInterval, setBillingInterval] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"monthly\");\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (user) {\n            fetchPlansAndSubscription();\n        }\n    }, [\n        user\n    ]);\n    const fetchPlansAndSubscription = async ()=>{\n        try {\n            // First sync usage to ensure accurate data\n            try {\n                await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.authenticatedFetch)(\"/api/subscriptions/usage/sync\", {\n                    method: \"POST\"\n                });\n            } catch (syncError) {\n                console.warn(\"Failed to sync usage:\", syncError);\n            // Continue even if sync fails\n            }\n            const [plansResponse, subscriptionResponse] = await Promise.all([\n                (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.authenticatedFetch)(\"/api/subscriptions/plans\"),\n                (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.authenticatedFetch)(\"/api/subscriptions/current\")\n            ]);\n            const plansData = await plansResponse.json();\n            const subscriptionData = await subscriptionResponse.json();\n            if (plansData.success) {\n                setPlans(plansData.data.plans);\n            }\n            if (subscriptionData.success) {\n                setCurrentSubscription(subscriptionData.data.subscription);\n            }\n        } catch (error) {\n            console.error(\"Failed to fetch subscription data:\", error);\n            setError(\"Failed to load subscription information\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleUpgrade = async (planId)=>{\n        if (planId === \"free\") {\n            // Handle downgrade to free\n            try {\n                setUpgrading(planId);\n                const response = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.authenticatedFetch)(\"/api/subscriptions/change\", {\n                    method: \"POST\",\n                    body: JSON.stringify({\n                        planId,\n                        interval: billingInterval\n                    })\n                });\n                const data = await response.json();\n                if (data.success) {\n                    await fetchPlansAndSubscription();\n                    setError(\"\");\n                } else {\n                    setError(data.message);\n                }\n            } catch (error) {\n                console.error(\"Failed to change subscription:\", error);\n                setError(\"Failed to change subscription\");\n            } finally{\n                setUpgrading(null);\n            }\n            return;\n        }\n        // Handle paid plan upgrade\n        try {\n            var _plans_find;\n            setUpgrading(planId);\n            // Create payment order\n            const orderResponse = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.authenticatedFetch)(\"/api/subscriptions/payment/create-order\", {\n                method: \"POST\",\n                body: JSON.stringify({\n                    planId,\n                    interval: billingInterval\n                })\n            });\n            const orderData = await orderResponse.json();\n            console.log(\"\\uD83D\\uDD0D Order response:\", orderData);\n            if (!orderData.success) {\n                throw new Error(orderData.message || \"Failed to create payment order\");\n            }\n            // Check if Razorpay key is available\n            if (!orderData.data.key) {\n                throw new Error(\"Payment gateway is not configured. Please contact support.\");\n            }\n            console.log(\"✅ Order data received, initializing Razorpay...\");\n            // Check if we're in development and use mock payment\n            const isDevelopment = \"development\" === \"development\";\n            if (isDevelopment) {\n                var _plans_find1;\n                console.log(\"\\uD83E\\uDDEA Development mode: Using mock payment flow\");\n                const confirmPayment = confirm(\"Mock Payment Flow\\n\\nPlan: \".concat((_plans_find1 = plans.find((p)=>p.id === planId)) === null || _plans_find1 === void 0 ? void 0 : _plans_find1.name, \"\\nAmount: ₹\").concat(orderData.data.amount / 100, \"\\n\\nClick OK to simulate successful payment, Cancel to simulate failure.\"));\n                if (confirmPayment) {\n                    try {\n                        const verifyResponse = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.authenticatedFetch)(\"/api/subscriptions/payment/verify\", {\n                            method: \"POST\",\n                            body: JSON.stringify({\n                                razorpay_order_id: orderData.data.orderId,\n                                razorpay_payment_id: \"mock_payment_\" + Date.now(),\n                                razorpay_signature: \"mock_signature\"\n                            })\n                        });\n                        const verifyData = await verifyResponse.json();\n                        if (verifyData.success) {\n                            await fetchPlansAndSubscription();\n                            setError(\"\");\n                            alert(\"Mock payment successful! Your subscription has been upgraded.\");\n                        } else {\n                            throw new Error(verifyData.message || \"Payment verification failed\");\n                        }\n                    } catch (error) {\n                        console.error(\"Mock payment verification error:\", error);\n                        setError(error instanceof Error ? error.message : \"Payment verification failed\");\n                    }\n                }\n                setUpgrading(null);\n                return;\n            }\n            // Initialize Razorpay payment\n            const options = {\n                key: orderData.data.key,\n                amount: orderData.data.amount,\n                currency: orderData.data.currency,\n                name: \"InvoNest\",\n                description: \"Upgrade to \".concat((_plans_find = plans.find((p)=>p.id === planId)) === null || _plans_find === void 0 ? void 0 : _plans_find.name, \" Plan\"),\n                order_id: orderData.data.orderId,\n                handler: async (response)=>{\n                    try {\n                        // Verify payment\n                        const verifyResponse = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.authenticatedFetch)(\"/api/subscriptions/payment/verify\", {\n                            method: \"POST\",\n                            body: JSON.stringify({\n                                razorpay_order_id: response.razorpay_order_id,\n                                razorpay_payment_id: response.razorpay_payment_id,\n                                razorpay_signature: response.razorpay_signature\n                            })\n                        });\n                        const verifyData = await verifyResponse.json();\n                        if (verifyData.success) {\n                            await fetchPlansAndSubscription();\n                            setError(\"\");\n                            alert(\"Subscription upgraded successfully!\");\n                        } else {\n                            throw new Error(verifyData.message || \"Payment verification failed\");\n                        }\n                    } catch (error) {\n                        console.error(\"Payment verification failed:\", error);\n                        setError(error instanceof Error ? error.message : \"Payment verification failed\");\n                    } finally{\n                        setUpgrading(null);\n                    }\n                },\n                modal: {\n                    ondismiss: ()=>{\n                        console.log(\"\\uD83D\\uDD04 Razorpay modal dismissed\");\n                        setUpgrading(null);\n                    }\n                },\n                theme: {\n                    color: \"#4F46E5\"\n                }\n            };\n            // Load Razorpay script and open payment modal\n            console.log(\"\\uD83D\\uDD04 Loading Razorpay script...\");\n            // Check if Razorpay script is already loaded\n            if (window.Razorpay) {\n                console.log(\"✅ Razorpay already loaded, opening payment modal...\");\n                try {\n                    const rzp = new window.Razorpay(options);\n                    rzp.open();\n                } catch (error) {\n                    console.error(\"Failed to initialize Razorpay:\", error);\n                    setError(\"Failed to initialize payment gateway\");\n                    setUpgrading(null);\n                }\n                return;\n            }\n            const script = document.createElement(\"script\");\n            script.src = \"https://checkout.razorpay.com/v1/checkout.js\";\n            script.onload = ()=>{\n                console.log(\"✅ Razorpay script loaded successfully\");\n                try {\n                    const rzp = new window.Razorpay(options);\n                    console.log(\"✅ Razorpay instance created, opening modal...\");\n                    // Add a small delay to ensure DOM is ready\n                    setTimeout(()=>{\n                        rzp.open();\n                        console.log(\"\\uD83D\\uDE80 Razorpay modal opened successfully\");\n                        // Check if modal elements are created\n                        setTimeout(()=>{\n                            const modalElements = document.querySelectorAll('[id*=\"razorpay\"], [class*=\"razorpay\"]');\n                            console.log(\"\\uD83D\\uDD0D Razorpay modal elements found:\", modalElements.length);\n                            modalElements.forEach((el, index)=>{\n                                console.log(\"Element \".concat(index, \":\"), el.tagName, el.id, el.className);\n                            });\n                        }, 500);\n                    }, 100);\n                } catch (error) {\n                    console.error(\"Failed to initialize Razorpay:\", error);\n                    setError(\"Failed to initialize payment gateway\");\n                    setUpgrading(null);\n                }\n            };\n            script.onerror = ()=>{\n                console.error(\"❌ Failed to load Razorpay script\");\n                setError(\"Failed to load payment gateway\");\n                setUpgrading(null);\n            };\n            document.head.appendChild(script);\n        } catch (error) {\n            console.error(\"Failed to upgrade subscription:\", error);\n            setError(error instanceof Error ? error.message : \"Failed to upgrade subscription\");\n            setUpgrading(null);\n        }\n    };\n    const formatPrice = (price)=>{\n        return new Intl.NumberFormat(\"en-IN\", {\n            style: \"currency\",\n            currency: \"INR\"\n        }).format(price / 100);\n    };\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString(\"en-IN\", {\n            year: \"numeric\",\n            month: \"long\",\n            day: \"numeric\"\n        });\n    };\n    const getUsageColor = (percentage)=>{\n        if (percentage >= 90) return \"bg-red-500\";\n        if (percentage >= 75) return \"bg-yellow-500\";\n        return \"bg-green-500\";\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center py-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"animate-spin h-8 w-8 text-indigo-600\",\n                            xmlns: \"http://www.w3.org/2000/svg\",\n                            fill: \"none\",\n                            viewBox: \"0 0 24 24\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                    className: \"opacity-25\",\n                                    cx: \"12\",\n                                    cy: \"12\",\n                                    r: \"10\",\n                                    stroke: \"currentColor\",\n                                    strokeWidth: \"4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                    lineNumber: 311,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    className: \"opacity-75\",\n                                    fill: \"currentColor\",\n                                    d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                    lineNumber: 312,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                            lineNumber: 310,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-lg text-gray-600\",\n                            children: \"Loading subscription...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                            lineNumber: 314,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                    lineNumber: 309,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                lineNumber: 308,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n            lineNumber: 307,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__.DashboardHeader, {\n                title: \"Subscription & Billing\",\n                subtitle: \"Manage your InvoNest subscription and view usage\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                lineNumber: 323,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto p-4 lg:p-8 space-y-8\",\n                children: [\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                        lineNumber: 331,\n                        columnNumber: 11\n                    }, this),\n                    currentSubscription && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-xl shadow-sm border border-gray-200 p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                children: \"Current Subscription\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                lineNumber: 339,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-gray-900\",\n                                                children: currentSubscription.planName\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                lineNumber: 342,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600\",\n                                                children: [\n                                                    formatPrice(currentSubscription.amount),\n                                                    \" / \",\n                                                    currentSubscription.interval\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                lineNumber: 343,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-500 mt-1\",\n                                                children: [\n                                                    currentSubscription.status === \"active\" ? \"Active\" : currentSubscription.status,\n                                                    currentSubscription.currentPeriodEnd && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            \" until \",\n                                                            formatDate(currentSubscription.currentPeriodEnd)\n                                                        ]\n                                                    }, void 0, true)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                lineNumber: 346,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                        lineNumber: 341,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"md:col-span-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-sm font-medium text-gray-900 mb-3\",\n                                                children: \"Usage This Period\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                lineNumber: 356,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between text-sm text-gray-900\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Invoices\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                        lineNumber: 360,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            currentSubscription.usage.invoicesUsed,\n                                                                            \" / \",\n                                                                            currentSubscription.features.maxInvoices === -1 ? \"∞\" : currentSubscription.features.maxInvoices\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                        lineNumber: 361,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 359,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            currentSubscription.features.maxInvoices !== -1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-full bg-gray-200 rounded-full h-2 mt-1\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-2 rounded-full \".concat(getUsageColor(currentSubscription.usagePercentages.invoices).split(\" \")[1]),\n                                                                    style: {\n                                                                        width: \"\".concat(Math.min(currentSubscription.usagePercentages.invoices, 100), \"%\")\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                    lineNumber: 367,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 366,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                        lineNumber: 358,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between text-sm text-gray-900\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Storage\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                        lineNumber: 377,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            currentSubscription.usage.storageUsed,\n                                                                            \"MB / \",\n                                                                            currentSubscription.features.maxStorage,\n                                                                            \"MB\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                        lineNumber: 378,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 376,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-full bg-gray-200 rounded-full h-2 mt-1\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-2 rounded-full \".concat(getUsageColor(currentSubscription.usagePercentages.storage).split(\" \")[1]),\n                                                                    style: {\n                                                                        width: \"\".concat(Math.min(currentSubscription.usagePercentages.storage, 100), \"%\")\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                    lineNumber: 383,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 382,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                        lineNumber: 375,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                lineNumber: 357,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                        lineNumber: 355,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                lineNumber: 340,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                        lineNumber: 338,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-100 rounded-lg p-1 flex\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setBillingInterval(\"monthly\"),\n                                    className: \"px-4 py-2 text-sm font-medium rounded-md transition-colors \".concat(billingInterval === \"monthly\" ? \"bg-white text-indigo-600 shadow-sm\" : \"text-gray-800 hover:text-gray-900\"),\n                                    children: \"Monthly\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                    lineNumber: 398,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setBillingInterval(\"yearly\"),\n                                    className: \"px-4 py-2 text-sm font-medium rounded-md transition-colors \".concat(billingInterval === \"yearly\" ? \"bg-white text-indigo-600 shadow-sm\" : \"text-gray-800 hover:text-gray-900\"),\n                                    children: [\n                                        \"Yearly\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-1 text-xs bg-green-100 text-green-800 px-1.5 py-0.5 rounded\",\n                                            children: \"Save 17%\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                            lineNumber: 417,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                    lineNumber: 408,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                            lineNumber: 397,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                        lineNumber: 396,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                        children: plans.map((plan)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative bg-white rounded-xl shadow-sm border-2 transition-all \".concat(plan.popular ? \"border-indigo-500 ring-2 ring-indigo-200\" : \"border-gray-200 hover:border-indigo-300\", \" \").concat((currentSubscription === null || currentSubscription === void 0 ? void 0 : currentSubscription.planId) === plan.id ? \"ring-2 ring-green-200 border-green-500\" : \"\"),\n                                children: [\n                                    plan.popular && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -top-3 left-1/2 transform -translate-x-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"bg-indigo-600 text-white px-3 py-1 text-xs font-medium rounded-full\",\n                                            children: \"Most Popular\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                            lineNumber: 437,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                        lineNumber: 436,\n                                        columnNumber: 17\n                                    }, this),\n                                    (currentSubscription === null || currentSubscription === void 0 ? void 0 : currentSubscription.planId) === plan.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -top-3 right-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"bg-green-600 text-white px-3 py-1 text-xs font-medium rounded-full\",\n                                            children: \"Current Plan\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                            lineNumber: 445,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                        lineNumber: 444,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-semibold text-gray-900\",\n                                                children: plan.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                lineNumber: 452,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 mt-2\",\n                                                children: plan.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                lineNumber: 453,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-3xl font-bold text-gray-900\",\n                                                        children: formatPrice(plan.pricing[billingInterval])\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                        lineNumber: 456,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    plan.pricing[billingInterval] > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600 ml-1\",\n                                                        children: [\n                                                            \"/\",\n                                                            billingInterval === \"monthly\" ? \"month\" : \"year\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                        lineNumber: 460,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                lineNumber: 455,\n                                                columnNumber: 17\n                                            }, this),\n                                            billingInterval === \"yearly\" && plan.pricing.yearly > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-green-600 mt-1\",\n                                                children: [\n                                                    \"Save ₹\",\n                                                    ((plan.pricing.monthly * 12 - plan.pricing.yearly) / 100).toLocaleString(\"en-IN\"),\n                                                    \" per year\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                lineNumber: 467,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"mt-6 space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-4 h-4 text-green-500 mr-3\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M5 13l4 4L19 7\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                    lineNumber: 475,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 474,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: [\n                                                                    plan.features.maxInvoices === -1 ? \"Unlimited\" : plan.features.maxInvoices,\n                                                                    \" invoices/month\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 477,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                        lineNumber: 473,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-4 h-4 text-green-500 mr-3\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M5 13l4 4L19 7\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                    lineNumber: 483,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 482,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: [\n                                                                    plan.features.maxStorage,\n                                                                    \"MB storage\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 485,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                        lineNumber: 481,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    plan.features.documentAnalysis && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-4 h-4 text-green-500 mr-3\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M5 13l4 4L19 7\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                    lineNumber: 492,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 491,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: \"Document analysis\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 494,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                        lineNumber: 490,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    plan.features.prioritySupport && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-4 h-4 text-green-500 mr-3\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M5 13l4 4L19 7\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                    lineNumber: 500,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 499,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: \"Priority support\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 502,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                        lineNumber: 498,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    plan.features.apiAccess && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-4 h-4 text-green-500 mr-3\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M5 13l4 4L19 7\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                    lineNumber: 508,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 507,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: \"API access\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 510,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                        lineNumber: 506,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    plan.features.multiUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-4 h-4 text-green-500 mr-3\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M5 13l4 4L19 7\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                    lineNumber: 516,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 515,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: [\n                                                                    \"Up to \",\n                                                                    plan.features.maxUsers,\n                                                                    \" users\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 518,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                        lineNumber: 514,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                lineNumber: 472,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleUpgrade(plan.id),\n                                                disabled: upgrading === plan.id || (currentSubscription === null || currentSubscription === void 0 ? void 0 : currentSubscription.planId) === plan.id,\n                                                className: \"w-full mt-6 px-4 py-2 rounded-lg text-sm font-medium transition-colors \".concat((currentSubscription === null || currentSubscription === void 0 ? void 0 : currentSubscription.planId) === plan.id ? \"bg-green-100 text-green-800 cursor-not-allowed\" : plan.popular ? \"bg-indigo-600 text-white hover:bg-indigo-700 disabled:opacity-50\" : \"bg-gray-100 text-gray-900 hover:bg-gray-200 disabled:opacity-50\"),\n                                                children: upgrading === plan.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"animate-spin -ml-1 mr-3 h-4 w-4\",\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            fill: \"none\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                    className: \"opacity-25\",\n                                                                    cx: \"12\",\n                                                                    cy: \"12\",\n                                                                    r: \"10\",\n                                                                    stroke: \"currentColor\",\n                                                                    strokeWidth: \"4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                    lineNumber: 539,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    className: \"opacity-75\",\n                                                                    fill: \"currentColor\",\n                                                                    d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                    lineNumber: 540,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                            lineNumber: 538,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"Processing...\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                    lineNumber: 537,\n                                                    columnNumber: 21\n                                                }, this) : (currentSubscription === null || currentSubscription === void 0 ? void 0 : currentSubscription.planId) === plan.id ? \"Current Plan\" : plan.id === \"free\" ? \"Downgrade to Free\" : \"Upgrade to \".concat(plan.name)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                lineNumber: 525,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                        lineNumber: 451,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, plan.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                lineNumber: 427,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                        lineNumber: 425,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-xl shadow-sm border border-gray-200 p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold text-gray-900 mb-6\",\n                                children: \"Feature Comparison\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                lineNumber: 559,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"overflow-x-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                    className: \"min-w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                className: \"border-b border-gray-200\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-left py-3 px-4 font-medium text-gray-900\",\n                                                        children: \"Feature\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                        lineNumber: 564,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    plans.map((plan)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-center py-3 px-4 font-medium text-gray-900\",\n                                                            children: plan.name\n                                                        }, plan.id, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                            lineNumber: 566,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                lineNumber: 563,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                            lineNumber: 562,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                            className: \"divide-y divide-gray-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4 text-sm text-gray-900\",\n                                                            children: \"Monthly Invoices\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                            lineNumber: 574,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        plans.map((plan)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"text-center py-3 px-4 text-sm text-gray-900\",\n                                                                children: plan.features.maxInvoices === -1 ? \"∞\" : plan.features.maxInvoices\n                                                            }, plan.id, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 576,\n                                                                columnNumber: 21\n                                                            }, this))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                    lineNumber: 573,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4 text-sm text-gray-900\",\n                                                            children: \"Storage\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                            lineNumber: 582,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        plans.map((plan)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"text-center py-3 px-4 text-sm text-gray-900\",\n                                                                children: [\n                                                                    plan.features.maxStorage,\n                                                                    \"MB\"\n                                                                ]\n                                                            }, plan.id, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 584,\n                                                                columnNumber: 21\n                                                            }, this))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                    lineNumber: 581,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4 text-sm text-gray-900\",\n                                                            children: \"Document Analysis\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                            lineNumber: 590,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        plans.map((plan)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"text-center py-3 px-4\",\n                                                                children: plan.features.documentAnalysis ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-5 h-5 text-green-500 mx-auto\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M5 13l4 4L19 7\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                        lineNumber: 595,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                    lineNumber: 594,\n                                                                    columnNumber: 25\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-5 h-5 text-gray-300 mx-auto\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M6 18L18 6M6 6l12 12\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                        lineNumber: 599,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                    lineNumber: 598,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, plan.id, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 592,\n                                                                columnNumber: 21\n                                                            }, this))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                    lineNumber: 589,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4 text-sm text-gray-900\",\n                                                            children: \"Priority Support\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                            lineNumber: 606,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        plans.map((plan)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"text-center py-3 px-4\",\n                                                                children: plan.features.prioritySupport ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-5 h-5 text-green-500 mx-auto\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M5 13l4 4L19 7\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                        lineNumber: 611,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                    lineNumber: 610,\n                                                                    columnNumber: 25\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-5 h-5 text-gray-300 mx-auto\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M6 18L18 6M6 6l12 12\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                        lineNumber: 615,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                    lineNumber: 614,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, plan.id, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 608,\n                                                                columnNumber: 21\n                                                            }, this))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                    lineNumber: 605,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4 text-sm text-gray-900\",\n                                                            children: \"API Access\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                            lineNumber: 622,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        plans.map((plan)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"text-center py-3 px-4\",\n                                                                children: plan.features.apiAccess ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-5 h-5 text-green-500 mx-auto\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M5 13l4 4L19 7\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                        lineNumber: 627,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                    lineNumber: 626,\n                                                                    columnNumber: 25\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-5 h-5 text-gray-300 mx-auto\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M6 18L18 6M6 6l12 12\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                        lineNumber: 631,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                    lineNumber: 630,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, plan.id, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 624,\n                                                                columnNumber: 21\n                                                            }, this))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                    lineNumber: 621,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                            lineNumber: 572,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                    lineNumber: 561,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                lineNumber: 560,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                        lineNumber: 558,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                lineNumber: 328,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n        lineNumber: 322,\n        columnNumber: 5\n    }, this);\n}\n_s(SubscriptionPage, \"LOYJOYyij6YvjGWMxRhvseJEImk=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = SubscriptionPage;\nvar _c;\n$RefreshReg$(_c, \"SubscriptionPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/subscription/page.tsx\n"));

/***/ })

});