/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/dashboard/compliance/page";
exports.ids = ["app/dashboard/compliance/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fcompliance%2Fpage&page=%2Fdashboard%2Fcompliance%2Fpage&appPaths=%2Fdashboard%2Fcompliance%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fcompliance%2Fpage.tsx&appDir=C%3A%5CUsers%5Cgupta%5CDesktop%5CinvoNest%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cgupta%5CDesktop%5CinvoNest%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fcompliance%2Fpage&page=%2Fdashboard%2Fcompliance%2Fpage&appPaths=%2Fdashboard%2Fcompliance%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fcompliance%2Fpage.tsx&appDir=C%3A%5CUsers%5Cgupta%5CDesktop%5CinvoNest%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cgupta%5CDesktop%5CinvoNest%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9100\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: [\n        'compliance',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/compliance/page.tsx */ \"(rsc)/./src/app/dashboard/compliance/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/dashboard/compliance/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/dashboard/compliance/page\",\n        pathname: \"/dashboard/compliance\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fcompliance%2Fpage&page=%2Fdashboard%2Fcompliance%2Fpage&appPaths=%2Fdashboard%2Fcompliance%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fcompliance%2Fpage.tsx&appDir=C%3A%5CUsers%5Cgupta%5CDesktop%5CinvoNest%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cgupta%5CDesktop%5CinvoNest%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/react-hot-toast/dist/index.mjs */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AuthContext.tsx */ \"(ssr)/./src/contexts/AuthContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Ccompliance%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Ccompliance%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/compliance/page.tsx */ \"(ssr)/./src/app/dashboard/compliance/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2d1cHRhJTVDJTVDRGVza3RvcCU1QyU1Q2ludm9OZXN0JTVDJTVDZnJvbnRlbmQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNkYXNoYm9hcmQlNUMlNUNjb21wbGlhbmNlJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDBMQUE2SCIsInNvdXJjZXMiOlsid2VicGFjazovL2Zyb250ZW5kLz83OGZiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcZ3VwdGFcXFxcRGVza3RvcFxcXFxpbnZvTmVzdFxcXFxmcm9udGVuZFxcXFxzcmNcXFxcYXBwXFxcXGRhc2hib2FyZFxcXFxjb21wbGlhbmNlXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Ccompliance%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/dashboard/compliance/page.tsx":
/*!***********************************************!*\
  !*** ./src/app/dashboard/compliance/page.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CompliancePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _components_dashboard_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../components/dashboard/DashboardLayout */ \"(ssr)/./src/components/dashboard/DashboardLayout.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction CompliancePage() {\n    const [complianceItems, setComplianceItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [overdueActivity, setOverdueActivity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [selectedType, setSelectedType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [selectedMonth, setSelectedMonth] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date().getMonth() + 1);\n    const [selectedYear, setSelectedYear] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date().getFullYear());\n    const [showOverdueSection, setShowOverdueSection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [completingItems, setCompletingItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [clearingData, setClearingData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchComplianceData();\n        fetchComplianceStats();\n        fetchOverdueActivity();\n    }, [\n        selectedType,\n        selectedMonth,\n        selectedYear\n    ]);\n    const fetchComplianceData = async ()=>{\n        try {\n            const token = localStorage.getItem(\"token\");\n            if (!token) {\n                router.push(\"/login\");\n                return;\n            }\n            const params = new URLSearchParams({\n                type: selectedType,\n                month: selectedMonth.toString(),\n                year: selectedYear.toString()\n            });\n            const response = await fetch(`http://localhost:5000/api/compliance/calendar?${params}`, {\n                headers: {\n                    \"Authorization\": `Bearer ${token}`,\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            if (response.ok) {\n                const data = await response.json();\n                setComplianceItems(data.data);\n            } else {\n                console.error(\"Failed to fetch compliance data\");\n            }\n        } catch (error) {\n            console.error(\"Error fetching compliance data:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const fetchComplianceStats = async ()=>{\n        try {\n            const token = localStorage.getItem(\"token\");\n            const response = await fetch(\"http://localhost:5000/api/compliance/stats\", {\n                headers: {\n                    \"Authorization\": `Bearer ${token}`,\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            if (response.ok) {\n                const data = await response.json();\n                setStats(data.data);\n            }\n        } catch (error) {\n            console.error(\"Error fetching compliance stats:\", error);\n        }\n    };\n    const fetchOverdueActivity = async ()=>{\n        try {\n            const token = localStorage.getItem(\"token\");\n            const response = await fetch(\"http://localhost:5000/api/compliance/overdue-activity\", {\n                headers: {\n                    \"Authorization\": `Bearer ${token}`,\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            if (response.ok) {\n                const data = await response.json();\n                setOverdueActivity(data.data || []);\n            }\n        } catch (error) {\n            console.error(\"Error fetching overdue activity:\", error);\n            // Set empty array on error to prevent UI issues\n            setOverdueActivity([]);\n        }\n    };\n    const markAsCompleted = async (complianceId)=>{\n        try {\n            // Add to completing items set\n            setCompletingItems((prev)=>new Set(prev).add(complianceId));\n            const token = localStorage.getItem(\"token\");\n            const response = await fetch(`http://localhost:5000/api/compliance/${complianceId}/complete`, {\n                method: \"PATCH\",\n                headers: {\n                    \"Authorization\": `Bearer ${token}`,\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    notes: \"Marked as completed from dashboard\"\n                })\n            });\n            if (response.ok) {\n                // Immediately update the overdue activity list to remove the completed item\n                setOverdueActivity((prev)=>prev.filter((item)=>item._id !== complianceId));\n                // Refresh all data\n                await Promise.all([\n                    fetchComplianceData(),\n                    fetchComplianceStats(),\n                    fetchOverdueActivity()\n                ]);\n                console.log(\"✅ Compliance item marked as completed successfully\");\n            } else {\n                const errorData = await response.json();\n                console.error(\"Failed to mark compliance as completed:\", errorData.message);\n                alert(\"Failed to mark compliance as completed. Please try again.\");\n            }\n        } catch (error) {\n            console.error(\"Error marking compliance as completed:\", error);\n            alert(\"Error marking compliance as completed. Please check your connection and try again.\");\n        } finally{\n            // Remove from completing items set\n            setCompletingItems((prev)=>{\n                const newSet = new Set(prev);\n                newSet.delete(complianceId);\n                return newSet;\n            });\n        }\n    };\n    const clearAllComplianceData = async ()=>{\n        if (!confirm(\"⚠️ Are you sure you want to clear ALL compliance data? This action cannot be undone.\")) {\n            return;\n        }\n        try {\n            setClearingData(true);\n            const token = localStorage.getItem(\"token\");\n            const response = await fetch(\"http://localhost:5000/api/compliance/clear-all\", {\n                method: \"DELETE\",\n                headers: {\n                    \"Authorization\": `Bearer ${token}`,\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            if (response.ok) {\n                const data = await response.json();\n                alert(`✅ Successfully cleared ${data.data.deletedCount} compliance items`);\n                // Refresh all data\n                await Promise.all([\n                    fetchComplianceData(),\n                    fetchComplianceStats(),\n                    fetchOverdueActivity()\n                ]);\n            } else {\n                const errorData = await response.json();\n                alert(`❌ Failed to clear compliance data: ${errorData.message}`);\n            }\n        } catch (error) {\n            console.error(\"Error clearing compliance data:\", error);\n            alert(\"❌ Error clearing compliance data. Please try again.\");\n        } finally{\n            setClearingData(false);\n        }\n    };\n    const getPriorityColor = (priority)=>{\n        switch(priority){\n            case \"critical\":\n                return \"text-red-600 bg-red-50 border-red-200\";\n            case \"high\":\n                return \"text-orange-600 bg-orange-50 border-orange-200\";\n            case \"medium\":\n                return \"text-yellow-600 bg-yellow-50 border-yellow-200\";\n            case \"low\":\n                return \"text-green-600 bg-green-50 border-green-200\";\n            default:\n                return \"text-gray-800 bg-gray-50 border-gray-200\";\n        }\n    };\n    const getTypeColor = (type)=>{\n        switch(type){\n            case \"gst\":\n                return \"bg-blue-100 text-blue-800\";\n            case \"tds\":\n                return \"bg-purple-100 text-purple-800\";\n            case \"income_tax\":\n                return \"bg-green-100 text-green-800\";\n            case \"pf\":\n                return \"bg-orange-100 text-orange-800\";\n            case \"esi\":\n                return \"bg-pink-100 text-pink-800\";\n            default:\n                return \"bg-gray-100 text-gray-800\";\n        }\n    };\n    const getDaysUntilDue = (dueDate)=>{\n        const due = new Date(dueDate);\n        const today = new Date();\n        const diffTime = due.getTime() - today.getTime();\n        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n        return diffDays;\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                lineNumber: 261,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n            lineNumber: 260,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        title: \"Compliance Calendar\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"flex\",\n                    \"aria-label\": \"Breadcrumb\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                        className: \"flex items-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    href: \"/dashboard\",\n                                    className: \"text-gray-500 hover:text-gray-700 text-sm font-medium\",\n                                    children: \"Dashboard\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                    lineNumber: 273,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                lineNumber: 272,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"flex-shrink-0 h-4 w-4 text-gray-400\",\n                                    fill: \"currentColor\",\n                                    viewBox: \"0 0 20 20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        fillRule: \"evenodd\",\n                                        d: \"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z\",\n                                        clipRule: \"evenodd\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                        lineNumber: 279,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                    lineNumber: 278,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                lineNumber: 277,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gray-900 text-sm font-medium\",\n                                    children: \"Compliance Calendar\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                    lineNumber: 283,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                lineNumber: 282,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                        lineNumber: 271,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                    lineNumber: 270,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white shadow rounded-lg\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-6 py-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: \"Compliance Calendar\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                            lineNumber: 293,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-sm text-gray-600\",\n                                            children: \"Track and manage your tax compliance deadlines\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                            lineNumber: 294,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                    lineNumber: 292,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: clearAllComplianceData,\n                                            disabled: clearingData,\n                                            className: `px-4 py-2 rounded-md text-sm font-medium transition-colors flex items-center space-x-1 ${clearingData ? \"bg-gray-400 text-white cursor-not-allowed\" : \"bg-red-600 text-white hover:bg-red-700\"}`,\n                                            children: clearingData ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4 animate-spin\",\n                                                        fill: \"none\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                className: \"opacity-25\",\n                                                                cx: \"12\",\n                                                                cy: \"12\",\n                                                                r: \"10\",\n                                                                stroke: \"currentColor\",\n                                                                strokeWidth: \"4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                                lineNumber: 311,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                className: \"opacity-75\",\n                                                                fill: \"currentColor\",\n                                                                d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                                lineNumber: 312,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                        lineNumber: 310,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Clearing...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                        lineNumber: 314,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                            lineNumber: 319,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                        lineNumber: 318,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Clear All\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                        lineNumber: 321,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            href: \"/dashboard/compliance/settings\",\n                                            className: \"bg-white border border-gray-300 text-gray-800 px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-50 transition-colors\",\n                                            children: \"Settings\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                            lineNumber: 325,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            href: \"/dashboard/compliance/custom\",\n                                            className: \"bg-indigo-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-indigo-700 transition-colors\",\n                                            children: \"Add Custom\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                            lineNumber: 331,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                    lineNumber: 298,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                            lineNumber: 291,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                        lineNumber: 290,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                    lineNumber: 289,\n                    columnNumber: 9\n                }, this),\n                stats && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-4 gap-6 mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white text-sm font-medium\",\n                                                children: stats.totalActive\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                lineNumber: 349,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                            lineNumber: 348,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                        lineNumber: 347,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-900\",\n                                                children: \"Total Active\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                lineNumber: 353,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-semibold text-gray-900\",\n                                                children: stats.totalActive\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                lineNumber: 354,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                        lineNumber: 352,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                lineNumber: 346,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                            lineNumber: 345,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 bg-green-500 rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white text-sm font-medium\",\n                                                children: stats.completedThisMonth\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                lineNumber: 363,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                            lineNumber: 362,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                        lineNumber: 361,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-900\",\n                                                children: \"Completed This Month\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                lineNumber: 367,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-semibold text-gray-900\",\n                                                children: stats.completedThisMonth\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                lineNumber: 368,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                        lineNumber: 366,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                lineNumber: 360,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                            lineNumber: 359,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white text-sm font-medium\",\n                                                children: stats.upcomingCount\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                lineNumber: 377,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                            lineNumber: 376,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                        lineNumber: 375,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-900\",\n                                                children: \"Upcoming (30 days)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                lineNumber: 381,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-semibold text-gray-900\",\n                                                children: stats.upcomingCount\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                lineNumber: 382,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                        lineNumber: 380,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                lineNumber: 374,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                            lineNumber: 373,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `bg-white rounded-lg shadow p-6 transition-all duration-200 ${stats.overdueCount > 0 ? \"cursor-pointer hover:shadow-lg hover:bg-red-50\" : \"\"}`,\n                            onClick: ()=>stats.overdueCount > 0 && setShowOverdueSection(!showOverdueSection),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 bg-red-500 rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white text-sm font-medium\",\n                                                children: stats.overdueCount\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                lineNumber: 396,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                            lineNumber: 395,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                        lineNumber: 394,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-4 flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-900\",\n                                                children: \"Overdue\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                lineNumber: 400,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-semibold text-gray-900\",\n                                                children: stats.overdueCount\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                lineNumber: 401,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                        lineNumber: 399,\n                                        columnNumber: 17\n                                    }, this),\n                                    stats.overdueCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: `w-5 h-5 text-gray-400 transition-transform duration-200 ${showOverdueSection ? \"transform rotate-180\" : \"\"}`,\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M19 9l-7 7-7-7\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                lineNumber: 413,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                            lineNumber: 405,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                        lineNumber: 404,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                lineNumber: 393,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                            lineNumber: 387,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                    lineNumber: 344,\n                    columnNumber: 11\n                }, this),\n                stats && stats.overdueCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-6 py-4 border-b border-gray-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-3 h-3 bg-red-500 rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                lineNumber: 428,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-lg font-medium text-gray-900\",\n                                                children: [\n                                                    \"Overdue Activity (\",\n                                                    stats.overdueCount,\n                                                    \")\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                lineNumber: 429,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                        lineNumber: 427,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowOverdueSection(!showOverdueSection),\n                                        className: \"text-sm text-indigo-600 hover:text-indigo-800 font-medium\",\n                                        children: showOverdueSection ? \"Hide Details\" : \"View Details\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                        lineNumber: 433,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                lineNumber: 426,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                            lineNumber: 425,\n                            columnNumber: 13\n                        }, this),\n                        showOverdueSection && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-6 py-4\",\n                            children: overdueActivity.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-6 h-6 text-red-600\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                lineNumber: 448,\n                                                columnNumber: 25\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                            lineNumber: 447,\n                                            columnNumber: 23\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                        lineNumber: 446,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Loading overdue activity...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                        lineNumber: 451,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                lineNumber: 445,\n                                columnNumber: 19\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    overdueActivity.slice(0, 5).map((activity)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between p-4 bg-red-50 rounded-lg border border-red-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-sm font-medium text-gray-900\",\n                                                                    children: activity.complianceId.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                                    lineNumber: 459,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: `px-2 py-1 text-xs font-medium rounded-full ${activity.complianceId.priority === \"critical\" ? \"bg-red-100 text-red-800\" : activity.complianceId.priority === \"high\" ? \"bg-orange-100 text-orange-800\" : activity.complianceId.priority === \"medium\" ? \"bg-yellow-100 text-yellow-800\" : \"bg-gray-100 text-gray-800\"}`,\n                                                                    children: activity.complianceId.priority\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                                    lineNumber: 462,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                            lineNumber: 458,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-1 flex items-center space-x-4 text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-600\",\n                                                                    children: [\n                                                                        \"Due: \",\n                                                                        new Date(activity.nextDueDate).toLocaleDateString(\"en-IN\")\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                                    lineNumber: 472,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-red-600 font-medium\",\n                                                                    children: [\n                                                                        activity.daysOverdue,\n                                                                        \" days overdue\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                                    lineNumber: 475,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-600\",\n                                                                    children: [\n                                                                        \"Type: \",\n                                                                        activity.complianceId.type.toUpperCase()\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                                    lineNumber: 478,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                            lineNumber: 471,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        activity.notes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"mt-2 text-sm text-gray-600 italic\",\n                                                            children: [\n                                                                \"Note: \",\n                                                                activity.notes\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                            lineNumber: 483,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                    lineNumber: 457,\n                                                    columnNumber: 25\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"ml-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>markAsCompleted(activity._id),\n                                                        disabled: completingItems.has(activity._id),\n                                                        className: `px-3 py-1 text-xs font-medium rounded transition-colors flex items-center space-x-1 ${completingItems.has(activity._id) ? \"bg-gray-400 text-white cursor-not-allowed\" : \"bg-green-600 text-white hover:bg-green-700\"}`,\n                                                        children: completingItems.has(activity._id) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-3 h-3 animate-spin\",\n                                                                    fill: \"none\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                            className: \"opacity-25\",\n                                                                            cx: \"12\",\n                                                                            cy: \"12\",\n                                                                            r: \"10\",\n                                                                            stroke: \"currentColor\",\n                                                                            strokeWidth: \"4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                                            lineNumber: 501,\n                                                                            columnNumber: 35\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            className: \"opacity-75\",\n                                                                            fill: \"currentColor\",\n                                                                            d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                                            lineNumber: 502,\n                                                                            columnNumber: 35\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                                    lineNumber: 500,\n                                                                    columnNumber: 33\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Completing...\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                                    lineNumber: 504,\n                                                                    columnNumber: 33\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Mark Complete\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                            lineNumber: 507,\n                                                            columnNumber: 31\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                        lineNumber: 489,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                    lineNumber: 488,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, activity._id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                            lineNumber: 456,\n                                            columnNumber: 23\n                                        }, this)),\n                                    overdueActivity.length > 5 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center pt-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: [\n                                                \"Showing 5 of \",\n                                                overdueActivity.length,\n                                                \" overdue items\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                            lineNumber: 516,\n                                            columnNumber: 25\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                        lineNumber: 515,\n                                        columnNumber: 23\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                lineNumber: 454,\n                                columnNumber: 19\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                            lineNumber: 443,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                    lineNumber: 424,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow mb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-6 py-4 border-b border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-900 mb-2\",\n                                            children: \"Type\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                            lineNumber: 533,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: selectedType,\n                                            onChange: (e)=>setSelectedType(e.target.value),\n                                            className: \"bg-white border border-gray-300 rounded-md px-3 py-2 text-sm text-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 hover:border-gray-400 transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"all\",\n                                                    className: \"text-gray-900\",\n                                                    children: \"All Types\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                    lineNumber: 539,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"gst\",\n                                                    className: \"text-gray-900\",\n                                                    children: \"GST\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                    lineNumber: 540,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"tds\",\n                                                    className: \"text-gray-900\",\n                                                    children: \"TDS\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                    lineNumber: 541,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"income_tax\",\n                                                    className: \"text-gray-900\",\n                                                    children: \"Income Tax\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                    lineNumber: 542,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"pf\",\n                                                    className: \"text-gray-900\",\n                                                    children: \"PF\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                    lineNumber: 543,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"esi\",\n                                                    className: \"text-gray-900\",\n                                                    children: \"ESI\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                    lineNumber: 544,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"custom\",\n                                                    className: \"text-gray-900\",\n                                                    children: \"Custom\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                    lineNumber: 545,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                            lineNumber: 534,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                    lineNumber: 532,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-900 mb-2\",\n                                            children: \"Month\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                            lineNumber: 550,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: selectedMonth,\n                                            onChange: (e)=>setSelectedMonth(parseInt(e.target.value)),\n                                            className: \"bg-white border border-gray-300 rounded-md px-3 py-2 text-sm text-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 hover:border-gray-400 transition-colors\",\n                                            children: Array.from({\n                                                length: 12\n                                            }, (_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: i + 1,\n                                                    className: \"text-gray-900\",\n                                                    children: new Date(2024, i).toLocaleDateString(\"en-US\", {\n                                                        month: \"long\"\n                                                    })\n                                                }, i + 1, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                    lineNumber: 557,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                            lineNumber: 551,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                    lineNumber: 549,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-900 mb-2\",\n                                            children: \"Year\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                            lineNumber: 565,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: selectedYear,\n                                            onChange: (e)=>setSelectedYear(parseInt(e.target.value)),\n                                            className: \"bg-white border border-gray-300 rounded-md px-3 py-2 text-sm text-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 hover:border-gray-400 transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: 2024,\n                                                    className: \"text-gray-900\",\n                                                    children: \"2024\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                    lineNumber: 571,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: 2025,\n                                                    className: \"text-gray-900\",\n                                                    children: \"2025\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                    lineNumber: 572,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                            lineNumber: 566,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                    lineNumber: 564,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                            lineNumber: 531,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                        lineNumber: 530,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                    lineNumber: 529,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-6 py-4 border-b border-gray-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-lg font-medium text-gray-900\",\n                                children: [\n                                    \"Compliance Items (\",\n                                    complianceItems.length,\n                                    \")\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                lineNumber: 582,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                            lineNumber: 581,\n                            columnNumber: 11\n                        }, this),\n                        complianceItems.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-6 py-12 text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-900 text-base\",\n                                children: \"No compliance items found for the selected filters.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                lineNumber: 589,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                            lineNumber: 588,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"divide-y divide-gray-200\",\n                            children: complianceItems.map((item)=>{\n                                const daysUntilDue = getDaysUntilDue(item.nextDueDate);\n                                const isOverdue = daysUntilDue < 0;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-6 py-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-medium text-gray-900\",\n                                                                children: item.complianceId.title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                                lineNumber: 602,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: `px-2 py-1 text-xs font-medium rounded-full ${getTypeColor(item.complianceId.type)}`,\n                                                                children: item.complianceId.type.toUpperCase()\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                                lineNumber: 605,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: `px-2 py-1 text-xs font-medium rounded border ${getPriorityColor(item.complianceId.priority)}`,\n                                                                children: item.complianceId.priority\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                                lineNumber: 608,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                        lineNumber: 601,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"mt-1 text-sm text-gray-900\",\n                                                        children: item.complianceId.description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                        lineNumber: 613,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-2 flex items-center space-x-4 text-sm text-gray-900\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: [\n                                                                    \"Due: \",\n                                                                    new Date(item.nextDueDate).toLocaleDateString(\"en-IN\")\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                                lineNumber: 618,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: isOverdue ? \"text-red-600 font-medium\" : daysUntilDue <= 7 ? \"text-orange-600 font-medium\" : \"text-gray-900 font-medium\",\n                                                                children: isOverdue ? `${Math.abs(daysUntilDue)} days overdue` : `${daysUntilDue} days left`\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                                lineNumber: 621,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            item.complianceId.resources?.formNumber && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-900\",\n                                                                children: [\n                                                                    \"Form: \",\n                                                                    item.complianceId.resources.formNumber\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                                lineNumber: 625,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                        lineNumber: 617,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    item.complianceId.penaltyInfo?.lateFilingPenalty && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-2 text-sm text-red-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: \"Penalty:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                                lineNumber: 631,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            \" \",\n                                                            item.complianceId.penaltyInfo.lateFilingPenalty\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                        lineNumber: 630,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                lineNumber: 600,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    item.complianceId.resources?.officialLink && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: item.complianceId.resources.officialLink,\n                                                        target: \"_blank\",\n                                                        rel: \"noopener noreferrer\",\n                                                        className: \"text-indigo-600 hover:text-indigo-900 text-sm font-medium\",\n                                                        children: \"Official Link\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                        lineNumber: 638,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    !item.isCompleted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>markAsCompleted(item._id),\n                                                        disabled: completingItems.has(item._id),\n                                                        className: `px-3 py-1 text-sm font-medium rounded transition-colors flex items-center space-x-1 ${completingItems.has(item._id) ? \"bg-gray-400 text-white cursor-not-allowed\" : \"bg-green-600 text-white hover:bg-green-700\"}`,\n                                                        children: completingItems.has(item._id) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-4 h-4 animate-spin\",\n                                                                    fill: \"none\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                            className: \"opacity-25\",\n                                                                            cx: \"12\",\n                                                                            cy: \"12\",\n                                                                            r: \"10\",\n                                                                            stroke: \"currentColor\",\n                                                                            strokeWidth: \"4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                                            lineNumber: 661,\n                                                                            columnNumber: 35\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            className: \"opacity-75\",\n                                                                            fill: \"currentColor\",\n                                                                            d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                                            lineNumber: 662,\n                                                                            columnNumber: 35\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                                    lineNumber: 660,\n                                                                    columnNumber: 33\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Completing...\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                                    lineNumber: 664,\n                                                                    columnNumber: 33\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Mark Complete\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                            lineNumber: 667,\n                                                            columnNumber: 31\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                        lineNumber: 649,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    item.isCompleted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-green-600 text-sm font-medium\",\n                                                        children: \"✓ Completed\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                        lineNumber: 673,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                                lineNumber: 636,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                        lineNumber: 599,\n                                        columnNumber: 21\n                                    }, this)\n                                }, item._id, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                                    lineNumber: 598,\n                                    columnNumber: 19\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                            lineNumber: 592,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n                    lineNumber: 580,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n            lineNumber: 268,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\compliance\\\\page.tsx\",\n        lineNumber: 267,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/dashboard/compliance/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/auth/ProtectedRoute.tsx":
/*!************************************************!*\
  !*** ./src/components/auth/ProtectedRoute.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthLoadingSpinner: () => (/* binding */ AuthLoadingSpinner),\n/* harmony export */   LoadingSpinner: () => (/* binding */ LoadingSpinner),\n/* harmony export */   \"default\": () => (/* binding */ ProtectedRoute)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default,AuthLoadingSpinner,LoadingSpinner auto */ \n\n\n\nfunction ProtectedRoute({ children, fallback, redirectTo = \"/login\" }) {\n    const { isLoggedIn, loading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!loading && !isLoggedIn) {\n            router.push(redirectTo);\n        }\n    }, [\n        isLoggedIn,\n        loading,\n        router,\n        redirectTo\n    ]);\n    // Show loading spinner while checking authentication\n    if (loading) {\n        return fallback || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                lineNumber: 32,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n            lineNumber: 31,\n            columnNumber: 9\n        }, this);\n    }\n    // Don't render anything if not authenticated (redirect will happen)\n    if (!isLoggedIn) {\n        return null;\n    }\n    // Render children if authenticated\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n// Loading component for authentication checks\nconst AuthLoadingSpinner = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-16 w-16 border-b-2 border-indigo-600 mx-auto\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                    lineNumber: 54,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"mt-4 text-lg text-gray-600\",\n                    children: \"Authenticating...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                    lineNumber: 55,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"mt-2 text-sm text-gray-500\",\n                    children: \"Please wait while we verify your credentials\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n            lineNumber: 53,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n        lineNumber: 52,\n        columnNumber: 3\n    }, undefined);\n// Simple loading component\nconst LoadingSpinner = ({ size = \"md\", text = \"Loading...\" })=>{\n    const sizeClasses = {\n        sm: \"h-6 w-6\",\n        md: \"h-8 w-8\",\n        lg: \"h-12 w-12\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center justify-center p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `animate-spin rounded-full border-b-2 border-indigo-600 mx-auto ${sizeClasses[size]}`\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                    lineNumber: 75,\n                    columnNumber: 9\n                }, undefined),\n                text && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"mt-2 text-sm text-gray-600\",\n                    children: text\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                    lineNumber: 76,\n                    columnNumber: 18\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n            lineNumber: 74,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n        lineNumber: 73,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/auth/ProtectedRoute.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/dashboard/DashboardLayout.tsx":
/*!******************************************************!*\
  !*** ./src/components/dashboard/DashboardLayout.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DashboardHeader: () => (/* binding */ DashboardHeader),\n/* harmony export */   StatsCard: () => (/* binding */ StatsCard),\n/* harmony export */   \"default\": () => (/* binding */ DashboardLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../auth/ProtectedRoute */ \"(ssr)/./src/components/auth/ProtectedRoute.tsx\");\n/* harmony import */ var _mobile_MobileDashboardLayout__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../mobile/MobileDashboardLayout */ \"(ssr)/./src/components/mobile/MobileDashboardLayout.tsx\");\n/* __next_internal_client_entry_do_not_use__ default,DashboardHeader,StatsCard auto */ \n\n\n\n\n\n\n\nfunction DashboardLayout({ children, title, showBack = false, backHref = \"/dashboard\", actions, enablePullToRefresh = false, onRefresh }) {\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.usePathname)();\n    const { user, logout } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    // Check if device is mobile\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const checkMobile = ()=>{\n            setIsMobile(window.innerWidth < 1024);\n        };\n        checkMobile();\n        window.addEventListener(\"resize\", checkMobile);\n        return ()=>window.removeEventListener(\"resize\", checkMobile);\n    }, []);\n    // Remove unwanted floating elements\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const removeFloatingElements = ()=>{\n            // Target high z-index elements that might be browser extensions\n            const highZIndexElements = document.querySelectorAll('div[style*=\"z-index: 2147483647\"], div[style*=\"z-index: 999999\"]');\n            highZIndexElements.forEach((element)=>{\n                const isOurComponent = element.closest('[class*=\"dashboard\"]') || element.closest('[class*=\"chat\"]') || element.closest('[class*=\"mobile\"]') || element.id?.includes(\"dashboard\") || element.className?.includes(\"dashboard\");\n                if (!isOurComponent) {\n                    element.style.display = \"none\";\n                }\n            });\n            // Remove Next.js development toasts and overlays\n            const nextjsElements = document.querySelectorAll(\"[data-nextjs-toast], [data-nextjs-toast-wrapper]\");\n            nextjsElements.forEach((element)=>{\n                element.style.display = \"none\";\n            });\n            // Remove any floating circular avatars that aren't part of our app\n            const floatingElements = document.querySelectorAll('div[style*=\"position: fixed\"]');\n            floatingElements.forEach((element)=>{\n                const style = element.getAttribute(\"style\") || \"\";\n                const hasCircularStyle = style.includes(\"border-radius: 50%\") || style.includes(\"border-radius:50%\") || element.querySelector('[style*=\"border-radius: 50%\"]');\n                // Check if it's not one of our components\n                const isOurComponent = element.closest('[class*=\"dashboard\"]') || element.closest('[class*=\"chat\"]') || element.closest('[class*=\"mobile\"]') || element.id?.includes(\"invoNest\") || element.className?.includes(\"invoNest\");\n                if (hasCircularStyle && !isOurComponent) {\n                    element.style.display = \"none\";\n                }\n            });\n        };\n        // Run immediately and then periodically\n        removeFloatingElements();\n        const interval = setInterval(removeFloatingElements, 2000);\n        return ()=>clearInterval(interval);\n    }, []);\n    const navigation = [\n        {\n            name: \"Dashboard\",\n            href: \"/dashboard\",\n            icon: \"\\uD83C\\uDFE0\"\n        },\n        {\n            name: \"Invoices\",\n            href: \"/dashboard/invoices\",\n            icon: \"\\uD83E\\uDDFE\"\n        },\n        {\n            name: \"Create Invoice\",\n            href: \"/dashboard/invoices/create\",\n            icon: \"➕\"\n        },\n        {\n            name: \"Documents\",\n            href: \"/dashboard/documents\",\n            icon: \"\\uD83D\\uDCC1\"\n        },\n        {\n            name: \"Compliance\",\n            href: \"/dashboard/compliance\",\n            icon: \"\\uD83D\\uDCC5\"\n        },\n        {\n            name: \"Notifications\",\n            href: \"/dashboard/notifications\",\n            icon: \"\\uD83D\\uDD14\"\n        },\n        {\n            name: \"Subscription\",\n            href: \"/dashboard/subscription\",\n            icon: \"\\uD83D\\uDCB3\"\n        },\n        {\n            name: \"Profile\",\n            href: \"/dashboard/profile\",\n            icon: \"\\uD83D\\uDC64\"\n        },\n        {\n            name: \"Settings\",\n            href: \"/dashboard/settings\",\n            icon: \"⚙️\"\n        }\n    ];\n    const handleLogout = async ()=>{\n        await logout();\n    };\n    // Use mobile layout on mobile devices\n    if (isMobile) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mobile_MobileDashboardLayout__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            title: title || \"Dashboard\",\n            showBack: showBack,\n            backHref: backHref,\n            actions: actions,\n            enablePullToRefresh: enablePullToRefresh,\n            onRefresh: onRefresh,\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n            lineNumber: 117,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50\",\n            children: [\n                sidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"fixed inset-0 bg-gray-900/50 backdrop-blur-sm z-40 lg:hidden\",\n                    onClick: ()=>setSidebarOpen(false)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                    lineNumber: 135,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"lg:hidden fixed top-0 left-0 right-0 z-30 bg-white shadow-sm border-b border-gray-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between px-4 py-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setSidebarOpen(true),\n                                className: \"text-gray-700 hover:text-gray-900 p-2 rounded-lg hover:bg-gray-100 transition-colors\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-6 h-6\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M4 6h16M4 12h16M4 18h16\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-10 h-10 relative\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        src: \"/invologo.png\",\n                                        alt: \"InvoNest Logo\",\n                                        width: 40,\n                                        height: 40,\n                                        className: \"object-contain w-full h-full\",\n                                        priority: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-sm font-semibold text-gray-900 truncate max-w-24\",\n                                children: title || \"Dashboard\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                    lineNumber: 142,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-xl border-r border-gray-200 transform ${sidebarOpen ? \"translate-x-0\" : \"-translate-x-full\"} transition-transform duration-300 ease-in-out lg:translate-x-0 flex flex-col`,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between h-20 px-6 border-b border-gray-200 bg-white\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: \"/\",\n                                    className: \"group flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 relative\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                src: \"/invologo.png\",\n                                                alt: \"InvoNest Logo\",\n                                                width: 40,\n                                                height: 40,\n                                                className: \"object-contain w-full h-full\",\n                                                priority: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                                                lineNumber: 176,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                                            lineNumber: 175,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xl font-bold text-gray-900 group-hover:text-indigo-600 transition-colors\",\n                                            children: \"InvoNest\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setSidebarOpen(false),\n                                    className: \"lg:hidden text-gray-600 hover:text-gray-900 hover:bg-gray-100 p-2 rounded-lg transition-colors\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-6 h-6\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M6 18L18 6M6 6l12 12\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                                            lineNumber: 194,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                            lineNumber: 173,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"flex-1 overflow-y-auto py-6 px-4 chat-sidebar-scroll\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"space-y-2\",\n                                children: navigation.map((item)=>{\n                                    const isActive = pathname === item.href;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: item.href,\n                                            onClick: ()=>setSidebarOpen(false),\n                                            className: `flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 ${isActive ? \"bg-indigo-100 text-indigo-700 shadow-sm border border-indigo-200\" : \"text-gray-800 hover:bg-gray-50 hover:text-gray-900 hover:shadow-sm\"}`,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"mr-3 text-lg\",\n                                                    children: item.icon\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 215,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium\",\n                                                    children: item.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 216,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, item.name, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 19\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                            lineNumber: 200,\n                            columnNumber: 11\n                        }, this),\n                        user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-t border-gray-200 p-4 bg-gray-50\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-lg shadow-lg\",\n                                            children: user.name.charAt(0).toUpperCase()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 min-w-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-semibold text-gray-900 truncate\",\n                                                children: user.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                                                lineNumber: 234,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-700 truncate\",\n                                                children: user.email\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                                                lineNumber: 235,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleLogout,\n                                        className: \"p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-all duration-200\",\n                                        title: \"Logout\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-5 h-5\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                                                lineNumber: 243,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                                        lineNumber: 237,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                                lineNumber: 227,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                    lineNumber: 171,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"lg:pl-64 pt-16 lg:pt-0 min-h-screen\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"h-full overflow-y-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 lg:p-6\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                            lineNumber: 254,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                        lineNumber: 253,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                    lineNumber: 252,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n            lineNumber: 132,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n        lineNumber: 131,\n        columnNumber: 5\n    }, this);\n}\n// Header component for dashboard pages\nfunction DashboardHeader({ title, subtitle, actions }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white border-b border-gray-200 px-4 py-6 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-gray-900\",\n                            children: title\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                            lineNumber: 278,\n                            columnNumber: 11\n                        }, this),\n                        subtitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-1 text-sm text-gray-700\",\n                            children: subtitle\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                            lineNumber: 280,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                    lineNumber: 277,\n                    columnNumber: 9\n                }, this),\n                actions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: actions\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                    lineNumber: 284,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n            lineNumber: 276,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n        lineNumber: 275,\n        columnNumber: 5\n    }, this);\n}\n// Stats card component\nfunction StatsCard({ title, value, icon, color = \"indigo\", subtitle, trend }) {\n    const colorClasses = {\n        indigo: \"from-indigo-500 to-indigo-600\",\n        green: \"from-green-500 to-green-600\",\n        yellow: \"from-yellow-500 to-yellow-600\",\n        red: \"from-red-500 to-red-600\",\n        blue: \"from-blue-500 to-blue-600\",\n        purple: \"from-purple-500 to-purple-600\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-xl shadow-lg border border-gray-100 p-6 hover:shadow-xl transition-all duration-300 hover:-translate-y-1\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm font-medium text-gray-700 mb-1\",\n                            children: title\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                            lineNumber: 322,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-3xl font-bold text-gray-900\",\n                            children: value\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                            lineNumber: 323,\n                            columnNumber: 11\n                        }, this),\n                        subtitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-700 mt-1\",\n                            children: subtitle\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                            lineNumber: 325,\n                            columnNumber: 13\n                        }, this),\n                        trend && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center mt-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: `text-xs font-medium ${trend.positive ? \"text-green-600\" : \"text-red-600\"}`,\n                                    children: [\n                                        trend.positive ? \"↗\" : \"↘\",\n                                        \" \",\n                                        trend.value,\n                                        \"%\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                                    lineNumber: 329,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-gray-500 ml-1\",\n                                    children: trend.label\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                                    lineNumber: 332,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                            lineNumber: 328,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                    lineNumber: 321,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `w-14 h-14 bg-gradient-to-br ${colorClasses[color]} rounded-xl flex items-center justify-center shadow-lg`,\n                    children: icon\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                    lineNumber: 336,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n            lineNumber: 320,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n        lineNumber: 319,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/dashboard/DashboardLayout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/mobile/MobileBottomNav.tsx":
/*!***************************************************!*\
  !*** ./src/components/mobile/MobileBottomNav.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MobileBottomNav)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst navigationItems = [\n    {\n        name: \"Dashboard\",\n        href: \"/dashboard\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-6 h-6\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileBottomNav.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileBottomNav.tsx\",\n                    lineNumber: 21,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileBottomNav.tsx\",\n            lineNumber: 19,\n            columnNumber: 7\n        }, undefined),\n        activeIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-6 h-6\",\n            fill: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileBottomNav.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileBottomNav.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileBottomNav.tsx\",\n            lineNumber: 25,\n            columnNumber: 7\n        }, undefined)\n    },\n    {\n        name: \"Invoices\",\n        href: \"/dashboard/invoices\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-6 h-6\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileBottomNav.tsx\",\n                lineNumber: 36,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileBottomNav.tsx\",\n            lineNumber: 35,\n            columnNumber: 7\n        }, undefined),\n        activeIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-6 h-6\",\n            fill: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M7 3a2 2 0 00-2 2v14a2 2 0 002 2h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7zm2 9a1 1 0 100 2h6a1 1 0 100-2H9zm0 4a1 1 0 100 2h6a1 1 0 100-2H9z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileBottomNav.tsx\",\n                lineNumber: 41,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileBottomNav.tsx\",\n            lineNumber: 40,\n            columnNumber: 7\n        }, undefined)\n    },\n    {\n        name: \"Create\",\n        href: \"/dashboard/invoices/create\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-8 h-8\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M12 6v6m0 0v6m0-6h6m-6 0H6\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileBottomNav.tsx\",\n                lineNumber: 50,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileBottomNav.tsx\",\n            lineNumber: 49,\n            columnNumber: 7\n        }, undefined),\n        activeIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-8 h-8\",\n            fill: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M12 2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2zm5 11h-4v4a1 1 0 11-2 0v-4H7a1 1 0 110-2h4V7a1 1 0 112 0v4h4a1 1 0 110 2z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileBottomNav.tsx\",\n                lineNumber: 55,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileBottomNav.tsx\",\n            lineNumber: 54,\n            columnNumber: 7\n        }, undefined)\n    },\n    {\n        name: \"Notifications\",\n        href: \"/dashboard/notifications\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-6 h-6\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M15 17h5l-5 5v-5zM11 19H6a2 2 0 01-2-2V7a2 2 0 012-2h5m5 0v5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileBottomNav.tsx\",\n                lineNumber: 64,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileBottomNav.tsx\",\n            lineNumber: 63,\n            columnNumber: 7\n        }, undefined),\n        activeIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-6 h-6\",\n            fill: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M15 17h5l-5 5v-5zM11 19H6a2 2 0 01-2-2V7a2 2 0 012-2h5m5 0v5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileBottomNav.tsx\",\n                lineNumber: 69,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileBottomNav.tsx\",\n            lineNumber: 68,\n            columnNumber: 7\n        }, undefined)\n    },\n    {\n        name: \"Subscription\",\n        href: \"/dashboard/subscription\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-6 h-6\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileBottomNav.tsx\",\n                lineNumber: 78,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileBottomNav.tsx\",\n            lineNumber: 77,\n            columnNumber: 7\n        }, undefined),\n        activeIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-6 h-6\",\n            fill: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileBottomNav.tsx\",\n                lineNumber: 83,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileBottomNav.tsx\",\n            lineNumber: 82,\n            columnNumber: 7\n        }, undefined)\n    }\n];\nfunction MobileBottomNav() {\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"lg:hidden fixed bottom-0 left-0 right-0 z-40 bg-white border-t border-gray-200 safe-area-pb\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-around px-2 py-2\",\n            children: navigationItems.map((item)=>{\n                const isActive = pathname === item.href || item.href !== \"/dashboard\" && pathname.startsWith(item.href);\n                const isCreateButton = item.name === \"Create\";\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    href: item.href,\n                    className: `flex flex-col items-center justify-center min-w-0 flex-1 py-2 px-1 transition-all duration-200 ${isCreateButton ? \"relative -top-4\" : \"\"}`,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `flex items-center justify-center transition-all duration-200 ${isCreateButton ? `w-14 h-14 rounded-full shadow-lg ${isActive ? \"bg-gradient-to-r from-indigo-600 to-purple-600 text-white\" : \"bg-gradient-to-r from-indigo-500 to-purple-500 text-white hover:from-indigo-600 hover:to-purple-600\"}` : `w-10 h-10 rounded-lg ${isActive ? \"bg-indigo-100 text-indigo-600\" : \"text-gray-500 hover:text-indigo-600 hover:bg-indigo-50\"}`}`,\n                            children: isActive ? item.activeIcon : item.icon\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileBottomNav.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: `text-xs font-medium mt-1 transition-colors duration-200 ${isCreateButton ? \"text-indigo-600\" : isActive ? \"text-indigo-600\" : \"text-gray-500\"} ${isCreateButton ? \"hidden\" : \"block\"}`,\n                            children: item.name\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileBottomNav.tsx\",\n                            lineNumber: 124,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, item.name, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileBottomNav.tsx\",\n                    lineNumber: 100,\n                    columnNumber: 13\n                }, this);\n            })\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileBottomNav.tsx\",\n            lineNumber: 94,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileBottomNav.tsx\",\n        lineNumber: 93,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/mobile/MobileBottomNav.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/mobile/MobileDashboardLayout.tsx":
/*!*********************************************************!*\
  !*** ./src/components/mobile/MobileDashboardLayout.tsx ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MobileButton: () => (/* binding */ MobileButton),\n/* harmony export */   MobileCard: () => (/* binding */ MobileCard),\n/* harmony export */   MobileInput: () => (/* binding */ MobileInput),\n/* harmony export */   \"default\": () => (/* binding */ MobileDashboardLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../auth/ProtectedRoute */ \"(ssr)/./src/components/auth/ProtectedRoute.tsx\");\n/* harmony import */ var _MobileHeader__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./MobileHeader */ \"(ssr)/./src/components/mobile/MobileHeader.tsx\");\n/* harmony import */ var _MobileBottomNav__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./MobileBottomNav */ \"(ssr)/./src/components/mobile/MobileBottomNav.tsx\");\n/* __next_internal_client_entry_do_not_use__ default,MobileCard,MobileButton,MobileInput auto */ \n\n\n\n\n\nfunction MobileDashboardLayout({ children, title, showBack = false, backHref = \"/dashboard\", actions, enablePullToRefresh = false, onRefresh }) {\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [isRefreshing, setIsRefreshing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [pullDistance, setPullDistance] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [startY, setStartY] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const handleTouchStart = (e)=>{\n        if (!enablePullToRefresh) return;\n        setStartY(e.touches[0].clientY);\n    };\n    const handleTouchMove = (e)=>{\n        if (!enablePullToRefresh || isRefreshing) return;\n        const currentY = e.touches[0].clientY;\n        const distance = currentY - startY;\n        if (distance > 0 && window.scrollY === 0) {\n            setPullDistance(Math.min(distance * 0.5, 80));\n        }\n    };\n    const handleTouchEnd = async ()=>{\n        if (!enablePullToRefresh || isRefreshing) return;\n        if (pullDistance > 60 && onRefresh) {\n            setIsRefreshing(true);\n            try {\n                await onRefresh();\n            } catch (error) {\n                console.error(\"Refresh failed:\", error);\n            } finally{\n                setIsRefreshing(false);\n            }\n        }\n        setPullDistance(0);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-gray-50 lg:hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MobileHeader__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        title: title,\n                        showBack: showBack,\n                        backHref: backHref,\n                        actions: actions\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileDashboardLayout.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: `pt-14 pb-20 min-h-screen mobile-scroll ${enablePullToRefresh ? \"pull-to-refresh\" : \"\"} ${isRefreshing ? \"refreshing\" : \"\"}`,\n                        style: {\n                            transform: `translateY(${pullDistance}px)`,\n                            transition: pullDistance === 0 ? \"transform 0.3s ease\" : \"none\"\n                        },\n                        onTouchStart: handleTouchStart,\n                        onTouchMove: handleTouchMove,\n                        onTouchEnd: handleTouchEnd,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"safe-area-inset\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileDashboardLayout.tsx\",\n                            lineNumber: 89,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileDashboardLayout.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MobileBottomNav__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileDashboardLayout.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileDashboardLayout.tsx\",\n                lineNumber: 69,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden lg:block\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-h-screen bg-gray-50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold text-gray-900\",\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileDashboardLayout.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileDashboardLayout.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 13\n                            }, this),\n                            children\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileDashboardLayout.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileDashboardLayout.tsx\",\n                    lineNumber: 101,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileDashboardLayout.tsx\",\n                lineNumber: 100,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileDashboardLayout.tsx\",\n        lineNumber: 68,\n        columnNumber: 5\n    }, this);\n}\n// Mobile Card Component\nfunction MobileCard({ children, className = \"\", onClick, swipeActions }) {\n    const [swipeX, setSwipeX] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [startX, setStartX] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleTouchStart = (e)=>{\n        if (!swipeActions) return;\n        setStartX(e.touches[0].clientX);\n        setIsDragging(true);\n    };\n    const handleTouchMove = (e)=>{\n        if (!swipeActions || !isDragging) return;\n        const currentX = e.touches[0].clientX;\n        const distance = currentX - startX;\n        setSwipeX(Math.max(-120, Math.min(120, distance)));\n    };\n    const handleTouchEnd = ()=>{\n        if (!swipeActions) return;\n        if (swipeX > 60 && swipeActions.left) {\n            swipeActions.left.action();\n        } else if (swipeX < -60 && swipeActions.right) {\n            swipeActions.right.action();\n        }\n        setSwipeX(0);\n        setIsDragging(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `mobile-card ${swipeActions ? \"swipe-actions\" : \"\"} ${className}`,\n        children: [\n            swipeActions?.left && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"swipe-actions-left\",\n                style: {\n                    background: swipeActions.left.color || \"#10b981\"\n                },\n                children: swipeActions.left.icon\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileDashboardLayout.tsx\",\n                lineNumber: 163,\n                columnNumber: 9\n            }, this),\n            swipeActions?.right && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"swipe-actions-right\",\n                style: {\n                    background: swipeActions.right.color || \"#ef4444\"\n                },\n                children: swipeActions.right.icon\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileDashboardLayout.tsx\",\n                lineNumber: 169,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"swipe-content\",\n                style: {\n                    transform: `translateX(${swipeX}px)`\n                },\n                onTouchStart: handleTouchStart,\n                onTouchMove: handleTouchMove,\n                onTouchEnd: handleTouchEnd,\n                onClick: onClick,\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileDashboardLayout.tsx\",\n                lineNumber: 174,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileDashboardLayout.tsx\",\n        lineNumber: 161,\n        columnNumber: 5\n    }, this);\n}\n// Mobile Button Component\nfunction MobileButton({ children, variant = \"primary\", size = \"medium\", className = \"\", onClick, disabled = false, loading = false, ...props }) {\n    const baseClasses = \"mobile-btn flex items-center justify-center font-semibold transition-all duration-200\";\n    const variantClasses = {\n        primary: \"mobile-btn-primary\",\n        secondary: \"mobile-btn-secondary\",\n        outline: \"bg-transparent border-2 border-gray-300 text-gray-700 hover:border-gray-400\"\n    };\n    const sizeClasses = {\n        small: \"px-4 py-2 text-sm min-h-[36px]\",\n        medium: \"px-6 py-3 text-base min-h-[44px]\",\n        large: \"px-8 py-4 text-lg min-h-[52px]\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: `${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${className} ${disabled ? \"opacity-50 cursor-not-allowed\" : \"\"}`,\n        onClick: onClick,\n        disabled: disabled || loading,\n        ...props,\n        children: [\n            loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-5 h-5 border-2 border-current border-t-transparent rounded-full animate-spin mr-2\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileDashboardLayout.tsx\",\n                lineNumber: 232,\n                columnNumber: 9\n            }, this) : null,\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileDashboardLayout.tsx\",\n        lineNumber: 223,\n        columnNumber: 5\n    }, this);\n}\n// Mobile Input Component\nfunction MobileInput({ label, error, className = \"\", ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `space-y-2 ${className}`,\n        children: [\n            label && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                className: \"block text-sm font-medium text-gray-700\",\n                children: label\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileDashboardLayout.tsx\",\n                lineNumber: 254,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                className: `mobile-input w-full ${error ? \"border-red-500\" : \"\"}`,\n                ...props\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileDashboardLayout.tsx\",\n                lineNumber: 258,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-sm text-red-600\",\n                children: error\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileDashboardLayout.tsx\",\n                lineNumber: 263,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileDashboardLayout.tsx\",\n        lineNumber: 252,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/mobile/MobileDashboardLayout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/mobile/MobileHeader.tsx":
/*!************************************************!*\
  !*** ./src/components/mobile/MobileHeader.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NotificationBadge: () => (/* binding */ NotificationBadge),\n/* harmony export */   QuickActionButton: () => (/* binding */ QuickActionButton),\n/* harmony export */   \"default\": () => (/* binding */ MobileHeader)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default,QuickActionButton,NotificationBadge auto */ \n\n\n\n\nfunction MobileHeader({ title, showBack = false, backHref = \"/dashboard\", actions }) {\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"lg:hidden fixed top-0 left-0 right-0 z-40 bg-white border-b border-gray-200 safe-area-pt\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-between px-4 py-3 h-14\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3 flex-1\",\n                    children: [\n                        showBack ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            href: backHref,\n                            className: \"p-2 -ml-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-5 h-5\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M15 19l-7-7 7-7\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileHeader.tsx\",\n                                    lineNumber: 34,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileHeader.tsx\",\n                                lineNumber: 33,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileHeader.tsx\",\n                            lineNumber: 29,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            href: \"/\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-10 h-10 relative\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    src: \"/invologo.png\",\n                                    alt: \"InvoNest Logo\",\n                                    width: 40,\n                                    height: 40,\n                                    className: \"object-contain w-full h-full\",\n                                    priority: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileHeader.tsx\",\n                                    lineNumber: 40,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileHeader.tsx\",\n                                lineNumber: 39,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileHeader.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 min-w-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-lg font-semibold text-gray-900 truncate\",\n                                children: title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileHeader.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileHeader.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileHeader.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [\n                        actions,\n                        user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            href: \"/dashboard/settings\",\n                            className: \"p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors\",\n                            title: \"Settings\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-5 h-5\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileHeader.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileHeader.tsx\",\n                                        lineNumber: 72,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileHeader.tsx\",\n                                lineNumber: 70,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileHeader.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 13\n                        }, this),\n                        user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            href: \"/dashboard/profile\",\n                            className: \"flex items-center space-x-2 p-1 rounded-lg hover:bg-gray-100 transition-colors\",\n                            title: \"Profile\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-8 h-8 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-full flex items-center justify-center text-white font-semibold text-sm\",\n                                children: user.name.charAt(0).toUpperCase()\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileHeader.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileHeader.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileHeader.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileHeader.tsx\",\n            lineNumber: 25,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileHeader.tsx\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, this);\n}\n// Quick Action Button Component\nfunction QuickActionButton({ icon, onClick, className = \"\" }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        onClick: onClick,\n        className: `p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors ${className}`,\n        children: icon\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileHeader.tsx\",\n        lineNumber: 106,\n        columnNumber: 5\n    }, this);\n}\n// Notification Badge Component\nfunction NotificationBadge({ count }) {\n    if (count === 0) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs font-bold rounded-full flex items-center justify-center\",\n        children: count > 99 ? \"99+\" : count\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileHeader.tsx\",\n        lineNumber: 120,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/mobile/MobileHeader.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth),\n/* harmony export */   useRequireAuth: () => (/* binding */ useRequireAuth),\n/* harmony export */   withAuth: () => (/* binding */ withAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../lib/auth */ \"(ssr)/./src/lib/auth.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth,withAuth,useRequireAuth auto */ \n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst AuthProvider = ({ children })=>{\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // Initialize auth state\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initAuth = async ()=>{\n            try {\n                if ((0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.isAuthenticated)()) {\n                    // Try to get user from localStorage first\n                    const cachedUser = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.getCurrentUser)();\n                    if (cachedUser) {\n                        setUser(cachedUser);\n                    }\n                    // Then refresh from server\n                    const freshUser = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.getProfile)();\n                    if (freshUser) {\n                        setUser(freshUser);\n                    } else {\n                        // Token might be expired, clear auth state\n                        (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.removeTokens)();\n                        setUser(null);\n                    }\n                }\n            } catch (error) {\n                console.error(\"Auth initialization error:\", error);\n                (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.removeTokens)();\n                setUser(null);\n            } finally{\n                setLoading(false);\n            }\n        };\n        initAuth();\n    }, []);\n    const login = async (data, remember = false)=>{\n        try {\n            const response = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.login)(data);\n            if (response.success && response.data) {\n                (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.setTokens)(response.data.token, response.data.refreshToken, remember);\n                (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.setCurrentUser)(response.data.user);\n                setUser(response.data.user);\n            }\n            return response;\n        } catch (error) {\n            console.error(\"Login error:\", error);\n            return {\n                success: false,\n                message: \"An unexpected error occurred during login.\"\n            };\n        }\n    };\n    const register = async (data)=>{\n        try {\n            const response = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.register)(data);\n            if (response.success && response.data) {\n                (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.setTokens)(response.data.token, response.data.refreshToken, true);\n                (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.setCurrentUser)(response.data.user);\n                setUser(response.data.user);\n            }\n            return response;\n        } catch (error) {\n            console.error(\"Register error:\", error);\n            return {\n                success: false,\n                message: \"An unexpected error occurred during registration.\"\n            };\n        }\n    };\n    const logout = async ()=>{\n        try {\n            await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.logout)();\n        } catch (error) {\n            console.error(\"Logout error:\", error);\n        } finally{\n            setUser(null);\n            router.push(\"/login\");\n        }\n    };\n    const updateUser = (updatedUser)=>{\n        setUser(updatedUser);\n        (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.setCurrentUser)(updatedUser);\n    };\n    const refreshUser = async ()=>{\n        try {\n            const freshUser = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.getProfile)();\n            if (freshUser) {\n                setUser(freshUser);\n            }\n        } catch (error) {\n            console.error(\"Refresh user error:\", error);\n        }\n    };\n    const value = {\n        user,\n        loading,\n        isLoggedIn: !!user,\n        login,\n        register,\n        logout,\n        updateUser,\n        refreshUser\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 155,\n        columnNumber: 5\n    }, undefined);\n};\nconst useAuth = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n};\n// Higher-order component for protected routes\nconst withAuth = (WrappedComponent)=>{\n    const AuthenticatedComponent = (props)=>{\n        const { isLoggedIn, loading } = useAuth();\n        const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n        (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n            if (!loading && !isLoggedIn) {\n                router.push(\"/login\");\n            }\n        }, [\n            isLoggedIn,\n            loading,\n            router\n        ]);\n        if (loading) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                    lineNumber: 186,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                lineNumber: 185,\n                columnNumber: 9\n            }, undefined);\n        }\n        if (!isLoggedIn) {\n            return null;\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WrappedComponent, {\n            ...props\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n            lineNumber: 195,\n            columnNumber: 12\n        }, undefined);\n    };\n    AuthenticatedComponent.displayName = `withAuth(${WrappedComponent.displayName || WrappedComponent.name})`;\n    return AuthenticatedComponent;\n};\n// Hook for protected routes\nconst useRequireAuth = ()=>{\n    const { isLoggedIn, loading } = useAuth();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!loading && !isLoggedIn) {\n            router.push(\"/login\");\n        }\n    }, [\n        isLoggedIn,\n        loading,\n        router\n    ]);\n    return {\n        isLoggedIn,\n        loading\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authenticatedFetch: () => (/* binding */ authenticatedFetch),\n/* harmony export */   getCurrentUser: () => (/* binding */ getCurrentUser),\n/* harmony export */   getProfile: () => (/* binding */ getProfile),\n/* harmony export */   getRefreshToken: () => (/* binding */ getRefreshToken),\n/* harmony export */   getToken: () => (/* binding */ getToken),\n/* harmony export */   isAuthenticated: () => (/* binding */ isAuthenticated),\n/* harmony export */   login: () => (/* binding */ login),\n/* harmony export */   logout: () => (/* binding */ logout),\n/* harmony export */   refreshAuthToken: () => (/* binding */ refreshAuthToken),\n/* harmony export */   register: () => (/* binding */ register),\n/* harmony export */   removeTokens: () => (/* binding */ removeTokens),\n/* harmony export */   setCurrentUser: () => (/* binding */ setCurrentUser),\n/* harmony export */   setTokens: () => (/* binding */ setTokens),\n/* harmony export */   updateProfile: () => (/* binding */ updateProfile)\n/* harmony export */ });\n// Authentication utilities and API functions\nconst API_BASE_URL = \"http://localhost:5000\" || 0;\n// Token management\nconst getToken = ()=>{\n    if (true) return null;\n    return localStorage.getItem(\"token\") || sessionStorage.getItem(\"token\");\n};\nconst getRefreshToken = ()=>{\n    if (true) return null;\n    return localStorage.getItem(\"refreshToken\") || sessionStorage.getItem(\"refreshToken\");\n};\nconst setTokens = (token, refreshToken, remember = false)=>{\n    if (true) return;\n    const storage = remember ? localStorage : sessionStorage;\n    storage.setItem(\"token\", token);\n    storage.setItem(\"refreshToken\", refreshToken);\n    // Also store in localStorage for consistency\n    localStorage.setItem(\"token\", token);\n};\nconst removeTokens = ()=>{\n    if (true) return;\n    localStorage.removeItem(\"token\");\n    localStorage.removeItem(\"refreshToken\");\n    localStorage.removeItem(\"user\");\n    sessionStorage.removeItem(\"token\");\n    sessionStorage.removeItem(\"refreshToken\");\n    sessionStorage.removeItem(\"user\");\n};\nconst getCurrentUser = ()=>{\n    if (true) return null;\n    const userStr = localStorage.getItem(\"user\") || sessionStorage.getItem(\"user\");\n    if (!userStr) return null;\n    try {\n        return JSON.parse(userStr);\n    } catch  {\n        return null;\n    }\n};\nconst setCurrentUser = (user)=>{\n    if (true) return;\n    localStorage.setItem(\"user\", JSON.stringify(user));\n};\n// API functions\nconst login = async (data)=>{\n    try {\n        console.log(\"\\uD83D\\uDD10 Login attempt:\", {\n            email: data.email,\n            passwordLength: data.password?.length\n        });\n        console.log(\"\\uD83C\\uDF10 API URL:\", `${API_BASE_URL}/api/auth/login`);\n        const response = await fetch(`${API_BASE_URL}/api/auth/login`, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(data)\n        });\n        console.log(\"\\uD83D\\uDCE1 Response status:\", response.status, response.statusText);\n        const result = await response.json();\n        console.log(\"\\uD83D\\uDCCB Response data:\", result);\n        return result;\n    } catch (error) {\n        console.error(\"❌ Login error:\", error);\n        return {\n            success: false,\n            message: \"Network error. Please check your connection and try again.\"\n        };\n    }\n};\nconst register = async (data)=>{\n    try {\n        const response = await fetch(`${API_BASE_URL}/api/auth/register`, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(data)\n        });\n        return await response.json();\n    } catch (error) {\n        console.error(\"Register error:\", error);\n        return {\n            success: false,\n            message: \"Network error. Please check your connection and try again.\"\n        };\n    }\n};\nconst logout = async ()=>{\n    try {\n        const token = getToken();\n        if (token) {\n            await fetch(`${API_BASE_URL}/api/auth/logout`, {\n                method: \"POST\",\n                headers: {\n                    \"Authorization\": `Bearer ${token}`,\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n        }\n    } catch (error) {\n        console.error(\"Logout error:\", error);\n    } finally{\n        removeTokens();\n    }\n};\nconst getProfile = async ()=>{\n    try {\n        const token = getToken();\n        if (!token) return null;\n        const response = await fetch(`${API_BASE_URL}/api/auth/profile`, {\n            headers: {\n                \"Authorization\": `Bearer ${token}`,\n                \"Content-Type\": \"application/json\"\n            }\n        });\n        const data = await response.json();\n        if (data.success) {\n            setCurrentUser(data.data.user);\n            return data.data.user;\n        }\n        return null;\n    } catch (error) {\n        console.error(\"Get profile error:\", error);\n        return null;\n    }\n};\nconst updateProfile = async (userData)=>{\n    try {\n        const token = getToken();\n        if (!token) {\n            return {\n                success: false,\n                message: \"No authentication token found\"\n            };\n        }\n        console.log(\"Sending profile update request:\", userData);\n        const response = await fetch(`${API_BASE_URL}/api/auth/profile`, {\n            method: \"PUT\",\n            headers: {\n                \"Authorization\": `Bearer ${token}`,\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(userData)\n        });\n        const data = await response.json();\n        console.log(\"Profile update response:\", data);\n        if (data.success) {\n            setCurrentUser(data.data.user);\n        }\n        return data;\n    } catch (error) {\n        console.error(\"Update profile error:\", error);\n        return {\n            success: false,\n            message: \"Network error. Please check your connection and try again.\"\n        };\n    }\n};\n// Check if user is authenticated\nconst isAuthenticated = ()=>{\n    return !!getToken();\n};\n// Create authenticated fetch function\nconst authenticatedFetch = async (url, options = {})=>{\n    const token = getToken();\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        ...options.headers || {}\n    };\n    if (token) {\n        headers[\"Authorization\"] = `Bearer ${token}`;\n    }\n    return fetch(url.startsWith(\"http\") ? url : `${API_BASE_URL}${url}`, {\n        ...options,\n        headers\n    });\n};\n// Refresh token function\nconst refreshAuthToken = async ()=>{\n    try {\n        const refreshToken = getRefreshToken();\n        if (!refreshToken) return false;\n        const response = await fetch(`${API_BASE_URL}/api/auth/refresh`, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                refreshToken\n            })\n        });\n        const data = await response.json();\n        if (data.success) {\n            setTokens(data.data.token, data.data.refreshToken);\n            return true;\n        }\n        return false;\n    } catch (error) {\n        console.error(\"Token refresh error:\", error);\n        return false;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"1f6b6a191842\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZnJvbnRlbmQvLi9zcmMvYXBwL2dsb2JhbHMuY3NzPzc1YzEiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIxZjZiNmExOTE4NDJcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/dashboard/compliance/page.tsx":
/*!***********************************************!*\
  !*** ./src/app/dashboard/compliance/page.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\invoNest\frontend\src\app\dashboard\compliance\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata),\n/* harmony export */   viewport: () => (/* binding */ viewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-inter\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../contexts/AuthContext */ \"(rsc)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"(rsc)/./node_modules/react-hot-toast/dist/index.mjs\");\n\n\n\n\n\nconst metadata = {\n    title: \"InvoNest - AI-Powered Invoicing & Compliance Platform\",\n    description: \"Secure, AI-powered invoicing and compliance platform for Indian MSMEs, freelancers, and gig workers. GST-compliant invoice generation with blockchain integrity.\",\n    keywords: [\n        \"invoicing\",\n        \"GST\",\n        \"compliance\",\n        \"AI\",\n        \"MSME\",\n        \"India\",\n        \"tax\",\n        \"blockchain\"\n    ],\n    authors: [\n        {\n            name: \"InvoNest Team\"\n        }\n    ]\n};\nconst viewport = {\n    width: \"device-width\",\n    initialScale: 1,\n    maximumScale: 1,\n    userScalable: false,\n    viewportFit: \"cover\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.svg\",\n                        type: \"image/svg+xml\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\",\n                        sizes: \"32x32\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"manifest\",\n                        href: \"/manifest.json\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"theme-color\",\n                        content: \"#4f46e5\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-capable\",\n                        content: \"yes\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-status-bar-style\",\n                        content: \"default\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-title\",\n                        content: \"InvoNest\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        href: \"/icons/icon-192x192.svg\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"mobile-web-app-capable\",\n                        content: \"yes\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"application-name\",\n                        content: \"InvoNest\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"msapplication-TileColor\",\n                        content: \"#4f46e5\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"msapplication-TileImage\",\n                        content: \"/icons/icon-144x144.svg\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"format-detection\",\n                        content: \"telephone=no\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 9\n                    }, this),\n                     true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                                httpEquiv: \"Cache-Control\",\n                                content: \"no-cache, no-store, must-revalidate\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 50,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                                httpEquiv: \"Pragma\",\n                                content: \"no-cache\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 51,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                                httpEquiv: \"Expires\",\n                                content: \"0\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 52,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                                name: \"cache-bust\",\n                                content: Date.now().toString()\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                                src: \"/disable-turbopack-overlay.js\",\n                                defer: true\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().variable)} font-sans antialiased bg-gray-50 touch-manipulation`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"min-h-screen safe-area-inset\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.Toaster, {\n                            position: \"top-right\",\n                            toastOptions: {\n                                duration: 4000,\n                                style: {\n                                    background: \"#363636\",\n                                    color: \"#fff\"\n                                },\n                                success: {\n                                    duration: 3000,\n                                    iconTheme: {\n                                        primary: \"#4ade80\",\n                                        secondary: \"#fff\"\n                                    }\n                                },\n                                error: {\n                                    duration: 4000,\n                                    iconTheme: {\n                                        primary: \"#ef4444\",\n                                        secondary: \"#fff\"\n                                    }\n                                }\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 63,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 59,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ e0),
/* harmony export */   useAuth: () => (/* binding */ e1),
/* harmony export */   useRequireAuth: () => (/* binding */ e3),
/* harmony export */   withAuth: () => (/* binding */ e2)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\invoNest\frontend\src\contexts\AuthContext.tsx#AuthProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\invoNest\frontend\src\contexts\AuthContext.tsx#useAuth`);

const e2 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\invoNest\frontend\src\contexts\AuthContext.tsx#withAuth`);

const e3 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\invoNest\frontend\src\contexts\AuthContext.tsx#useRequireAuth`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/react-hot-toast","vendor-chunks/goober","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fcompliance%2Fpage&page=%2Fdashboard%2Fcompliance%2Fpage&appPaths=%2Fdashboard%2Fcompliance%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fcompliance%2Fpage.tsx&appDir=C%3A%5CUsers%5Cgupta%5CDesktop%5CinvoNest%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cgupta%5CDesktop%5CinvoNest%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();