"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const seedComplianceData_1 = __importDefault(require("./seedComplianceData"));
const seedNotificationData_1 = __importDefault(require("./seedNotificationData"));
async function seedDashboardData() {
    console.log('🚀 Starting dashboard data seeding...');
    try {
        // Seed compliance data first
        await (0, seedComplianceData_1.default)();
        // Then seed notification data
        await (0, seedNotificationData_1.default)();
        console.log('✨ All dashboard data seeded successfully!');
        console.log('🔄 Please refresh your dashboard to see the new data.');
    }
    catch (error) {
        console.error('❌ Error seeding dashboard data:', error);
    }
}
// Run if called directly
if (require.main === module) {
    seedDashboardData();
}
exports.default = seedDashboardData;
//# sourceMappingURL=seedDashboardData.js.map