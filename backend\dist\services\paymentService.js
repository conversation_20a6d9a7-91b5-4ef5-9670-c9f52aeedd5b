"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentService = void 0;
const crypto_1 = __importDefault(require("crypto"));
const razorpay_1 = __importDefault(require("razorpay"));
const Payment_1 = __importDefault(require("../models/Payment"));
const Subscription_1 = __importDefault(require("../models/Subscription"));
const User_1 = __importDefault(require("../models/User"));
// Check if Razorpay keys are configured
const isRazorpayConfigured = process.env.RAZORPAY_KEY_ID && process.env.RAZORPAY_KEY_SECRET;
// Initialize Razorpay with environment variables
const razorpay = isRazorpayConfigured
    ? new razorpay_1.default({
        key_id: process.env.RAZORPAY_KEY_ID || '',
        key_secret: process.env.RAZORPAY_KEY_SECRET || ''
    })
    : null;
class PaymentService {
    /**
     * Create Razorpay order for subscription payment
     */
    static async createSubscriptionOrder(userId, planId, interval = 'monthly') {
        try {
            // Check if Razorpay is configured
            if (!isRazorpayConfigured) {
                throw new Error('Razorpay is not configured. Please set RAZORPAY_KEY_ID and RAZORPAY_KEY_SECRET environment variables.');
            }
            // Get user and subscription details
            const user = await User_1.default.findById(userId);
            if (!user) {
                throw new Error('User not found');
            }
            // Get plan pricing
            const pricing = Subscription_1.default.getPlanPricing();
            const amount = pricing[planId][interval];
            if (amount === 0) {
                throw new Error('Cannot create payment order for free plan');
            }
            // Create Razorpay order
            // Generate short receipt ID (max 40 chars) - use last 8 chars of userId + timestamp
            const shortUserId = userId.slice(-8);
            const shortTimestamp = Date.now().toString().slice(-8);
            const receipt = `sub_${shortUserId}_${shortTimestamp}`;
            console.log(`💳 Creating Razorpay order with receipt: ${receipt} (length: ${receipt.length})`);
            const orderOptions = {
                amount: amount, // Amount in paise
                currency: 'INR',
                receipt: receipt, // Max 40 characters
                notes: {
                    userId,
                    planId,
                    interval,
                    type: 'subscription'
                }
            };
            const order = await razorpay.orders.create(orderOptions);
            // Create payment record
            const subscription = await Subscription_1.default.findOne({ userId });
            const payment = new Payment_1.default({
                userId,
                subscriptionId: subscription?._id,
                razorpayOrderId: order.id,
                amount,
                currency: 'INR',
                status: 'pending',
                description: `${this.getPlanName(planId)} - ${interval} subscription`,
                metadata: {
                    planId,
                    interval,
                    orderDetails: order
                }
            });
            await payment.save();
            return {
                orderId: order.id,
                amount: order.amount,
                currency: order.currency,
                paymentId: payment._id,
                key: process.env.RAZORPAY_KEY_ID
            };
        }
        catch (error) {
            console.error('Error creating subscription order:', error);
            throw error;
        }
    }
    /**
     * Verify payment signature
     */
    static verifyPaymentSignature(razorpayOrderId, razorpayPaymentId, razorpaySignature) {
        try {
            if (!isRazorpayConfigured) {
                throw new Error('Razorpay is not configured');
            }
            const body = razorpayOrderId + '|' + razorpayPaymentId;
            const expectedSignature = crypto_1.default
                .createHmac('sha256', process.env.RAZORPAY_KEY_SECRET || '')
                .update(body.toString())
                .digest('hex');
            return expectedSignature === razorpaySignature;
        }
        catch (error) {
            console.error('Error verifying payment signature:', error);
            return false;
        }
    }
    /**
     * Handle successful payment
     */
    static async handleSuccessfulPayment(razorpayOrderId, razorpayPaymentId, razorpaySignature) {
        try {
            // Verify signature
            const isValid = this.verifyPaymentSignature(razorpayOrderId, razorpayPaymentId, razorpaySignature);
            if (!isValid) {
                throw new Error('Invalid payment signature');
            }
            // Find payment record
            const payment = await Payment_1.default.findOne({ razorpayOrderId });
            if (!payment) {
                throw new Error('Payment record not found');
            }
            // Update payment status
            payment.razorpayPaymentId = razorpayPaymentId;
            payment.razorpaySignature = razorpaySignature;
            payment.status = 'completed';
            payment.paidAt = new Date();
            await payment.save();
            // Update or create subscription
            const { planId, interval } = payment.metadata;
            let subscription = await Subscription_1.default.findOne({ userId: payment.userId });
            if (subscription) {
                // Upgrade existing subscription
                const features = Subscription_1.default.getPlanFeatures(planId);
                subscription.planId = planId;
                subscription.planName = this.getPlanName(planId);
                subscription.amount = payment.amount;
                subscription.interval = interval;
                subscription.features = features;
                subscription.status = 'active';
                // Extend period
                const now = new Date();
                subscription.currentPeriodStart = now;
                const periodEnd = new Date(now);
                if (interval === 'monthly') {
                    periodEnd.setMonth(periodEnd.getMonth() + 1);
                }
                else {
                    periodEnd.setFullYear(periodEnd.getFullYear() + 1);
                }
                subscription.currentPeriodEnd = periodEnd;
                await subscription.save();
            }
            else {
                // Create new subscription
                const SubscriptionService = require('./subscriptionService').default;
                subscription = await SubscriptionService.createSubscription(payment.userId.toString(), planId, interval, false // No trial for paid subscriptions
                );
            }
            return {
                payment,
                subscription
            };
        }
        catch (error) {
            console.error('Error handling successful payment:', error);
            throw error;
        }
    }
    /**
     * Handle failed payment
     */
    static async handleFailedPayment(razorpayOrderId, failureReason) {
        try {
            const payment = await Payment_1.default.findOne({ razorpayOrderId });
            if (payment) {
                payment.status = 'failed';
                payment.failureReason = failureReason;
                await payment.save();
            }
            return payment;
        }
        catch (error) {
            console.error('Error handling failed payment:', error);
            throw error;
        }
    }
    /**
     * Create refund
     */
    static async createRefund(paymentId, amount, reason) {
        try {
            // Check if Razorpay is configured
            if (!isRazorpayConfigured) {
                throw new Error('Razorpay is not configured. Please set RAZORPAY_KEY_ID and RAZORPAY_KEY_SECRET environment variables.');
            }
            const payment = await Payment_1.default.findById(paymentId);
            if (!payment || !payment.razorpayPaymentId) {
                throw new Error('Payment not found or not completed');
            }
            const refundAmount = amount || payment.amount;
            const refund = await razorpay.payments.refund(payment.razorpayPaymentId, {
                amount: refundAmount,
                notes: {
                    reason: reason || 'Subscription cancellation'
                }
            });
            // Update payment record
            payment.status = 'refunded';
            payment.refundId = refund.id;
            payment.refundAmount = refundAmount;
            payment.refundReason = reason;
            await payment.save();
            return refund;
        }
        catch (error) {
            console.error('Error creating refund:', error);
            throw error;
        }
    }
    /**
     * Get payment history for user
     */
    static async getPaymentHistory(userId, page = 1, limit = 10) {
        try {
            const skip = (page - 1) * limit;
            const [payments, total] = await Promise.all([
                Payment_1.default.find({ userId })
                    .sort({ createdAt: -1 })
                    .skip(skip)
                    .limit(limit)
                    .lean(),
                Payment_1.default.countDocuments({ userId })
            ]);
            return {
                payments,
                pagination: {
                    current: page,
                    pages: Math.ceil(total / limit),
                    total,
                    limit
                }
            };
        }
        catch (error) {
            console.error('Error getting payment history:', error);
            throw error;
        }
    }
    /**
     * Get revenue analytics
     */
    static async getRevenueAnalytics(startDate, endDate) {
        try {
            const matchStage = {
                status: 'completed'
            };
            if (startDate || endDate) {
                matchStage.createdAt = {};
                if (startDate)
                    matchStage.createdAt.$gte = startDate;
                if (endDate)
                    matchStage.createdAt.$lte = endDate;
            }
            const analytics = await Payment_1.default.aggregate([
                { $match: matchStage },
                {
                    $group: {
                        _id: null,
                        totalRevenue: { $sum: '$amount' },
                        totalTransactions: { $sum: 1 },
                        averageOrderValue: { $avg: '$amount' }
                    }
                }
            ]);
            const planBreakdown = await Payment_1.default.aggregate([
                { $match: matchStage },
                {
                    $group: {
                        _id: '$metadata.planId',
                        revenue: { $sum: '$amount' },
                        count: { $sum: 1 }
                    }
                }
            ]);
            return {
                overview: analytics[0] || {
                    totalRevenue: 0,
                    totalTransactions: 0,
                    averageOrderValue: 0
                },
                planBreakdown
            };
        }
        catch (error) {
            console.error('Error getting revenue analytics:', error);
            throw error;
        }
    }
    /**
     * Helper method to get plan name
     */
    static getPlanName(planId) {
        const names = {
            free: 'Free Plan',
            professional: 'Professional Plan',
            business: 'Business Plan'
        };
        return names[planId] || 'Unknown Plan';
    }
}
exports.PaymentService = PaymentService;
exports.default = PaymentService;
//# sourceMappingURL=paymentService.js.map