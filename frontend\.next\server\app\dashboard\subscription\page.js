/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/dashboard/subscription/page";
exports.ids = ["app/dashboard/subscription/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fsubscription%2Fpage&page=%2Fdashboard%2Fsubscription%2Fpage&appPaths=%2Fdashboard%2Fsubscription%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fsubscription%2Fpage.tsx&appDir=C%3A%5CUsers%5Cgupta%5CDesktop%5CinvoNest%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cgupta%5CDesktop%5CinvoNest%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fsubscription%2Fpage&page=%2Fdashboard%2Fsubscription%2Fpage&appPaths=%2Fdashboard%2Fsubscription%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fsubscription%2Fpage.tsx&appDir=C%3A%5CUsers%5Cgupta%5CDesktop%5CinvoNest%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cgupta%5CDesktop%5CinvoNest%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9100\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: [\n        'subscription',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/subscription/page.tsx */ \"(rsc)/./src/app/dashboard/subscription/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/dashboard/subscription/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/dashboard/subscription/page\",\n        pathname: \"/dashboard/subscription\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fsubscription%2Fpage&page=%2Fdashboard%2Fsubscription%2Fpage&appPaths=%2Fdashboard%2Fsubscription%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fsubscription%2Fpage.tsx&appDir=C%3A%5CUsers%5Cgupta%5CDesktop%5CinvoNest%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cgupta%5CDesktop%5CinvoNest%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/react-hot-toast/dist/index.mjs */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AuthContext.tsx */ \"(ssr)/./src/contexts/AuthContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Csubscription%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Csubscription%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/subscription/page.tsx */ \"(ssr)/./src/app/dashboard/subscription/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2d1cHRhJTVDJTVDRGVza3RvcCU1QyU1Q2ludm9OZXN0JTVDJTVDZnJvbnRlbmQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNkYXNoYm9hcmQlNUMlNUNzdWJzY3JpcHRpb24lNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsOExBQStIIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZnJvbnRlbmQvPzlhYTMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxndXB0YVxcXFxEZXNrdG9wXFxcXGludm9OZXN0XFxcXGZyb250ZW5kXFxcXHNyY1xcXFxhcHBcXFxcZGFzaGJvYXJkXFxcXHN1YnNjcmlwdGlvblxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Csubscription%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/dashboard/subscription/page.tsx":
/*!*************************************************!*\
  !*** ./src/app/dashboard/subscription/page.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SubscriptionPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../lib/auth */ \"(ssr)/./src/lib/auth.ts\");\n/* harmony import */ var _components_dashboard_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../components/dashboard/DashboardLayout */ \"(ssr)/./src/components/dashboard/DashboardLayout.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction SubscriptionPage() {\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [plans, setPlans] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentSubscription, setCurrentSubscription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [upgrading, setUpgrading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [billingInterval, setBillingInterval] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"monthly\");\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (user) {\n            fetchPlansAndSubscription();\n        }\n    }, [\n        user\n    ]);\n    const fetchPlansAndSubscription = async ()=>{\n        try {\n            // First sync usage to ensure accurate data\n            try {\n                await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.authenticatedFetch)(\"/api/subscriptions/usage/sync\", {\n                    method: \"POST\"\n                });\n            } catch (syncError) {\n                console.warn(\"Failed to sync usage:\", syncError);\n            // Continue even if sync fails\n            }\n            const [plansResponse, subscriptionResponse] = await Promise.all([\n                (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.authenticatedFetch)(\"/api/subscriptions/plans\"),\n                (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.authenticatedFetch)(\"/api/subscriptions/current\")\n            ]);\n            const plansData = await plansResponse.json();\n            const subscriptionData = await subscriptionResponse.json();\n            if (plansData.success) {\n                setPlans(plansData.data.plans);\n            }\n            if (subscriptionData.success) {\n                setCurrentSubscription(subscriptionData.data.subscription);\n            }\n        } catch (error) {\n            console.error(\"Failed to fetch subscription data:\", error);\n            setError(\"Failed to load subscription information\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleUpgrade = async (planId)=>{\n        if (planId === \"free\") {\n            // Handle downgrade to free\n            try {\n                setUpgrading(planId);\n                const response = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.authenticatedFetch)(\"/api/subscriptions/change\", {\n                    method: \"POST\",\n                    body: JSON.stringify({\n                        planId,\n                        interval: billingInterval\n                    })\n                });\n                const data = await response.json();\n                if (data.success) {\n                    await fetchPlansAndSubscription();\n                    setError(\"\");\n                } else {\n                    setError(data.message);\n                }\n            } catch (error) {\n                console.error(\"Failed to change subscription:\", error);\n                setError(\"Failed to change subscription\");\n            } finally{\n                setUpgrading(null);\n            }\n            return;\n        }\n        // Handle paid plan upgrade\n        try {\n            setUpgrading(planId);\n            // Create payment order\n            const orderResponse = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.authenticatedFetch)(\"/api/subscriptions/payment/create-order\", {\n                method: \"POST\",\n                body: JSON.stringify({\n                    planId,\n                    interval: billingInterval\n                })\n            });\n            const orderData = await orderResponse.json();\n            console.log(\"\\uD83D\\uDD0D Order response:\", orderData);\n            if (!orderData.success) {\n                throw new Error(orderData.message || \"Failed to create payment order\");\n            }\n            // Check if Razorpay key is available\n            if (!orderData.data.key) {\n                throw new Error(\"Payment gateway is not configured. Please contact support.\");\n            }\n            console.log(\"✅ Order data received, initializing Razorpay...\");\n            // Check if we're in development and use mock payment\n            const isDevelopment = \"development\" === \"development\";\n            if (isDevelopment) {\n                console.log(\"\\uD83E\\uDDEA Development mode: Using mock payment flow\");\n                const confirmPayment = confirm(`Mock Payment Flow\\n\\nPlan: ${plans.find((p)=>p.id === planId)?.name}\\nAmount: ₹${orderData.data.amount / 100}\\n\\nClick OK to simulate successful payment, Cancel to simulate failure.`);\n                if (confirmPayment) {\n                    try {\n                        const verifyResponse = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.authenticatedFetch)(\"/api/subscriptions/payment/verify\", {\n                            method: \"POST\",\n                            body: JSON.stringify({\n                                razorpay_order_id: orderData.data.orderId,\n                                razorpay_payment_id: \"mock_payment_\" + Date.now(),\n                                razorpay_signature: \"mock_signature\"\n                            })\n                        });\n                        const verifyData = await verifyResponse.json();\n                        if (verifyData.success) {\n                            await fetchPlansAndSubscription();\n                            setError(\"\");\n                            alert(\"Mock payment successful! Your subscription has been upgraded.\");\n                        } else {\n                            throw new Error(verifyData.message || \"Payment verification failed\");\n                        }\n                    } catch (error) {\n                        console.error(\"Mock payment verification error:\", error);\n                        setError(error instanceof Error ? error.message : \"Payment verification failed\");\n                    }\n                }\n                setUpgrading(null);\n                return;\n            }\n            // Initialize Razorpay payment\n            const options = {\n                key: orderData.data.key,\n                amount: orderData.data.amount,\n                currency: orderData.data.currency,\n                name: \"InvoNest\",\n                description: `Upgrade to ${plans.find((p)=>p.id === planId)?.name} Plan`,\n                order_id: orderData.data.orderId,\n                handler: async (response)=>{\n                    try {\n                        // Verify payment\n                        const verifyResponse = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.authenticatedFetch)(\"/api/subscriptions/payment/verify\", {\n                            method: \"POST\",\n                            body: JSON.stringify({\n                                razorpay_order_id: response.razorpay_order_id,\n                                razorpay_payment_id: response.razorpay_payment_id,\n                                razorpay_signature: response.razorpay_signature\n                            })\n                        });\n                        const verifyData = await verifyResponse.json();\n                        if (verifyData.success) {\n                            await fetchPlansAndSubscription();\n                            setError(\"\");\n                            alert(\"Subscription upgraded successfully!\");\n                        } else {\n                            throw new Error(verifyData.message || \"Payment verification failed\");\n                        }\n                    } catch (error) {\n                        console.error(\"Payment verification failed:\", error);\n                        setError(error instanceof Error ? error.message : \"Payment verification failed\");\n                    } finally{\n                        setUpgrading(null);\n                    }\n                },\n                modal: {\n                    ondismiss: ()=>{\n                        console.log(\"\\uD83D\\uDD04 Razorpay modal dismissed\");\n                        setUpgrading(null);\n                    }\n                },\n                theme: {\n                    color: \"#4F46E5\"\n                }\n            };\n            // Load Razorpay script and open payment modal\n            console.log(\"\\uD83D\\uDD04 Loading Razorpay script...\");\n            // Check if Razorpay script is already loaded\n            if (window.Razorpay) {\n                console.log(\"✅ Razorpay already loaded, opening payment modal...\");\n                try {\n                    const rzp = new window.Razorpay(options);\n                    rzp.open();\n                } catch (error) {\n                    console.error(\"Failed to initialize Razorpay:\", error);\n                    setError(\"Failed to initialize payment gateway\");\n                    setUpgrading(null);\n                }\n                return;\n            }\n            const script = document.createElement(\"script\");\n            script.src = \"https://checkout.razorpay.com/v1/checkout.js\";\n            script.onload = ()=>{\n                console.log(\"✅ Razorpay script loaded successfully\");\n                try {\n                    const rzp = new window.Razorpay(options);\n                    console.log(\"✅ Razorpay instance created, opening modal...\");\n                    // Add a small delay to ensure DOM is ready\n                    setTimeout(()=>{\n                        rzp.open();\n                        console.log(\"\\uD83D\\uDE80 Razorpay modal opened successfully\");\n                        // Check if modal elements are created\n                        setTimeout(()=>{\n                            const modalElements = document.querySelectorAll('[id*=\"razorpay\"], [class*=\"razorpay\"]');\n                            console.log(\"\\uD83D\\uDD0D Razorpay modal elements found:\", modalElements.length);\n                            modalElements.forEach((el, index)=>{\n                                console.log(`Element ${index}:`, el.tagName, el.id, el.className);\n                            });\n                        }, 500);\n                    }, 100);\n                } catch (error) {\n                    console.error(\"Failed to initialize Razorpay:\", error);\n                    setError(\"Failed to initialize payment gateway\");\n                    setUpgrading(null);\n                }\n            };\n            script.onerror = ()=>{\n                console.error(\"❌ Failed to load Razorpay script\");\n                setError(\"Failed to load payment gateway\");\n                setUpgrading(null);\n            };\n            document.head.appendChild(script);\n        } catch (error) {\n            console.error(\"Failed to upgrade subscription:\", error);\n            setError(error instanceof Error ? error.message : \"Failed to upgrade subscription\");\n            setUpgrading(null);\n        }\n    };\n    const formatPrice = (price)=>{\n        return new Intl.NumberFormat(\"en-IN\", {\n            style: \"currency\",\n            currency: \"INR\"\n        }).format(price / 100);\n    };\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString(\"en-IN\", {\n            year: \"numeric\",\n            month: \"long\",\n            day: \"numeric\"\n        });\n    };\n    const getUsageColor = (percentage)=>{\n        if (percentage >= 90) return \"bg-red-500\";\n        if (percentage >= 75) return \"bg-yellow-500\";\n        return \"bg-green-500\";\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center py-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"animate-spin h-8 w-8 text-indigo-600\",\n                            xmlns: \"http://www.w3.org/2000/svg\",\n                            fill: \"none\",\n                            viewBox: \"0 0 24 24\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                    className: \"opacity-25\",\n                                    cx: \"12\",\n                                    cy: \"12\",\n                                    r: \"10\",\n                                    stroke: \"currentColor\",\n                                    strokeWidth: \"4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                    lineNumber: 311,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    className: \"opacity-75\",\n                                    fill: \"currentColor\",\n                                    d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                    lineNumber: 312,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                            lineNumber: 310,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-lg text-gray-600\",\n                            children: \"Loading subscription...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                            lineNumber: 314,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                    lineNumber: 309,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                lineNumber: 308,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n            lineNumber: 307,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__.DashboardHeader, {\n                title: \"Subscription & Billing\",\n                subtitle: \"Manage your InvoNest subscription and view usage\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                lineNumber: 323,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto p-4 lg:p-8 space-y-8\",\n                children: [\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                        lineNumber: 331,\n                        columnNumber: 11\n                    }, this),\n                    currentSubscription && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-xl shadow-sm border border-gray-200 p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                children: \"Current Subscription\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                lineNumber: 339,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-gray-900\",\n                                                children: currentSubscription.planName\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                lineNumber: 342,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600\",\n                                                children: [\n                                                    formatPrice(currentSubscription.amount),\n                                                    \" / \",\n                                                    currentSubscription.interval\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                lineNumber: 343,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-500 mt-1\",\n                                                children: [\n                                                    currentSubscription.status === \"active\" ? \"Active\" : currentSubscription.status,\n                                                    currentSubscription.currentPeriodEnd && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            \" until \",\n                                                            formatDate(currentSubscription.currentPeriodEnd)\n                                                        ]\n                                                    }, void 0, true)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                lineNumber: 346,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                        lineNumber: 341,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"md:col-span-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-sm font-medium text-gray-900 mb-3\",\n                                                children: \"Usage This Period\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                lineNumber: 356,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between text-sm text-gray-900\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Invoices\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                        lineNumber: 360,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            currentSubscription.usage.invoicesUsed,\n                                                                            \" / \",\n                                                                            currentSubscription.features.maxInvoices === -1 ? \"∞\" : currentSubscription.features.maxInvoices\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                        lineNumber: 361,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 359,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            currentSubscription.features.maxInvoices !== -1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-full bg-gray-200 rounded-full h-2 mt-1\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: `h-2 rounded-full ${getUsageColor(currentSubscription.usagePercentages.invoices).split(\" \")[1]}`,\n                                                                    style: {\n                                                                        width: `${Math.min(currentSubscription.usagePercentages.invoices, 100)}%`\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                    lineNumber: 367,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 366,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                        lineNumber: 358,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between text-sm text-gray-900\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Storage\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                        lineNumber: 377,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            currentSubscription.usage.storageUsed,\n                                                                            \"MB / \",\n                                                                            currentSubscription.features.maxStorage,\n                                                                            \"MB\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                        lineNumber: 378,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 376,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-full bg-gray-200 rounded-full h-2 mt-1\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: `h-2 rounded-full ${getUsageColor(currentSubscription.usagePercentages.storage).split(\" \")[1]}`,\n                                                                    style: {\n                                                                        width: `${Math.min(currentSubscription.usagePercentages.storage, 100)}%`\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                    lineNumber: 383,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 382,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                        lineNumber: 375,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                lineNumber: 357,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                        lineNumber: 355,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                lineNumber: 340,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                        lineNumber: 338,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-100 rounded-lg p-1 flex\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setBillingInterval(\"monthly\"),\n                                    className: `px-4 py-2 text-sm font-medium rounded-md transition-colors ${billingInterval === \"monthly\" ? \"bg-white text-indigo-600 shadow-sm\" : \"text-gray-800 hover:text-gray-900\"}`,\n                                    children: \"Monthly\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                    lineNumber: 398,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setBillingInterval(\"yearly\"),\n                                    className: `px-4 py-2 text-sm font-medium rounded-md transition-colors ${billingInterval === \"yearly\" ? \"bg-white text-indigo-600 shadow-sm\" : \"text-gray-800 hover:text-gray-900\"}`,\n                                    children: [\n                                        \"Yearly\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-1 text-xs bg-green-100 text-green-800 px-1.5 py-0.5 rounded\",\n                                            children: \"Save 17%\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                            lineNumber: 417,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                    lineNumber: 408,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                            lineNumber: 397,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                        lineNumber: 396,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                        children: plans.map((plan)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `relative bg-white rounded-xl shadow-sm border-2 transition-all ${plan.popular ? \"border-indigo-500 ring-2 ring-indigo-200\" : \"border-gray-200 hover:border-indigo-300\"} ${currentSubscription?.planId === plan.id ? \"ring-2 ring-green-200 border-green-500\" : \"\"}`,\n                                children: [\n                                    plan.popular && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -top-3 left-1/2 transform -translate-x-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"bg-indigo-600 text-white px-3 py-1 text-xs font-medium rounded-full\",\n                                            children: \"Most Popular\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                            lineNumber: 437,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                        lineNumber: 436,\n                                        columnNumber: 17\n                                    }, this),\n                                    currentSubscription?.planId === plan.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -top-3 right-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"bg-green-600 text-white px-3 py-1 text-xs font-medium rounded-full\",\n                                            children: \"Current Plan\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                            lineNumber: 445,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                        lineNumber: 444,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-semibold text-gray-900\",\n                                                children: plan.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                lineNumber: 452,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 mt-2\",\n                                                children: plan.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                lineNumber: 453,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-3xl font-bold text-gray-900\",\n                                                        children: formatPrice(plan.pricing[billingInterval])\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                        lineNumber: 456,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    plan.pricing[billingInterval] > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600 ml-1\",\n                                                        children: [\n                                                            \"/\",\n                                                            billingInterval === \"monthly\" ? \"month\" : \"year\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                        lineNumber: 460,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                lineNumber: 455,\n                                                columnNumber: 17\n                                            }, this),\n                                            billingInterval === \"yearly\" && plan.pricing.yearly > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-green-600 mt-1\",\n                                                children: [\n                                                    \"Save ₹\",\n                                                    ((plan.pricing.monthly * 12 - plan.pricing.yearly) / 100).toLocaleString(\"en-IN\"),\n                                                    \" per year\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                lineNumber: 467,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"mt-6 space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-4 h-4 text-green-500 mr-3\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M5 13l4 4L19 7\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                    lineNumber: 475,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 474,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: [\n                                                                    plan.features.maxInvoices === -1 ? \"Unlimited\" : plan.features.maxInvoices,\n                                                                    \" invoices/month\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 477,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                        lineNumber: 473,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-4 h-4 text-green-500 mr-3\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M5 13l4 4L19 7\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                    lineNumber: 483,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 482,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: [\n                                                                    plan.features.maxStorage,\n                                                                    \"MB storage\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 485,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                        lineNumber: 481,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    plan.features.documentAnalysis && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-4 h-4 text-green-500 mr-3\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M5 13l4 4L19 7\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                    lineNumber: 492,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 491,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: \"Document analysis\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 494,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                        lineNumber: 490,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    plan.features.prioritySupport && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-4 h-4 text-green-500 mr-3\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M5 13l4 4L19 7\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                    lineNumber: 500,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 499,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: \"Priority support\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 502,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                        lineNumber: 498,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    plan.features.apiAccess && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-4 h-4 text-green-500 mr-3\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M5 13l4 4L19 7\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                    lineNumber: 508,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 507,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: \"API access\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 510,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                        lineNumber: 506,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    plan.features.multiUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-4 h-4 text-green-500 mr-3\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M5 13l4 4L19 7\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                    lineNumber: 516,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 515,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: [\n                                                                    \"Up to \",\n                                                                    plan.features.maxUsers,\n                                                                    \" users\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 518,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                        lineNumber: 514,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                lineNumber: 472,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleUpgrade(plan.id),\n                                                disabled: upgrading === plan.id || currentSubscription?.planId === plan.id,\n                                                className: `w-full mt-6 px-4 py-2 rounded-lg text-sm font-medium transition-colors ${currentSubscription?.planId === plan.id ? \"bg-green-100 text-green-800 cursor-not-allowed\" : plan.popular ? \"bg-indigo-600 text-white hover:bg-indigo-700 disabled:opacity-50\" : \"bg-gray-100 text-gray-900 hover:bg-gray-200 disabled:opacity-50\"}`,\n                                                children: upgrading === plan.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"animate-spin -ml-1 mr-3 h-4 w-4\",\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            fill: \"none\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                    className: \"opacity-25\",\n                                                                    cx: \"12\",\n                                                                    cy: \"12\",\n                                                                    r: \"10\",\n                                                                    stroke: \"currentColor\",\n                                                                    strokeWidth: \"4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                    lineNumber: 539,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    className: \"opacity-75\",\n                                                                    fill: \"currentColor\",\n                                                                    d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                    lineNumber: 540,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                            lineNumber: 538,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"Processing...\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                    lineNumber: 537,\n                                                    columnNumber: 21\n                                                }, this) : currentSubscription?.planId === plan.id ? \"Current Plan\" : plan.id === \"free\" ? \"Downgrade to Free\" : `Upgrade to ${plan.name}`\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                lineNumber: 525,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                        lineNumber: 451,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, plan.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                lineNumber: 427,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                        lineNumber: 425,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-xl shadow-sm border border-gray-200 p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold text-gray-900 mb-6\",\n                                children: \"Feature Comparison\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                lineNumber: 559,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"overflow-x-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                    className: \"min-w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                className: \"border-b border-gray-200\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-left py-3 px-4 font-medium text-gray-900\",\n                                                        children: \"Feature\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                        lineNumber: 564,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    plans.map((plan)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-center py-3 px-4 font-medium text-gray-900\",\n                                                            children: plan.name\n                                                        }, plan.id, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                            lineNumber: 566,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                lineNumber: 563,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                            lineNumber: 562,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                            className: \"divide-y divide-gray-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4 text-sm text-gray-900\",\n                                                            children: \"Monthly Invoices\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                            lineNumber: 574,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        plans.map((plan)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"text-center py-3 px-4 text-sm text-gray-900\",\n                                                                children: plan.features.maxInvoices === -1 ? \"∞\" : plan.features.maxInvoices\n                                                            }, plan.id, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 576,\n                                                                columnNumber: 21\n                                                            }, this))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                    lineNumber: 573,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4 text-sm text-gray-900\",\n                                                            children: \"Storage\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                            lineNumber: 582,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        plans.map((plan)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"text-center py-3 px-4 text-sm text-gray-900\",\n                                                                children: [\n                                                                    plan.features.maxStorage,\n                                                                    \"MB\"\n                                                                ]\n                                                            }, plan.id, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 584,\n                                                                columnNumber: 21\n                                                            }, this))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                    lineNumber: 581,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4 text-sm text-gray-900\",\n                                                            children: \"Document Analysis\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                            lineNumber: 590,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        plans.map((plan)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"text-center py-3 px-4\",\n                                                                children: plan.features.documentAnalysis ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-5 h-5 text-green-500 mx-auto\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M5 13l4 4L19 7\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                        lineNumber: 595,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                    lineNumber: 594,\n                                                                    columnNumber: 25\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-5 h-5 text-gray-300 mx-auto\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M6 18L18 6M6 6l12 12\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                        lineNumber: 599,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                    lineNumber: 598,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, plan.id, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 592,\n                                                                columnNumber: 21\n                                                            }, this))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                    lineNumber: 589,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4 text-sm text-gray-900\",\n                                                            children: \"Priority Support\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                            lineNumber: 606,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        plans.map((plan)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"text-center py-3 px-4\",\n                                                                children: plan.features.prioritySupport ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-5 h-5 text-green-500 mx-auto\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M5 13l4 4L19 7\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                        lineNumber: 611,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                    lineNumber: 610,\n                                                                    columnNumber: 25\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-5 h-5 text-gray-300 mx-auto\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M6 18L18 6M6 6l12 12\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                        lineNumber: 615,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                    lineNumber: 614,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, plan.id, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 608,\n                                                                columnNumber: 21\n                                                            }, this))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                    lineNumber: 605,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4 text-sm text-gray-900\",\n                                                            children: \"API Access\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                            lineNumber: 622,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        plans.map((plan)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"text-center py-3 px-4\",\n                                                                children: plan.features.apiAccess ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-5 h-5 text-green-500 mx-auto\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M5 13l4 4L19 7\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                        lineNumber: 627,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                    lineNumber: 626,\n                                                                    columnNumber: 25\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-5 h-5 text-gray-300 mx-auto\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M6 18L18 6M6 6l12 12\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                        lineNumber: 631,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                    lineNumber: 630,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, plan.id, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 624,\n                                                                columnNumber: 21\n                                                            }, this))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                    lineNumber: 621,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                            lineNumber: 572,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                    lineNumber: 561,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                lineNumber: 560,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                        lineNumber: 558,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                lineNumber: 328,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n        lineNumber: 322,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/dashboard/subscription/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/auth/ProtectedRoute.tsx":
/*!************************************************!*\
  !*** ./src/components/auth/ProtectedRoute.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthLoadingSpinner: () => (/* binding */ AuthLoadingSpinner),\n/* harmony export */   LoadingSpinner: () => (/* binding */ LoadingSpinner),\n/* harmony export */   \"default\": () => (/* binding */ ProtectedRoute)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default,AuthLoadingSpinner,LoadingSpinner auto */ \n\n\n\nfunction ProtectedRoute({ children, fallback, redirectTo = \"/login\" }) {\n    const { isLoggedIn, loading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!loading && !isLoggedIn) {\n            router.push(redirectTo);\n        }\n    }, [\n        isLoggedIn,\n        loading,\n        router,\n        redirectTo\n    ]);\n    // Show loading spinner while checking authentication\n    if (loading) {\n        return fallback || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                lineNumber: 32,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n            lineNumber: 31,\n            columnNumber: 9\n        }, this);\n    }\n    // Don't render anything if not authenticated (redirect will happen)\n    if (!isLoggedIn) {\n        return null;\n    }\n    // Render children if authenticated\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n// Loading component for authentication checks\nconst AuthLoadingSpinner = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-16 w-16 border-b-2 border-indigo-600 mx-auto\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                    lineNumber: 54,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"mt-4 text-lg text-gray-600\",\n                    children: \"Authenticating...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                    lineNumber: 55,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"mt-2 text-sm text-gray-500\",\n                    children: \"Please wait while we verify your credentials\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n            lineNumber: 53,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n        lineNumber: 52,\n        columnNumber: 3\n    }, undefined);\n// Simple loading component\nconst LoadingSpinner = ({ size = \"md\", text = \"Loading...\" })=>{\n    const sizeClasses = {\n        sm: \"h-6 w-6\",\n        md: \"h-8 w-8\",\n        lg: \"h-12 w-12\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center justify-center p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `animate-spin rounded-full border-b-2 border-indigo-600 mx-auto ${sizeClasses[size]}`\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                    lineNumber: 75,\n                    columnNumber: 9\n                }, undefined),\n                text && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"mt-2 text-sm text-gray-600\",\n                    children: text\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                    lineNumber: 76,\n                    columnNumber: 18\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n            lineNumber: 74,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n        lineNumber: 73,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/auth/ProtectedRoute.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/dashboard/DashboardLayout.tsx":
/*!******************************************************!*\
  !*** ./src/components/dashboard/DashboardLayout.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DashboardHeader: () => (/* binding */ DashboardHeader),\n/* harmony export */   StatsCard: () => (/* binding */ StatsCard),\n/* harmony export */   \"default\": () => (/* binding */ DashboardLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../auth/ProtectedRoute */ \"(ssr)/./src/components/auth/ProtectedRoute.tsx\");\n/* harmony import */ var _mobile_MobileDashboardLayout__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../mobile/MobileDashboardLayout */ \"(ssr)/./src/components/mobile/MobileDashboardLayout.tsx\");\n/* __next_internal_client_entry_do_not_use__ default,DashboardHeader,StatsCard auto */ \n\n\n\n\n\n\n\nfunction DashboardLayout({ children, title, showBack = false, backHref = \"/dashboard\", actions, enablePullToRefresh = false, onRefresh }) {\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.usePathname)();\n    const { user, logout } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    // Check if device is mobile\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const checkMobile = ()=>{\n            setIsMobile(window.innerWidth < 1024);\n        };\n        checkMobile();\n        window.addEventListener(\"resize\", checkMobile);\n        return ()=>window.removeEventListener(\"resize\", checkMobile);\n    }, []);\n    // Remove unwanted floating elements\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const removeFloatingElements = ()=>{\n            // Target high z-index elements that might be browser extensions\n            const highZIndexElements = document.querySelectorAll('div[style*=\"z-index: 2147483647\"], div[style*=\"z-index: 999999\"]');\n            highZIndexElements.forEach((element)=>{\n                const isOurComponent = element.closest('[class*=\"dashboard\"]') || element.closest('[class*=\"chat\"]') || element.closest('[class*=\"mobile\"]') || element.id?.includes(\"dashboard\") || element.className?.includes(\"dashboard\");\n                if (!isOurComponent) {\n                    element.style.display = \"none\";\n                }\n            });\n            // Remove Next.js development toasts and overlays\n            const nextjsElements = document.querySelectorAll(\"[data-nextjs-toast], [data-nextjs-toast-wrapper]\");\n            nextjsElements.forEach((element)=>{\n                element.style.display = \"none\";\n            });\n            // Remove any floating circular avatars that aren't part of our app\n            const floatingElements = document.querySelectorAll('div[style*=\"position: fixed\"]');\n            floatingElements.forEach((element)=>{\n                const style = element.getAttribute(\"style\") || \"\";\n                const hasCircularStyle = style.includes(\"border-radius: 50%\") || style.includes(\"border-radius:50%\") || element.querySelector('[style*=\"border-radius: 50%\"]');\n                // Check if it's not one of our components\n                const isOurComponent = element.closest('[class*=\"dashboard\"]') || element.closest('[class*=\"chat\"]') || element.closest('[class*=\"mobile\"]') || element.id?.includes(\"invoNest\") || element.className?.includes(\"invoNest\");\n                if (hasCircularStyle && !isOurComponent) {\n                    element.style.display = \"none\";\n                }\n            });\n        };\n        // Run immediately and then periodically\n        removeFloatingElements();\n        const interval = setInterval(removeFloatingElements, 2000);\n        return ()=>clearInterval(interval);\n    }, []);\n    const navigation = [\n        {\n            name: \"Dashboard\",\n            href: \"/dashboard\",\n            icon: \"\\uD83C\\uDFE0\"\n        },\n        {\n            name: \"Invoices\",\n            href: \"/dashboard/invoices\",\n            icon: \"\\uD83E\\uDDFE\"\n        },\n        {\n            name: \"Create Invoice\",\n            href: \"/dashboard/invoices/create\",\n            icon: \"➕\"\n        },\n        {\n            name: \"Documents\",\n            href: \"/dashboard/documents\",\n            icon: \"\\uD83D\\uDCC1\"\n        },\n        {\n            name: \"Compliance\",\n            href: \"/dashboard/compliance\",\n            icon: \"\\uD83D\\uDCC5\"\n        },\n        {\n            name: \"Notifications\",\n            href: \"/dashboard/notifications\",\n            icon: \"\\uD83D\\uDD14\"\n        },\n        {\n            name: \"Subscription\",\n            href: \"/dashboard/subscription\",\n            icon: \"\\uD83D\\uDCB3\"\n        },\n        {\n            name: \"Profile\",\n            href: \"/dashboard/profile\",\n            icon: \"\\uD83D\\uDC64\"\n        },\n        {\n            name: \"Settings\",\n            href: \"/dashboard/settings\",\n            icon: \"⚙️\"\n        }\n    ];\n    const handleLogout = async ()=>{\n        await logout();\n    };\n    // Use mobile layout on mobile devices\n    if (isMobile) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mobile_MobileDashboardLayout__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            title: title || \"Dashboard\",\n            showBack: showBack,\n            backHref: backHref,\n            actions: actions,\n            enablePullToRefresh: enablePullToRefresh,\n            onRefresh: onRefresh,\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n            lineNumber: 117,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50\",\n            children: [\n                sidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"fixed inset-0 bg-gray-900/50 backdrop-blur-sm z-40 lg:hidden\",\n                    onClick: ()=>setSidebarOpen(false)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                    lineNumber: 135,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"lg:hidden fixed top-0 left-0 right-0 z-30 bg-white shadow-sm border-b border-gray-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between px-4 py-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setSidebarOpen(true),\n                                className: \"text-gray-700 hover:text-gray-900 p-2 rounded-lg hover:bg-gray-100 transition-colors\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-6 h-6\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M4 6h16M4 12h16M4 18h16\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-10 h-10 relative\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        src: \"/invologo.png\",\n                                        alt: \"InvoNest Logo\",\n                                        width: 40,\n                                        height: 40,\n                                        className: \"object-contain w-full h-full\",\n                                        priority: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-sm font-semibold text-gray-900 truncate max-w-24\",\n                                children: title || \"Dashboard\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                    lineNumber: 142,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-xl border-r border-gray-200 transform ${sidebarOpen ? \"translate-x-0\" : \"-translate-x-full\"} transition-transform duration-300 ease-in-out lg:translate-x-0 flex flex-col`,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between h-20 px-6 border-b border-gray-200 bg-white\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: \"/\",\n                                    className: \"group flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 relative\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                src: \"/invologo.png\",\n                                                alt: \"InvoNest Logo\",\n                                                width: 40,\n                                                height: 40,\n                                                className: \"object-contain w-full h-full\",\n                                                priority: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                                                lineNumber: 176,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                                            lineNumber: 175,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xl font-bold text-gray-900 group-hover:text-indigo-600 transition-colors\",\n                                            children: \"InvoNest\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setSidebarOpen(false),\n                                    className: \"lg:hidden text-gray-600 hover:text-gray-900 hover:bg-gray-100 p-2 rounded-lg transition-colors\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-6 h-6\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M6 18L18 6M6 6l12 12\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                                            lineNumber: 194,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                            lineNumber: 173,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"flex-1 overflow-y-auto py-6 px-4 chat-sidebar-scroll\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"space-y-2\",\n                                children: navigation.map((item)=>{\n                                    const isActive = pathname === item.href;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: item.href,\n                                            onClick: ()=>setSidebarOpen(false),\n                                            className: `flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 ${isActive ? \"bg-indigo-100 text-indigo-700 shadow-sm border border-indigo-200\" : \"text-gray-800 hover:bg-gray-50 hover:text-gray-900 hover:shadow-sm\"}`,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"mr-3 text-lg\",\n                                                    children: item.icon\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 215,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium\",\n                                                    children: item.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 216,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, item.name, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 19\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                            lineNumber: 200,\n                            columnNumber: 11\n                        }, this),\n                        user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-t border-gray-200 p-4 bg-gray-50\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-lg shadow-lg\",\n                                            children: user.name.charAt(0).toUpperCase()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 min-w-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-semibold text-gray-900 truncate\",\n                                                children: user.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                                                lineNumber: 234,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-700 truncate\",\n                                                children: user.email\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                                                lineNumber: 235,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleLogout,\n                                        className: \"p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-all duration-200\",\n                                        title: \"Logout\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-5 h-5\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                                                lineNumber: 243,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                                        lineNumber: 237,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                                lineNumber: 227,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                    lineNumber: 171,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"lg:pl-64 pt-16 lg:pt-0 min-h-screen\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"h-full overflow-y-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 lg:p-6\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                            lineNumber: 254,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                        lineNumber: 253,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                    lineNumber: 252,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n            lineNumber: 132,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n        lineNumber: 131,\n        columnNumber: 5\n    }, this);\n}\n// Header component for dashboard pages\nfunction DashboardHeader({ title, subtitle, actions }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white border-b border-gray-200 px-4 py-6 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-gray-900\",\n                            children: title\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                            lineNumber: 278,\n                            columnNumber: 11\n                        }, this),\n                        subtitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-1 text-sm text-gray-700\",\n                            children: subtitle\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                            lineNumber: 280,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                    lineNumber: 277,\n                    columnNumber: 9\n                }, this),\n                actions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: actions\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                    lineNumber: 284,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n            lineNumber: 276,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n        lineNumber: 275,\n        columnNumber: 5\n    }, this);\n}\n// Stats card component\nfunction StatsCard({ title, value, icon, color = \"indigo\", subtitle, trend }) {\n    const colorClasses = {\n        indigo: \"from-indigo-500 to-indigo-600\",\n        green: \"from-green-500 to-green-600\",\n        yellow: \"from-yellow-500 to-yellow-600\",\n        red: \"from-red-500 to-red-600\",\n        blue: \"from-blue-500 to-blue-600\",\n        purple: \"from-purple-500 to-purple-600\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-xl shadow-lg border border-gray-100 p-6 hover:shadow-xl transition-all duration-300 hover:-translate-y-1\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm font-medium text-gray-700 mb-1\",\n                            children: title\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                            lineNumber: 322,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-3xl font-bold text-gray-900\",\n                            children: value\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                            lineNumber: 323,\n                            columnNumber: 11\n                        }, this),\n                        subtitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-700 mt-1\",\n                            children: subtitle\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                            lineNumber: 325,\n                            columnNumber: 13\n                        }, this),\n                        trend && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center mt-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: `text-xs font-medium ${trend.positive ? \"text-green-600\" : \"text-red-600\"}`,\n                                    children: [\n                                        trend.positive ? \"↗\" : \"↘\",\n                                        \" \",\n                                        trend.value,\n                                        \"%\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                                    lineNumber: 329,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-gray-500 ml-1\",\n                                    children: trend.label\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                                    lineNumber: 332,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                            lineNumber: 328,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                    lineNumber: 321,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `w-14 h-14 bg-gradient-to-br ${colorClasses[color]} rounded-xl flex items-center justify-center shadow-lg`,\n                    children: icon\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n                    lineNumber: 336,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n            lineNumber: 320,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\dashboard\\\\DashboardLayout.tsx\",\n        lineNumber: 319,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/dashboard/DashboardLayout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/mobile/MobileBottomNav.tsx":
/*!***************************************************!*\
  !*** ./src/components/mobile/MobileBottomNav.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MobileBottomNav)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst navigationItems = [\n    {\n        name: \"Dashboard\",\n        href: \"/dashboard\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-6 h-6\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileBottomNav.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileBottomNav.tsx\",\n                    lineNumber: 21,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileBottomNav.tsx\",\n            lineNumber: 19,\n            columnNumber: 7\n        }, undefined),\n        activeIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-6 h-6\",\n            fill: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileBottomNav.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileBottomNav.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileBottomNav.tsx\",\n            lineNumber: 25,\n            columnNumber: 7\n        }, undefined)\n    },\n    {\n        name: \"Invoices\",\n        href: \"/dashboard/invoices\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-6 h-6\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileBottomNav.tsx\",\n                lineNumber: 36,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileBottomNav.tsx\",\n            lineNumber: 35,\n            columnNumber: 7\n        }, undefined),\n        activeIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-6 h-6\",\n            fill: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M7 3a2 2 0 00-2 2v14a2 2 0 002 2h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7zm2 9a1 1 0 100 2h6a1 1 0 100-2H9zm0 4a1 1 0 100 2h6a1 1 0 100-2H9z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileBottomNav.tsx\",\n                lineNumber: 41,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileBottomNav.tsx\",\n            lineNumber: 40,\n            columnNumber: 7\n        }, undefined)\n    },\n    {\n        name: \"Create\",\n        href: \"/dashboard/invoices/create\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-8 h-8\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M12 6v6m0 0v6m0-6h6m-6 0H6\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileBottomNav.tsx\",\n                lineNumber: 50,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileBottomNav.tsx\",\n            lineNumber: 49,\n            columnNumber: 7\n        }, undefined),\n        activeIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-8 h-8\",\n            fill: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M12 2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2zm5 11h-4v4a1 1 0 11-2 0v-4H7a1 1 0 110-2h4V7a1 1 0 112 0v4h4a1 1 0 110 2z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileBottomNav.tsx\",\n                lineNumber: 55,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileBottomNav.tsx\",\n            lineNumber: 54,\n            columnNumber: 7\n        }, undefined)\n    },\n    {\n        name: \"Notifications\",\n        href: \"/dashboard/notifications\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-6 h-6\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M15 17h5l-5 5v-5zM11 19H6a2 2 0 01-2-2V7a2 2 0 012-2h5m5 0v5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileBottomNav.tsx\",\n                lineNumber: 64,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileBottomNav.tsx\",\n            lineNumber: 63,\n            columnNumber: 7\n        }, undefined),\n        activeIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-6 h-6\",\n            fill: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M15 17h5l-5 5v-5zM11 19H6a2 2 0 01-2-2V7a2 2 0 012-2h5m5 0v5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileBottomNav.tsx\",\n                lineNumber: 69,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileBottomNav.tsx\",\n            lineNumber: 68,\n            columnNumber: 7\n        }, undefined)\n    },\n    {\n        name: \"Subscription\",\n        href: \"/dashboard/subscription\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-6 h-6\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileBottomNav.tsx\",\n                lineNumber: 78,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileBottomNav.tsx\",\n            lineNumber: 77,\n            columnNumber: 7\n        }, undefined),\n        activeIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-6 h-6\",\n            fill: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileBottomNav.tsx\",\n                lineNumber: 83,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileBottomNav.tsx\",\n            lineNumber: 82,\n            columnNumber: 7\n        }, undefined)\n    }\n];\nfunction MobileBottomNav() {\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"lg:hidden fixed bottom-0 left-0 right-0 z-40 bg-white border-t border-gray-200 safe-area-pb\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-around px-2 py-2\",\n            children: navigationItems.map((item)=>{\n                const isActive = pathname === item.href || item.href !== \"/dashboard\" && pathname.startsWith(item.href);\n                const isCreateButton = item.name === \"Create\";\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    href: item.href,\n                    className: `flex flex-col items-center justify-center min-w-0 flex-1 py-2 px-1 transition-all duration-200 ${isCreateButton ? \"relative -top-4\" : \"\"}`,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `flex items-center justify-center transition-all duration-200 ${isCreateButton ? `w-14 h-14 rounded-full shadow-lg ${isActive ? \"bg-gradient-to-r from-indigo-600 to-purple-600 text-white\" : \"bg-gradient-to-r from-indigo-500 to-purple-500 text-white hover:from-indigo-600 hover:to-purple-600\"}` : `w-10 h-10 rounded-lg ${isActive ? \"bg-indigo-100 text-indigo-600\" : \"text-gray-500 hover:text-indigo-600 hover:bg-indigo-50\"}`}`,\n                            children: isActive ? item.activeIcon : item.icon\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileBottomNav.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: `text-xs font-medium mt-1 transition-colors duration-200 ${isCreateButton ? \"text-indigo-600\" : isActive ? \"text-indigo-600\" : \"text-gray-500\"} ${isCreateButton ? \"hidden\" : \"block\"}`,\n                            children: item.name\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileBottomNav.tsx\",\n                            lineNumber: 124,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, item.name, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileBottomNav.tsx\",\n                    lineNumber: 100,\n                    columnNumber: 13\n                }, this);\n            })\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileBottomNav.tsx\",\n            lineNumber: 94,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileBottomNav.tsx\",\n        lineNumber: 93,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/mobile/MobileBottomNav.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/mobile/MobileDashboardLayout.tsx":
/*!*********************************************************!*\
  !*** ./src/components/mobile/MobileDashboardLayout.tsx ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MobileButton: () => (/* binding */ MobileButton),\n/* harmony export */   MobileCard: () => (/* binding */ MobileCard),\n/* harmony export */   MobileInput: () => (/* binding */ MobileInput),\n/* harmony export */   \"default\": () => (/* binding */ MobileDashboardLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../auth/ProtectedRoute */ \"(ssr)/./src/components/auth/ProtectedRoute.tsx\");\n/* harmony import */ var _MobileHeader__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./MobileHeader */ \"(ssr)/./src/components/mobile/MobileHeader.tsx\");\n/* harmony import */ var _MobileBottomNav__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./MobileBottomNav */ \"(ssr)/./src/components/mobile/MobileBottomNav.tsx\");\n/* __next_internal_client_entry_do_not_use__ default,MobileCard,MobileButton,MobileInput auto */ \n\n\n\n\n\nfunction MobileDashboardLayout({ children, title, showBack = false, backHref = \"/dashboard\", actions, enablePullToRefresh = false, onRefresh }) {\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [isRefreshing, setIsRefreshing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [pullDistance, setPullDistance] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [startY, setStartY] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const handleTouchStart = (e)=>{\n        if (!enablePullToRefresh) return;\n        setStartY(e.touches[0].clientY);\n    };\n    const handleTouchMove = (e)=>{\n        if (!enablePullToRefresh || isRefreshing) return;\n        const currentY = e.touches[0].clientY;\n        const distance = currentY - startY;\n        if (distance > 0 && window.scrollY === 0) {\n            setPullDistance(Math.min(distance * 0.5, 80));\n        }\n    };\n    const handleTouchEnd = async ()=>{\n        if (!enablePullToRefresh || isRefreshing) return;\n        if (pullDistance > 60 && onRefresh) {\n            setIsRefreshing(true);\n            try {\n                await onRefresh();\n            } catch (error) {\n                console.error(\"Refresh failed:\", error);\n            } finally{\n                setIsRefreshing(false);\n            }\n        }\n        setPullDistance(0);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_auth_ProtectedRoute__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-gray-50 lg:hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MobileHeader__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        title: title,\n                        showBack: showBack,\n                        backHref: backHref,\n                        actions: actions\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileDashboardLayout.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: `pt-14 pb-20 min-h-screen mobile-scroll ${enablePullToRefresh ? \"pull-to-refresh\" : \"\"} ${isRefreshing ? \"refreshing\" : \"\"}`,\n                        style: {\n                            transform: `translateY(${pullDistance}px)`,\n                            transition: pullDistance === 0 ? \"transform 0.3s ease\" : \"none\"\n                        },\n                        onTouchStart: handleTouchStart,\n                        onTouchMove: handleTouchMove,\n                        onTouchEnd: handleTouchEnd,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"safe-area-inset\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileDashboardLayout.tsx\",\n                            lineNumber: 89,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileDashboardLayout.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MobileBottomNav__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileDashboardLayout.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileDashboardLayout.tsx\",\n                lineNumber: 69,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden lg:block\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-h-screen bg-gray-50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold text-gray-900\",\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileDashboardLayout.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileDashboardLayout.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 13\n                            }, this),\n                            children\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileDashboardLayout.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileDashboardLayout.tsx\",\n                    lineNumber: 101,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileDashboardLayout.tsx\",\n                lineNumber: 100,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileDashboardLayout.tsx\",\n        lineNumber: 68,\n        columnNumber: 5\n    }, this);\n}\n// Mobile Card Component\nfunction MobileCard({ children, className = \"\", onClick, swipeActions }) {\n    const [swipeX, setSwipeX] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [startX, setStartX] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleTouchStart = (e)=>{\n        if (!swipeActions) return;\n        setStartX(e.touches[0].clientX);\n        setIsDragging(true);\n    };\n    const handleTouchMove = (e)=>{\n        if (!swipeActions || !isDragging) return;\n        const currentX = e.touches[0].clientX;\n        const distance = currentX - startX;\n        setSwipeX(Math.max(-120, Math.min(120, distance)));\n    };\n    const handleTouchEnd = ()=>{\n        if (!swipeActions) return;\n        if (swipeX > 60 && swipeActions.left) {\n            swipeActions.left.action();\n        } else if (swipeX < -60 && swipeActions.right) {\n            swipeActions.right.action();\n        }\n        setSwipeX(0);\n        setIsDragging(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `mobile-card ${swipeActions ? \"swipe-actions\" : \"\"} ${className}`,\n        children: [\n            swipeActions?.left && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"swipe-actions-left\",\n                style: {\n                    background: swipeActions.left.color || \"#10b981\"\n                },\n                children: swipeActions.left.icon\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileDashboardLayout.tsx\",\n                lineNumber: 163,\n                columnNumber: 9\n            }, this),\n            swipeActions?.right && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"swipe-actions-right\",\n                style: {\n                    background: swipeActions.right.color || \"#ef4444\"\n                },\n                children: swipeActions.right.icon\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileDashboardLayout.tsx\",\n                lineNumber: 169,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"swipe-content\",\n                style: {\n                    transform: `translateX(${swipeX}px)`\n                },\n                onTouchStart: handleTouchStart,\n                onTouchMove: handleTouchMove,\n                onTouchEnd: handleTouchEnd,\n                onClick: onClick,\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileDashboardLayout.tsx\",\n                lineNumber: 174,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileDashboardLayout.tsx\",\n        lineNumber: 161,\n        columnNumber: 5\n    }, this);\n}\n// Mobile Button Component\nfunction MobileButton({ children, variant = \"primary\", size = \"medium\", className = \"\", onClick, disabled = false, loading = false, ...props }) {\n    const baseClasses = \"mobile-btn flex items-center justify-center font-semibold transition-all duration-200\";\n    const variantClasses = {\n        primary: \"mobile-btn-primary\",\n        secondary: \"mobile-btn-secondary\",\n        outline: \"bg-transparent border-2 border-gray-300 text-gray-700 hover:border-gray-400\"\n    };\n    const sizeClasses = {\n        small: \"px-4 py-2 text-sm min-h-[36px]\",\n        medium: \"px-6 py-3 text-base min-h-[44px]\",\n        large: \"px-8 py-4 text-lg min-h-[52px]\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: `${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${className} ${disabled ? \"opacity-50 cursor-not-allowed\" : \"\"}`,\n        onClick: onClick,\n        disabled: disabled || loading,\n        ...props,\n        children: [\n            loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-5 h-5 border-2 border-current border-t-transparent rounded-full animate-spin mr-2\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileDashboardLayout.tsx\",\n                lineNumber: 232,\n                columnNumber: 9\n            }, this) : null,\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileDashboardLayout.tsx\",\n        lineNumber: 223,\n        columnNumber: 5\n    }, this);\n}\n// Mobile Input Component\nfunction MobileInput({ label, error, className = \"\", ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `space-y-2 ${className}`,\n        children: [\n            label && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                className: \"block text-sm font-medium text-gray-700\",\n                children: label\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileDashboardLayout.tsx\",\n                lineNumber: 254,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                className: `mobile-input w-full ${error ? \"border-red-500\" : \"\"}`,\n                ...props\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileDashboardLayout.tsx\",\n                lineNumber: 258,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-sm text-red-600\",\n                children: error\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileDashboardLayout.tsx\",\n                lineNumber: 263,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileDashboardLayout.tsx\",\n        lineNumber: 252,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/mobile/MobileDashboardLayout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/mobile/MobileHeader.tsx":
/*!************************************************!*\
  !*** ./src/components/mobile/MobileHeader.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NotificationBadge: () => (/* binding */ NotificationBadge),\n/* harmony export */   QuickActionButton: () => (/* binding */ QuickActionButton),\n/* harmony export */   \"default\": () => (/* binding */ MobileHeader)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default,QuickActionButton,NotificationBadge auto */ \n\n\n\n\nfunction MobileHeader({ title, showBack = false, backHref = \"/dashboard\", actions }) {\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"lg:hidden fixed top-0 left-0 right-0 z-40 bg-white border-b border-gray-200 safe-area-pt\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-between px-4 py-3 h-14\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3 flex-1\",\n                    children: [\n                        showBack ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            href: backHref,\n                            className: \"p-2 -ml-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-5 h-5\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M15 19l-7-7 7-7\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileHeader.tsx\",\n                                    lineNumber: 34,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileHeader.tsx\",\n                                lineNumber: 33,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileHeader.tsx\",\n                            lineNumber: 29,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            href: \"/\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-10 h-10 relative\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    src: \"/invologo.png\",\n                                    alt: \"InvoNest Logo\",\n                                    width: 40,\n                                    height: 40,\n                                    className: \"object-contain w-full h-full\",\n                                    priority: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileHeader.tsx\",\n                                    lineNumber: 40,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileHeader.tsx\",\n                                lineNumber: 39,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileHeader.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 min-w-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-lg font-semibold text-gray-900 truncate\",\n                                children: title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileHeader.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileHeader.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileHeader.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [\n                        actions,\n                        user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            href: \"/dashboard/settings\",\n                            className: \"p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors\",\n                            title: \"Settings\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-5 h-5\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileHeader.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileHeader.tsx\",\n                                        lineNumber: 72,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileHeader.tsx\",\n                                lineNumber: 70,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileHeader.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 13\n                        }, this),\n                        user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            href: \"/dashboard/profile\",\n                            className: \"flex items-center space-x-2 p-1 rounded-lg hover:bg-gray-100 transition-colors\",\n                            title: \"Profile\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-8 h-8 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-full flex items-center justify-center text-white font-semibold text-sm\",\n                                children: user.name.charAt(0).toUpperCase()\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileHeader.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileHeader.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileHeader.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileHeader.tsx\",\n            lineNumber: 25,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileHeader.tsx\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, this);\n}\n// Quick Action Button Component\nfunction QuickActionButton({ icon, onClick, className = \"\" }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        onClick: onClick,\n        className: `p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors ${className}`,\n        children: icon\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileHeader.tsx\",\n        lineNumber: 106,\n        columnNumber: 5\n    }, this);\n}\n// Notification Badge Component\nfunction NotificationBadge({ count }) {\n    if (count === 0) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs font-bold rounded-full flex items-center justify-center\",\n        children: count > 99 ? \"99+\" : count\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\mobile\\\\MobileHeader.tsx\",\n        lineNumber: 120,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/mobile/MobileHeader.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth),\n/* harmony export */   useRequireAuth: () => (/* binding */ useRequireAuth),\n/* harmony export */   withAuth: () => (/* binding */ withAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../lib/auth */ \"(ssr)/./src/lib/auth.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth,withAuth,useRequireAuth auto */ \n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst AuthProvider = ({ children })=>{\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // Initialize auth state\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initAuth = async ()=>{\n            try {\n                if ((0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.isAuthenticated)()) {\n                    // Try to get user from localStorage first\n                    const cachedUser = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.getCurrentUser)();\n                    if (cachedUser) {\n                        setUser(cachedUser);\n                    }\n                    // Then refresh from server\n                    const freshUser = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.getProfile)();\n                    if (freshUser) {\n                        setUser(freshUser);\n                    } else {\n                        // Token might be expired, clear auth state\n                        (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.removeTokens)();\n                        setUser(null);\n                    }\n                }\n            } catch (error) {\n                console.error(\"Auth initialization error:\", error);\n                (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.removeTokens)();\n                setUser(null);\n            } finally{\n                setLoading(false);\n            }\n        };\n        initAuth();\n    }, []);\n    const login = async (data, remember = false)=>{\n        try {\n            const response = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.login)(data);\n            if (response.success && response.data) {\n                (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.setTokens)(response.data.token, response.data.refreshToken, remember);\n                (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.setCurrentUser)(response.data.user);\n                setUser(response.data.user);\n            }\n            return response;\n        } catch (error) {\n            console.error(\"Login error:\", error);\n            return {\n                success: false,\n                message: \"An unexpected error occurred during login.\"\n            };\n        }\n    };\n    const register = async (data)=>{\n        try {\n            const response = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.register)(data);\n            if (response.success && response.data) {\n                (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.setTokens)(response.data.token, response.data.refreshToken, true);\n                (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.setCurrentUser)(response.data.user);\n                setUser(response.data.user);\n            }\n            return response;\n        } catch (error) {\n            console.error(\"Register error:\", error);\n            return {\n                success: false,\n                message: \"An unexpected error occurred during registration.\"\n            };\n        }\n    };\n    const logout = async ()=>{\n        try {\n            await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.logout)();\n        } catch (error) {\n            console.error(\"Logout error:\", error);\n        } finally{\n            setUser(null);\n            router.push(\"/login\");\n        }\n    };\n    const updateUser = (updatedUser)=>{\n        setUser(updatedUser);\n        (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.setCurrentUser)(updatedUser);\n    };\n    const refreshUser = async ()=>{\n        try {\n            const freshUser = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.getProfile)();\n            if (freshUser) {\n                setUser(freshUser);\n            }\n        } catch (error) {\n            console.error(\"Refresh user error:\", error);\n        }\n    };\n    const value = {\n        user,\n        loading,\n        isLoggedIn: !!user,\n        login,\n        register,\n        logout,\n        updateUser,\n        refreshUser\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 155,\n        columnNumber: 5\n    }, undefined);\n};\nconst useAuth = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n};\n// Higher-order component for protected routes\nconst withAuth = (WrappedComponent)=>{\n    const AuthenticatedComponent = (props)=>{\n        const { isLoggedIn, loading } = useAuth();\n        const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n        (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n            if (!loading && !isLoggedIn) {\n                router.push(\"/login\");\n            }\n        }, [\n            isLoggedIn,\n            loading,\n            router\n        ]);\n        if (loading) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                    lineNumber: 186,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                lineNumber: 185,\n                columnNumber: 9\n            }, undefined);\n        }\n        if (!isLoggedIn) {\n            return null;\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WrappedComponent, {\n            ...props\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n            lineNumber: 195,\n            columnNumber: 12\n        }, undefined);\n    };\n    AuthenticatedComponent.displayName = `withAuth(${WrappedComponent.displayName || WrappedComponent.name})`;\n    return AuthenticatedComponent;\n};\n// Hook for protected routes\nconst useRequireAuth = ()=>{\n    const { isLoggedIn, loading } = useAuth();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!loading && !isLoggedIn) {\n            router.push(\"/login\");\n        }\n    }, [\n        isLoggedIn,\n        loading,\n        router\n    ]);\n    return {\n        isLoggedIn,\n        loading\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authenticatedFetch: () => (/* binding */ authenticatedFetch),\n/* harmony export */   getCurrentUser: () => (/* binding */ getCurrentUser),\n/* harmony export */   getProfile: () => (/* binding */ getProfile),\n/* harmony export */   getRefreshToken: () => (/* binding */ getRefreshToken),\n/* harmony export */   getToken: () => (/* binding */ getToken),\n/* harmony export */   isAuthenticated: () => (/* binding */ isAuthenticated),\n/* harmony export */   login: () => (/* binding */ login),\n/* harmony export */   logout: () => (/* binding */ logout),\n/* harmony export */   refreshAuthToken: () => (/* binding */ refreshAuthToken),\n/* harmony export */   register: () => (/* binding */ register),\n/* harmony export */   removeTokens: () => (/* binding */ removeTokens),\n/* harmony export */   setCurrentUser: () => (/* binding */ setCurrentUser),\n/* harmony export */   setTokens: () => (/* binding */ setTokens),\n/* harmony export */   updateProfile: () => (/* binding */ updateProfile)\n/* harmony export */ });\n// Authentication utilities and API functions\nconst API_BASE_URL = \"http://localhost:5000\" || 0;\n// Token management\nconst getToken = ()=>{\n    if (true) return null;\n    return localStorage.getItem(\"token\") || sessionStorage.getItem(\"token\");\n};\nconst getRefreshToken = ()=>{\n    if (true) return null;\n    return localStorage.getItem(\"refreshToken\") || sessionStorage.getItem(\"refreshToken\");\n};\nconst setTokens = (token, refreshToken, remember = false)=>{\n    if (true) return;\n    const storage = remember ? localStorage : sessionStorage;\n    storage.setItem(\"token\", token);\n    storage.setItem(\"refreshToken\", refreshToken);\n    // Also store in localStorage for consistency\n    localStorage.setItem(\"token\", token);\n};\nconst removeTokens = ()=>{\n    if (true) return;\n    localStorage.removeItem(\"token\");\n    localStorage.removeItem(\"refreshToken\");\n    localStorage.removeItem(\"user\");\n    sessionStorage.removeItem(\"token\");\n    sessionStorage.removeItem(\"refreshToken\");\n    sessionStorage.removeItem(\"user\");\n};\nconst getCurrentUser = ()=>{\n    if (true) return null;\n    const userStr = localStorage.getItem(\"user\") || sessionStorage.getItem(\"user\");\n    if (!userStr) return null;\n    try {\n        return JSON.parse(userStr);\n    } catch  {\n        return null;\n    }\n};\nconst setCurrentUser = (user)=>{\n    if (true) return;\n    localStorage.setItem(\"user\", JSON.stringify(user));\n};\n// API functions\nconst login = async (data)=>{\n    try {\n        console.log(\"\\uD83D\\uDD10 Login attempt:\", {\n            email: data.email,\n            passwordLength: data.password?.length\n        });\n        console.log(\"\\uD83C\\uDF10 API URL:\", `${API_BASE_URL}/api/auth/login`);\n        const response = await fetch(`${API_BASE_URL}/api/auth/login`, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(data)\n        });\n        console.log(\"\\uD83D\\uDCE1 Response status:\", response.status, response.statusText);\n        const result = await response.json();\n        console.log(\"\\uD83D\\uDCCB Response data:\", result);\n        return result;\n    } catch (error) {\n        console.error(\"❌ Login error:\", error);\n        return {\n            success: false,\n            message: \"Network error. Please check your connection and try again.\"\n        };\n    }\n};\nconst register = async (data)=>{\n    try {\n        const response = await fetch(`${API_BASE_URL}/api/auth/register`, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(data)\n        });\n        return await response.json();\n    } catch (error) {\n        console.error(\"Register error:\", error);\n        return {\n            success: false,\n            message: \"Network error. Please check your connection and try again.\"\n        };\n    }\n};\nconst logout = async ()=>{\n    try {\n        const token = getToken();\n        if (token) {\n            await fetch(`${API_BASE_URL}/api/auth/logout`, {\n                method: \"POST\",\n                headers: {\n                    \"Authorization\": `Bearer ${token}`,\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n        }\n    } catch (error) {\n        console.error(\"Logout error:\", error);\n    } finally{\n        removeTokens();\n    }\n};\nconst getProfile = async ()=>{\n    try {\n        const token = getToken();\n        if (!token) return null;\n        const response = await fetch(`${API_BASE_URL}/api/auth/profile`, {\n            headers: {\n                \"Authorization\": `Bearer ${token}`,\n                \"Content-Type\": \"application/json\"\n            }\n        });\n        const data = await response.json();\n        if (data.success) {\n            setCurrentUser(data.data.user);\n            return data.data.user;\n        }\n        return null;\n    } catch (error) {\n        console.error(\"Get profile error:\", error);\n        return null;\n    }\n};\nconst updateProfile = async (userData)=>{\n    try {\n        const token = getToken();\n        if (!token) {\n            return {\n                success: false,\n                message: \"No authentication token found\"\n            };\n        }\n        console.log(\"Sending profile update request:\", userData);\n        const response = await fetch(`${API_BASE_URL}/api/auth/profile`, {\n            method: \"PUT\",\n            headers: {\n                \"Authorization\": `Bearer ${token}`,\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(userData)\n        });\n        const data = await response.json();\n        console.log(\"Profile update response:\", data);\n        if (data.success) {\n            setCurrentUser(data.data.user);\n        }\n        return data;\n    } catch (error) {\n        console.error(\"Update profile error:\", error);\n        return {\n            success: false,\n            message: \"Network error. Please check your connection and try again.\"\n        };\n    }\n};\n// Check if user is authenticated\nconst isAuthenticated = ()=>{\n    return !!getToken();\n};\n// Create authenticated fetch function\nconst authenticatedFetch = async (url, options = {})=>{\n    const token = getToken();\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        ...options.headers || {}\n    };\n    if (token) {\n        headers[\"Authorization\"] = `Bearer ${token}`;\n    }\n    return fetch(url.startsWith(\"http\") ? url : `${API_BASE_URL}${url}`, {\n        ...options,\n        headers\n    });\n};\n// Refresh token function\nconst refreshAuthToken = async ()=>{\n    try {\n        const refreshToken = getRefreshToken();\n        if (!refreshToken) return false;\n        const response = await fetch(`${API_BASE_URL}/api/auth/refresh`, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                refreshToken\n            })\n        });\n        const data = await response.json();\n        if (data.success) {\n            setTokens(data.data.token, data.data.refreshToken);\n            return true;\n        }\n        return false;\n    } catch (error) {\n        console.error(\"Token refresh error:\", error);\n        return false;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL2F1dGgudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSw2Q0FBNkM7QUFFN0MsTUFBTUEsZUFBZUMsdUJBQStCLElBQUk7QUF1RHhELG1CQUFtQjtBQUNaLE1BQU1HLFdBQVc7SUFDdEIsSUFBSSxJQUFrQixFQUFhLE9BQU87SUFDMUMsT0FBT0MsYUFBYUMsT0FBTyxDQUFDLFlBQVlDLGVBQWVELE9BQU8sQ0FBQztBQUNqRSxFQUFFO0FBRUssTUFBTUUsa0JBQWtCO0lBQzdCLElBQUksSUFBa0IsRUFBYSxPQUFPO0lBQzFDLE9BQU9ILGFBQWFDLE9BQU8sQ0FBQyxtQkFBbUJDLGVBQWVELE9BQU8sQ0FBQztBQUN4RSxFQUFFO0FBRUssTUFBTUcsWUFBWSxDQUFDQyxPQUFlQyxjQUFzQkMsV0FBb0IsS0FBSztJQUN0RixJQUFJLElBQWtCLEVBQWE7SUFFbkMsTUFBTUMsVUFBVUQsV0FBV1AsZUFBZUU7SUFDMUNNLFFBQVFDLE9BQU8sQ0FBQyxTQUFTSjtJQUN6QkcsUUFBUUMsT0FBTyxDQUFDLGdCQUFnQkg7SUFFaEMsNkNBQTZDO0lBQzdDTixhQUFhUyxPQUFPLENBQUMsU0FBU0o7QUFDaEMsRUFBRTtBQUVLLE1BQU1LLGVBQWU7SUFDMUIsSUFBSSxJQUFrQixFQUFhO0lBRW5DVixhQUFhVyxVQUFVLENBQUM7SUFDeEJYLGFBQWFXLFVBQVUsQ0FBQztJQUN4QlgsYUFBYVcsVUFBVSxDQUFDO0lBQ3hCVCxlQUFlUyxVQUFVLENBQUM7SUFDMUJULGVBQWVTLFVBQVUsQ0FBQztJQUMxQlQsZUFBZVMsVUFBVSxDQUFDO0FBQzVCLEVBQUU7QUFFSyxNQUFNQyxpQkFBaUI7SUFDNUIsSUFBSSxJQUFrQixFQUFhLE9BQU87SUFFMUMsTUFBTUMsVUFBVWIsYUFBYUMsT0FBTyxDQUFDLFdBQVdDLGVBQWVELE9BQU8sQ0FBQztJQUN2RSxJQUFJLENBQUNZLFNBQVMsT0FBTztJQUVyQixJQUFJO1FBQ0YsT0FBT0MsS0FBS0MsS0FBSyxDQUFDRjtJQUNwQixFQUFFLE9BQU07UUFDTixPQUFPO0lBQ1Q7QUFDRixFQUFFO0FBRUssTUFBTUcsaUJBQWlCLENBQUNDO0lBQzdCLElBQUksSUFBa0IsRUFBYTtJQUVuQ2pCLGFBQWFTLE9BQU8sQ0FBQyxRQUFRSyxLQUFLSSxTQUFTLENBQUNEO0FBQzlDLEVBQUU7QUFFRixnQkFBZ0I7QUFDVCxNQUFNRSxRQUFRLE9BQU9DO0lBQzFCLElBQUk7UUFDRkMsUUFBUUMsR0FBRyxDQUFDLCtCQUFxQjtZQUFFQyxPQUFPSCxLQUFLRyxLQUFLO1lBQUVDLGdCQUFnQkosS0FBS0ssUUFBUSxFQUFFQztRQUFPO1FBQzVGTCxRQUFRQyxHQUFHLENBQUMseUJBQWUsQ0FBQyxFQUFFM0IsYUFBYSxlQUFlLENBQUM7UUFFM0QsTUFBTWdDLFdBQVcsTUFBTUMsTUFBTSxDQUFDLEVBQUVqQyxhQUFhLGVBQWUsQ0FBQyxFQUFFO1lBQzdEa0MsUUFBUTtZQUNSQyxTQUFTO2dCQUNQLGdCQUFnQjtZQUNsQjtZQUNBQyxNQUFNakIsS0FBS0ksU0FBUyxDQUFDRTtRQUN2QjtRQUVBQyxRQUFRQyxHQUFHLENBQUMsaUNBQXVCSyxTQUFTSyxNQUFNLEVBQUVMLFNBQVNNLFVBQVU7UUFFdkUsTUFBTUMsU0FBUyxNQUFNUCxTQUFTUSxJQUFJO1FBQ2xDZCxRQUFRQyxHQUFHLENBQUMsK0JBQXFCWTtRQUVqQyxPQUFPQTtJQUNULEVBQUUsT0FBT0UsT0FBTztRQUNkZixRQUFRZSxLQUFLLENBQUMsa0JBQWtCQTtRQUNoQyxPQUFPO1lBQ0xDLFNBQVM7WUFDVEMsU0FBUztRQUNYO0lBQ0Y7QUFDRixFQUFFO0FBRUssTUFBTUMsV0FBVyxPQUFPbkI7SUFDN0IsSUFBSTtRQUNGLE1BQU1PLFdBQVcsTUFBTUMsTUFBTSxDQUFDLEVBQUVqQyxhQUFhLGtCQUFrQixDQUFDLEVBQUU7WUFDaEVrQyxRQUFRO1lBQ1JDLFNBQVM7Z0JBQ1AsZ0JBQWdCO1lBQ2xCO1lBQ0FDLE1BQU1qQixLQUFLSSxTQUFTLENBQUNFO1FBQ3ZCO1FBRUEsT0FBTyxNQUFNTyxTQUFTUSxJQUFJO0lBQzVCLEVBQUUsT0FBT0MsT0FBTztRQUNkZixRQUFRZSxLQUFLLENBQUMsbUJBQW1CQTtRQUNqQyxPQUFPO1lBQ0xDLFNBQVM7WUFDVEMsU0FBUztRQUNYO0lBQ0Y7QUFDRixFQUFFO0FBRUssTUFBTUUsU0FBUztJQUNwQixJQUFJO1FBQ0YsTUFBTW5DLFFBQVFOO1FBQ2QsSUFBSU0sT0FBTztZQUNULE1BQU11QixNQUFNLENBQUMsRUFBRWpDLGFBQWEsZ0JBQWdCLENBQUMsRUFBRTtnQkFDN0NrQyxRQUFRO2dCQUNSQyxTQUFTO29CQUNQLGlCQUFpQixDQUFDLE9BQU8sRUFBRXpCLE1BQU0sQ0FBQztvQkFDbEMsZ0JBQWdCO2dCQUNsQjtZQUNGO1FBQ0Y7SUFDRixFQUFFLE9BQU8rQixPQUFPO1FBQ2RmLFFBQVFlLEtBQUssQ0FBQyxpQkFBaUJBO0lBQ2pDLFNBQVU7UUFDUjFCO0lBQ0Y7QUFDRixFQUFFO0FBRUssTUFBTStCLGFBQWE7SUFDeEIsSUFBSTtRQUNGLE1BQU1wQyxRQUFRTjtRQUNkLElBQUksQ0FBQ00sT0FBTyxPQUFPO1FBRW5CLE1BQU1zQixXQUFXLE1BQU1DLE1BQU0sQ0FBQyxFQUFFakMsYUFBYSxpQkFBaUIsQ0FBQyxFQUFFO1lBQy9EbUMsU0FBUztnQkFDUCxpQkFBaUIsQ0FBQyxPQUFPLEVBQUV6QixNQUFNLENBQUM7Z0JBQ2xDLGdCQUFnQjtZQUNsQjtRQUNGO1FBRUEsTUFBTWUsT0FBTyxNQUFNTyxTQUFTUSxJQUFJO1FBRWhDLElBQUlmLEtBQUtpQixPQUFPLEVBQUU7WUFDaEJyQixlQUFlSSxLQUFLQSxJQUFJLENBQUNILElBQUk7WUFDN0IsT0FBT0csS0FBS0EsSUFBSSxDQUFDSCxJQUFJO1FBQ3ZCO1FBRUEsT0FBTztJQUNULEVBQUUsT0FBT21CLE9BQU87UUFDZGYsUUFBUWUsS0FBSyxDQUFDLHNCQUFzQkE7UUFDcEMsT0FBTztJQUNUO0FBQ0YsRUFBRTtBQUVLLE1BQU1NLGdCQUFnQixPQUFPQztJQUNsQyxJQUFJO1FBQ0YsTUFBTXRDLFFBQVFOO1FBQ2QsSUFBSSxDQUFDTSxPQUFPO1lBQ1YsT0FBTztnQkFDTGdDLFNBQVM7Z0JBQ1RDLFNBQVM7WUFDWDtRQUNGO1FBRUFqQixRQUFRQyxHQUFHLENBQUMsbUNBQW1DcUI7UUFDL0MsTUFBTWhCLFdBQVcsTUFBTUMsTUFBTSxDQUFDLEVBQUVqQyxhQUFhLGlCQUFpQixDQUFDLEVBQUU7WUFDL0RrQyxRQUFRO1lBQ1JDLFNBQVM7Z0JBQ1AsaUJBQWlCLENBQUMsT0FBTyxFQUFFekIsTUFBTSxDQUFDO2dCQUNsQyxnQkFBZ0I7WUFDbEI7WUFDQTBCLE1BQU1qQixLQUFLSSxTQUFTLENBQUN5QjtRQUN2QjtRQUVBLE1BQU12QixPQUFPLE1BQU1PLFNBQVNRLElBQUk7UUFDaENkLFFBQVFDLEdBQUcsQ0FBQyw0QkFBNEJGO1FBRXhDLElBQUlBLEtBQUtpQixPQUFPLEVBQUU7WUFDaEJyQixlQUFlSSxLQUFLQSxJQUFJLENBQUNILElBQUk7UUFDL0I7UUFFQSxPQUFPRztJQUNULEVBQUUsT0FBT2dCLE9BQU87UUFDZGYsUUFBUWUsS0FBSyxDQUFDLHlCQUF5QkE7UUFDdkMsT0FBTztZQUNMQyxTQUFTO1lBQ1RDLFNBQVM7UUFDWDtJQUNGO0FBQ0YsRUFBRTtBQUVGLGlDQUFpQztBQUMxQixNQUFNTSxrQkFBa0I7SUFDN0IsT0FBTyxDQUFDLENBQUM3QztBQUNYLEVBQUU7QUFFRixzQ0FBc0M7QUFDL0IsTUFBTThDLHFCQUFxQixPQUFPQyxLQUFhQyxVQUF1QixDQUFDLENBQUM7SUFDN0UsTUFBTTFDLFFBQVFOO0lBRWQsTUFBTStCLFVBQWtDO1FBQ3RDLGdCQUFnQjtRQUNoQixHQUFJaUIsUUFBUWpCLE9BQU8sSUFBOEIsQ0FBQyxDQUFDO0lBQ3JEO0lBRUEsSUFBSXpCLE9BQU87UUFDVHlCLE9BQU8sQ0FBQyxnQkFBZ0IsR0FBRyxDQUFDLE9BQU8sRUFBRXpCLE1BQU0sQ0FBQztJQUM5QztJQUVBLE9BQU91QixNQUFNa0IsSUFBSUUsVUFBVSxDQUFDLFVBQVVGLE1BQU0sQ0FBQyxFQUFFbkQsYUFBYSxFQUFFbUQsSUFBSSxDQUFDLEVBQUU7UUFDbkUsR0FBR0MsT0FBTztRQUNWakI7SUFDRjtBQUNGLEVBQUU7QUFFRix5QkFBeUI7QUFDbEIsTUFBTW1CLG1CQUFtQjtJQUM5QixJQUFJO1FBQ0YsTUFBTTNDLGVBQWVIO1FBQ3JCLElBQUksQ0FBQ0csY0FBYyxPQUFPO1FBRTFCLE1BQU1xQixXQUFXLE1BQU1DLE1BQU0sQ0FBQyxFQUFFakMsYUFBYSxpQkFBaUIsQ0FBQyxFQUFFO1lBQy9Ea0MsUUFBUTtZQUNSQyxTQUFTO2dCQUNQLGdCQUFnQjtZQUNsQjtZQUNBQyxNQUFNakIsS0FBS0ksU0FBUyxDQUFDO2dCQUFFWjtZQUFhO1FBQ3RDO1FBRUEsTUFBTWMsT0FBTyxNQUFNTyxTQUFTUSxJQUFJO1FBRWhDLElBQUlmLEtBQUtpQixPQUFPLEVBQUU7WUFDaEJqQyxVQUFVZ0IsS0FBS0EsSUFBSSxDQUFDZixLQUFLLEVBQUVlLEtBQUtBLElBQUksQ0FBQ2QsWUFBWTtZQUNqRCxPQUFPO1FBQ1Q7UUFFQSxPQUFPO0lBQ1QsRUFBRSxPQUFPOEIsT0FBTztRQUNkZixRQUFRZSxLQUFLLENBQUMsd0JBQXdCQTtRQUN0QyxPQUFPO0lBQ1Q7QUFDRixFQUFFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZnJvbnRlbmQvLi9zcmMvbGliL2F1dGgudHM/NjY5MiJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBBdXRoZW50aWNhdGlvbiB1dGlsaXRpZXMgYW5kIEFQSSBmdW5jdGlvbnNcblxuY29uc3QgQVBJX0JBU0VfVVJMID0gcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfQVBJX1VSTCB8fCAnaHR0cDovL2xvY2FsaG9zdDo1MDAwJztcblxuZXhwb3J0IGludGVyZmFjZSBVc2VyIHtcbiAgaWQ6IHN0cmluZztcbiAgbmFtZTogc3RyaW5nO1xuICBlbWFpbDogc3RyaW5nO1xuICByb2xlOiAndXNlcicgfCAnYWRtaW4nO1xuICBidXNpbmVzc05hbWU/OiBzdHJpbmc7XG4gIGxvZ28/OiBzdHJpbmc7XG4gIGdzdE51bWJlcj86IHN0cmluZztcbiAgcGhvbmU/OiBzdHJpbmc7XG4gIGFkZHJlc3M/OiB7XG4gICAgc3RyZWV0OiBzdHJpbmc7XG4gICAgY2l0eTogc3RyaW5nO1xuICAgIHN0YXRlOiBzdHJpbmc7XG4gICAgcGluY29kZTogc3RyaW5nO1xuICAgIGNvdW50cnk6IHN0cmluZztcbiAgfTtcbiAgaXNFbWFpbFZlcmlmaWVkOiBib29sZWFuO1xuICBsYXN0TG9naW4/OiBzdHJpbmc7XG4gIGNyZWF0ZWRBdDogc3RyaW5nO1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIEF1dGhSZXNwb25zZSB7XG4gIHN1Y2Nlc3M6IGJvb2xlYW47XG4gIG1lc3NhZ2U6IHN0cmluZztcbiAgZGF0YT86IHtcbiAgICB1c2VyOiBVc2VyO1xuICAgIHRva2VuOiBzdHJpbmc7XG4gICAgcmVmcmVzaFRva2VuOiBzdHJpbmc7XG4gIH07XG4gIGVycm9ycz86IHN0cmluZ1tdO1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIExvZ2luRGF0YSB7XG4gIGVtYWlsOiBzdHJpbmc7XG4gIHBhc3N3b3JkOiBzdHJpbmc7XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgUmVnaXN0ZXJEYXRhIHtcbiAgbmFtZTogc3RyaW5nO1xuICBlbWFpbDogc3RyaW5nO1xuICBwYXNzd29yZDogc3RyaW5nO1xuICBidXNpbmVzc05hbWU/OiBzdHJpbmc7XG4gIGdzdE51bWJlcj86IHN0cmluZztcbiAgcGhvbmU/OiBzdHJpbmc7XG4gIGFkZHJlc3M/OiB7XG4gICAgc3RyZWV0OiBzdHJpbmc7XG4gICAgY2l0eTogc3RyaW5nO1xuICAgIHN0YXRlOiBzdHJpbmc7XG4gICAgcGluY29kZTogc3RyaW5nO1xuICAgIGNvdW50cnk6IHN0cmluZztcbiAgfTtcbn1cblxuLy8gVG9rZW4gbWFuYWdlbWVudFxuZXhwb3J0IGNvbnN0IGdldFRva2VuID0gKCk6IHN0cmluZyB8IG51bGwgPT4ge1xuICBpZiAodHlwZW9mIHdpbmRvdyA9PT0gJ3VuZGVmaW5lZCcpIHJldHVybiBudWxsO1xuICByZXR1cm4gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ3Rva2VuJykgfHwgc2Vzc2lvblN0b3JhZ2UuZ2V0SXRlbSgndG9rZW4nKTtcbn07XG5cbmV4cG9ydCBjb25zdCBnZXRSZWZyZXNoVG9rZW4gPSAoKTogc3RyaW5nIHwgbnVsbCA9PiB7XG4gIGlmICh0eXBlb2Ygd2luZG93ID09PSAndW5kZWZpbmVkJykgcmV0dXJuIG51bGw7XG4gIHJldHVybiBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgncmVmcmVzaFRva2VuJykgfHwgc2Vzc2lvblN0b3JhZ2UuZ2V0SXRlbSgncmVmcmVzaFRva2VuJyk7XG59O1xuXG5leHBvcnQgY29uc3Qgc2V0VG9rZW5zID0gKHRva2VuOiBzdHJpbmcsIHJlZnJlc2hUb2tlbjogc3RyaW5nLCByZW1lbWJlcjogYm9vbGVhbiA9IGZhbHNlKSA9PiB7XG4gIGlmICh0eXBlb2Ygd2luZG93ID09PSAndW5kZWZpbmVkJykgcmV0dXJuO1xuICBcbiAgY29uc3Qgc3RvcmFnZSA9IHJlbWVtYmVyID8gbG9jYWxTdG9yYWdlIDogc2Vzc2lvblN0b3JhZ2U7XG4gIHN0b3JhZ2Uuc2V0SXRlbSgndG9rZW4nLCB0b2tlbik7XG4gIHN0b3JhZ2Uuc2V0SXRlbSgncmVmcmVzaFRva2VuJywgcmVmcmVzaFRva2VuKTtcbiAgXG4gIC8vIEFsc28gc3RvcmUgaW4gbG9jYWxTdG9yYWdlIGZvciBjb25zaXN0ZW5jeVxuICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbSgndG9rZW4nLCB0b2tlbik7XG59O1xuXG5leHBvcnQgY29uc3QgcmVtb3ZlVG9rZW5zID0gKCkgPT4ge1xuICBpZiAodHlwZW9mIHdpbmRvdyA9PT0gJ3VuZGVmaW5lZCcpIHJldHVybjtcbiAgXG4gIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCd0b2tlbicpO1xuICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgncmVmcmVzaFRva2VuJyk7XG4gIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCd1c2VyJyk7XG4gIHNlc3Npb25TdG9yYWdlLnJlbW92ZUl0ZW0oJ3Rva2VuJyk7XG4gIHNlc3Npb25TdG9yYWdlLnJlbW92ZUl0ZW0oJ3JlZnJlc2hUb2tlbicpO1xuICBzZXNzaW9uU3RvcmFnZS5yZW1vdmVJdGVtKCd1c2VyJyk7XG59O1xuXG5leHBvcnQgY29uc3QgZ2V0Q3VycmVudFVzZXIgPSAoKTogVXNlciB8IG51bGwgPT4ge1xuICBpZiAodHlwZW9mIHdpbmRvdyA9PT0gJ3VuZGVmaW5lZCcpIHJldHVybiBudWxsO1xuICBcbiAgY29uc3QgdXNlclN0ciA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCd1c2VyJykgfHwgc2Vzc2lvblN0b3JhZ2UuZ2V0SXRlbSgndXNlcicpO1xuICBpZiAoIXVzZXJTdHIpIHJldHVybiBudWxsO1xuICBcbiAgdHJ5IHtcbiAgICByZXR1cm4gSlNPTi5wYXJzZSh1c2VyU3RyKTtcbiAgfSBjYXRjaCB7XG4gICAgcmV0dXJuIG51bGw7XG4gIH1cbn07XG5cbmV4cG9ydCBjb25zdCBzZXRDdXJyZW50VXNlciA9ICh1c2VyOiBVc2VyKSA9PiB7XG4gIGlmICh0eXBlb2Ygd2luZG93ID09PSAndW5kZWZpbmVkJykgcmV0dXJuO1xuICBcbiAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oJ3VzZXInLCBKU09OLnN0cmluZ2lmeSh1c2VyKSk7XG59O1xuXG4vLyBBUEkgZnVuY3Rpb25zXG5leHBvcnQgY29uc3QgbG9naW4gPSBhc3luYyAoZGF0YTogTG9naW5EYXRhKTogUHJvbWlzZTxBdXRoUmVzcG9uc2U+ID0+IHtcbiAgdHJ5IHtcbiAgICBjb25zb2xlLmxvZygn8J+UkCBMb2dpbiBhdHRlbXB0OicsIHsgZW1haWw6IGRhdGEuZW1haWwsIHBhc3N3b3JkTGVuZ3RoOiBkYXRhLnBhc3N3b3JkPy5sZW5ndGggfSk7XG4gICAgY29uc29sZS5sb2coJ/CfjJAgQVBJIFVSTDonLCBgJHtBUElfQkFTRV9VUkx9L2FwaS9hdXRoL2xvZ2luYCk7XG5cbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGAke0FQSV9CQVNFX1VSTH0vYXBpL2F1dGgvbG9naW5gLCB7XG4gICAgICBtZXRob2Q6ICdQT1NUJyxcbiAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgIH0sXG4gICAgICBib2R5OiBKU09OLnN0cmluZ2lmeShkYXRhKSxcbiAgICB9KTtcblxuICAgIGNvbnNvbGUubG9nKCfwn5OhIFJlc3BvbnNlIHN0YXR1czonLCByZXNwb25zZS5zdGF0dXMsIHJlc3BvbnNlLnN0YXR1c1RleHQpO1xuXG4gICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgIGNvbnNvbGUubG9nKCfwn5OLIFJlc3BvbnNlIGRhdGE6JywgcmVzdWx0KTtcblxuICAgIHJldHVybiByZXN1bHQ7XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcign4p2MIExvZ2luIGVycm9yOicsIGVycm9yKTtcbiAgICByZXR1cm4ge1xuICAgICAgc3VjY2VzczogZmFsc2UsXG4gICAgICBtZXNzYWdlOiAnTmV0d29yayBlcnJvci4gUGxlYXNlIGNoZWNrIHlvdXIgY29ubmVjdGlvbiBhbmQgdHJ5IGFnYWluLicsXG4gICAgfTtcbiAgfVxufTtcblxuZXhwb3J0IGNvbnN0IHJlZ2lzdGVyID0gYXN5bmMgKGRhdGE6IFJlZ2lzdGVyRGF0YSk6IFByb21pc2U8QXV0aFJlc3BvbnNlPiA9PiB7XG4gIHRyeSB7XG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgJHtBUElfQkFTRV9VUkx9L2FwaS9hdXRoL3JlZ2lzdGVyYCwge1xuICAgICAgbWV0aG9kOiAnUE9TVCcsXG4gICAgICBoZWFkZXJzOiB7XG4gICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICB9LFxuICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoZGF0YSksXG4gICAgfSk7XG5cbiAgICByZXR1cm4gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ1JlZ2lzdGVyIGVycm9yOicsIGVycm9yKTtcbiAgICByZXR1cm4ge1xuICAgICAgc3VjY2VzczogZmFsc2UsXG4gICAgICBtZXNzYWdlOiAnTmV0d29yayBlcnJvci4gUGxlYXNlIGNoZWNrIHlvdXIgY29ubmVjdGlvbiBhbmQgdHJ5IGFnYWluLicsXG4gICAgfTtcbiAgfVxufTtcblxuZXhwb3J0IGNvbnN0IGxvZ291dCA9IGFzeW5jICgpOiBQcm9taXNlPHZvaWQ+ID0+IHtcbiAgdHJ5IHtcbiAgICBjb25zdCB0b2tlbiA9IGdldFRva2VuKCk7XG4gICAgaWYgKHRva2VuKSB7XG4gICAgICBhd2FpdCBmZXRjaChgJHtBUElfQkFTRV9VUkx9L2FwaS9hdXRoL2xvZ291dGAsIHtcbiAgICAgICAgbWV0aG9kOiAnUE9TVCcsXG4gICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICAnQXV0aG9yaXphdGlvbic6IGBCZWFyZXIgJHt0b2tlbn1gLFxuICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgIH0sXG4gICAgICB9KTtcbiAgICB9XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignTG9nb3V0IGVycm9yOicsIGVycm9yKTtcbiAgfSBmaW5hbGx5IHtcbiAgICByZW1vdmVUb2tlbnMoKTtcbiAgfVxufTtcblxuZXhwb3J0IGNvbnN0IGdldFByb2ZpbGUgPSBhc3luYyAoKTogUHJvbWlzZTxVc2VyIHwgbnVsbD4gPT4ge1xuICB0cnkge1xuICAgIGNvbnN0IHRva2VuID0gZ2V0VG9rZW4oKTtcbiAgICBpZiAoIXRva2VuKSByZXR1cm4gbnVsbDtcblxuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYCR7QVBJX0JBU0VfVVJMfS9hcGkvYXV0aC9wcm9maWxlYCwge1xuICAgICAgaGVhZGVyczoge1xuICAgICAgICAnQXV0aG9yaXphdGlvbic6IGBCZWFyZXIgJHt0b2tlbn1gLFxuICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgfSxcbiAgICB9KTtcblxuICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gICAgXG4gICAgaWYgKGRhdGEuc3VjY2Vzcykge1xuICAgICAgc2V0Q3VycmVudFVzZXIoZGF0YS5kYXRhLnVzZXIpO1xuICAgICAgcmV0dXJuIGRhdGEuZGF0YS51c2VyO1xuICAgIH1cbiAgICBcbiAgICByZXR1cm4gbnVsbDtcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdHZXQgcHJvZmlsZSBlcnJvcjonLCBlcnJvcik7XG4gICAgcmV0dXJuIG51bGw7XG4gIH1cbn07XG5cbmV4cG9ydCBjb25zdCB1cGRhdGVQcm9maWxlID0gYXN5bmMgKHVzZXJEYXRhOiBQYXJ0aWFsPFVzZXI+KTogUHJvbWlzZTxBdXRoUmVzcG9uc2U+ID0+IHtcbiAgdHJ5IHtcbiAgICBjb25zdCB0b2tlbiA9IGdldFRva2VuKCk7XG4gICAgaWYgKCF0b2tlbikge1xuICAgICAgcmV0dXJuIHtcbiAgICAgICAgc3VjY2VzczogZmFsc2UsXG4gICAgICAgIG1lc3NhZ2U6ICdObyBhdXRoZW50aWNhdGlvbiB0b2tlbiBmb3VuZCcsXG4gICAgICB9O1xuICAgIH1cblxuICAgIGNvbnNvbGUubG9nKCdTZW5kaW5nIHByb2ZpbGUgdXBkYXRlIHJlcXVlc3Q6JywgdXNlckRhdGEpO1xuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYCR7QVBJX0JBU0VfVVJMfS9hcGkvYXV0aC9wcm9maWxlYCwge1xuICAgICAgbWV0aG9kOiAnUFVUJyxcbiAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgJ0F1dGhvcml6YXRpb24nOiBgQmVhcmVyICR7dG9rZW59YCxcbiAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgIH0sXG4gICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh1c2VyRGF0YSksXG4gICAgfSk7XG5cbiAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgIGNvbnNvbGUubG9nKCdQcm9maWxlIHVwZGF0ZSByZXNwb25zZTonLCBkYXRhKTtcblxuICAgIGlmIChkYXRhLnN1Y2Nlc3MpIHtcbiAgICAgIHNldEN1cnJlbnRVc2VyKGRhdGEuZGF0YS51c2VyKTtcbiAgICB9XG5cbiAgICByZXR1cm4gZGF0YTtcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdVcGRhdGUgcHJvZmlsZSBlcnJvcjonLCBlcnJvcik7XG4gICAgcmV0dXJuIHtcbiAgICAgIHN1Y2Nlc3M6IGZhbHNlLFxuICAgICAgbWVzc2FnZTogJ05ldHdvcmsgZXJyb3IuIFBsZWFzZSBjaGVjayB5b3VyIGNvbm5lY3Rpb24gYW5kIHRyeSBhZ2Fpbi4nLFxuICAgIH07XG4gIH1cbn07XG5cbi8vIENoZWNrIGlmIHVzZXIgaXMgYXV0aGVudGljYXRlZFxuZXhwb3J0IGNvbnN0IGlzQXV0aGVudGljYXRlZCA9ICgpOiBib29sZWFuID0+IHtcbiAgcmV0dXJuICEhZ2V0VG9rZW4oKTtcbn07XG5cbi8vIENyZWF0ZSBhdXRoZW50aWNhdGVkIGZldGNoIGZ1bmN0aW9uXG5leHBvcnQgY29uc3QgYXV0aGVudGljYXRlZEZldGNoID0gYXN5bmMgKHVybDogc3RyaW5nLCBvcHRpb25zOiBSZXF1ZXN0SW5pdCA9IHt9KTogUHJvbWlzZTxSZXNwb25zZT4gPT4ge1xuICBjb25zdCB0b2tlbiA9IGdldFRva2VuKCk7XG5cbiAgY29uc3QgaGVhZGVyczogUmVjb3JkPHN0cmluZywgc3RyaW5nPiA9IHtcbiAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgIC4uLihvcHRpb25zLmhlYWRlcnMgYXMgUmVjb3JkPHN0cmluZywgc3RyaW5nPiB8fCB7fSksXG4gIH07XG5cbiAgaWYgKHRva2VuKSB7XG4gICAgaGVhZGVyc1snQXV0aG9yaXphdGlvbiddID0gYEJlYXJlciAke3Rva2VufWA7XG4gIH1cbiAgXG4gIHJldHVybiBmZXRjaCh1cmwuc3RhcnRzV2l0aCgnaHR0cCcpID8gdXJsIDogYCR7QVBJX0JBU0VfVVJMfSR7dXJsfWAsIHtcbiAgICAuLi5vcHRpb25zLFxuICAgIGhlYWRlcnMsXG4gIH0pO1xufTtcblxuLy8gUmVmcmVzaCB0b2tlbiBmdW5jdGlvblxuZXhwb3J0IGNvbnN0IHJlZnJlc2hBdXRoVG9rZW4gPSBhc3luYyAoKTogUHJvbWlzZTxib29sZWFuPiA9PiB7XG4gIHRyeSB7XG4gICAgY29uc3QgcmVmcmVzaFRva2VuID0gZ2V0UmVmcmVzaFRva2VuKCk7XG4gICAgaWYgKCFyZWZyZXNoVG9rZW4pIHJldHVybiBmYWxzZTtcblxuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYCR7QVBJX0JBU0VfVVJMfS9hcGkvYXV0aC9yZWZyZXNoYCwge1xuICAgICAgbWV0aG9kOiAnUE9TVCcsXG4gICAgICBoZWFkZXJzOiB7XG4gICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICB9LFxuICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoeyByZWZyZXNoVG9rZW4gfSksXG4gICAgfSk7XG5cbiAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgIFxuICAgIGlmIChkYXRhLnN1Y2Nlc3MpIHtcbiAgICAgIHNldFRva2VucyhkYXRhLmRhdGEudG9rZW4sIGRhdGEuZGF0YS5yZWZyZXNoVG9rZW4pO1xuICAgICAgcmV0dXJuIHRydWU7XG4gICAgfVxuICAgIFxuICAgIHJldHVybiBmYWxzZTtcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdUb2tlbiByZWZyZXNoIGVycm9yOicsIGVycm9yKTtcbiAgICByZXR1cm4gZmFsc2U7XG4gIH1cbn07XG4iXSwibmFtZXMiOlsiQVBJX0JBU0VfVVJMIiwicHJvY2VzcyIsImVudiIsIk5FWFRfUFVCTElDX0FQSV9VUkwiLCJnZXRUb2tlbiIsImxvY2FsU3RvcmFnZSIsImdldEl0ZW0iLCJzZXNzaW9uU3RvcmFnZSIsImdldFJlZnJlc2hUb2tlbiIsInNldFRva2VucyIsInRva2VuIiwicmVmcmVzaFRva2VuIiwicmVtZW1iZXIiLCJzdG9yYWdlIiwic2V0SXRlbSIsInJlbW92ZVRva2VucyIsInJlbW92ZUl0ZW0iLCJnZXRDdXJyZW50VXNlciIsInVzZXJTdHIiLCJKU09OIiwicGFyc2UiLCJzZXRDdXJyZW50VXNlciIsInVzZXIiLCJzdHJpbmdpZnkiLCJsb2dpbiIsImRhdGEiLCJjb25zb2xlIiwibG9nIiwiZW1haWwiLCJwYXNzd29yZExlbmd0aCIsInBhc3N3b3JkIiwibGVuZ3RoIiwicmVzcG9uc2UiLCJmZXRjaCIsIm1ldGhvZCIsImhlYWRlcnMiLCJib2R5Iiwic3RhdHVzIiwic3RhdHVzVGV4dCIsInJlc3VsdCIsImpzb24iLCJlcnJvciIsInN1Y2Nlc3MiLCJtZXNzYWdlIiwicmVnaXN0ZXIiLCJsb2dvdXQiLCJnZXRQcm9maWxlIiwidXBkYXRlUHJvZmlsZSIsInVzZXJEYXRhIiwiaXNBdXRoZW50aWNhdGVkIiwiYXV0aGVudGljYXRlZEZldGNoIiwidXJsIiwib3B0aW9ucyIsInN0YXJ0c1dpdGgiLCJyZWZyZXNoQXV0aFRva2VuIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"1f6b6a191842\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZnJvbnRlbmQvLi9zcmMvYXBwL2dsb2JhbHMuY3NzPzc1YzEiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIxZjZiNmExOTE4NDJcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/dashboard/subscription/page.tsx":
/*!*************************************************!*\
  !*** ./src/app/dashboard/subscription/page.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\invoNest\frontend\src\app\dashboard\subscription\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata),\n/* harmony export */   viewport: () => (/* binding */ viewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-inter\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../contexts/AuthContext */ \"(rsc)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"(rsc)/./node_modules/react-hot-toast/dist/index.mjs\");\n\n\n\n\n\nconst metadata = {\n    title: \"InvoNest - AI-Powered Invoicing & Compliance Platform\",\n    description: \"Secure, AI-powered invoicing and compliance platform for Indian MSMEs, freelancers, and gig workers. GST-compliant invoice generation with blockchain integrity.\",\n    keywords: [\n        \"invoicing\",\n        \"GST\",\n        \"compliance\",\n        \"AI\",\n        \"MSME\",\n        \"India\",\n        \"tax\",\n        \"blockchain\"\n    ],\n    authors: [\n        {\n            name: \"InvoNest Team\"\n        }\n    ]\n};\nconst viewport = {\n    width: \"device-width\",\n    initialScale: 1,\n    maximumScale: 1,\n    userScalable: false,\n    viewportFit: \"cover\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.svg\",\n                        type: \"image/svg+xml\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\",\n                        sizes: \"32x32\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"manifest\",\n                        href: \"/manifest.json\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"theme-color\",\n                        content: \"#4f46e5\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-capable\",\n                        content: \"yes\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-status-bar-style\",\n                        content: \"default\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-title\",\n                        content: \"InvoNest\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        href: \"/icons/icon-192x192.svg\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"mobile-web-app-capable\",\n                        content: \"yes\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"application-name\",\n                        content: \"InvoNest\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"msapplication-TileColor\",\n                        content: \"#4f46e5\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"msapplication-TileImage\",\n                        content: \"/icons/icon-144x144.svg\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"format-detection\",\n                        content: \"telephone=no\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 9\n                    }, this),\n                     true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                                httpEquiv: \"Cache-Control\",\n                                content: \"no-cache, no-store, must-revalidate\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 50,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                                httpEquiv: \"Pragma\",\n                                content: \"no-cache\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 51,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                                httpEquiv: \"Expires\",\n                                content: \"0\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 52,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                                name: \"cache-bust\",\n                                content: Date.now().toString()\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                                src: \"/disable-turbopack-overlay.js\",\n                                defer: true\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().variable)} font-sans antialiased bg-gray-50 touch-manipulation`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"min-h-screen safe-area-inset\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.Toaster, {\n                            position: \"top-right\",\n                            toastOptions: {\n                                duration: 4000,\n                                style: {\n                                    background: \"#363636\",\n                                    color: \"#fff\"\n                                },\n                                success: {\n                                    duration: 3000,\n                                    iconTheme: {\n                                        primary: \"#4ade80\",\n                                        secondary: \"#fff\"\n                                    }\n                                },\n                                error: {\n                                    duration: 4000,\n                                    iconTheme: {\n                                        primary: \"#ef4444\",\n                                        secondary: \"#fff\"\n                                    }\n                                }\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 63,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 59,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ e0),
/* harmony export */   useAuth: () => (/* binding */ e1),
/* harmony export */   useRequireAuth: () => (/* binding */ e3),
/* harmony export */   withAuth: () => (/* binding */ e2)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\invoNest\frontend\src\contexts\AuthContext.tsx#AuthProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\invoNest\frontend\src\contexts\AuthContext.tsx#useAuth`);

const e2 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\invoNest\frontend\src\contexts\AuthContext.tsx#withAuth`);

const e3 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\invoNest\frontend\src\contexts\AuthContext.tsx#useRequireAuth`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/react-hot-toast","vendor-chunks/goober","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fsubscription%2Fpage&page=%2Fdashboard%2Fsubscription%2Fpage&appPaths=%2Fdashboard%2Fsubscription%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fsubscription%2Fpage.tsx&appDir=C%3A%5CUsers%5Cgupta%5CDesktop%5CinvoNest%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cgupta%5CDesktop%5CinvoNest%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();