"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getRevenueAnalytics = exports.checkUsageLimit = exports.syncUsage = exports.startFreeTrial = exports.getPaymentHistory = exports.cancelSubscription = exports.changeSubscription = exports.handlePaymentFailure = exports.verifyPayment = exports.createPaymentOrder = exports.getCurrentSubscription = exports.getPlans = void 0;
const subscriptionService_1 = __importDefault(require("../services/subscriptionService"));
const paymentService_1 = __importDefault(require("../services/paymentService"));
const Subscription_1 = __importDefault(require("../models/Subscription"));
// Get available plans
const getPlans = async (req, res) => {
    try {
        const plans = subscriptionService_1.default.getAvailablePlans();
        res.status(200).json({
            success: true,
            message: 'Plans retrieved successfully',
            data: { plans }
        });
    }
    catch (error) {
        console.error('Get plans error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to retrieve plans'
        });
    }
};
exports.getPlans = getPlans;
// Get user's current subscription
const getCurrentSubscription = async (req, res) => {
    try {
        const userId = req.user?._id;
        if (!userId) {
            res.status(401).json({
                success: false,
                message: 'User not authenticated'
            });
            return;
        }
        const subscription = await subscriptionService_1.default.getSubscriptionWithUsage(userId.toString());
        if (!subscription) {
            // Create free subscription if none exists
            const freeSubscription = await subscriptionService_1.default.createSubscription(userId.toString(), 'free', 'monthly', false);
            res.status(200).json({
                success: true,
                message: 'Free subscription created',
                data: { subscription: freeSubscription }
            });
            return;
        }
        res.status(200).json({
            success: true,
            message: 'Subscription retrieved successfully',
            data: { subscription }
        });
    }
    catch (error) {
        console.error('Get subscription error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to retrieve subscription'
        });
    }
};
exports.getCurrentSubscription = getCurrentSubscription;
// Create payment order for subscription
const createPaymentOrder = async (req, res) => {
    try {
        const userId = req.user?._id;
        if (!userId) {
            res.status(401).json({
                success: false,
                message: 'User not authenticated'
            });
            return;
        }
        const { planId, interval = 'monthly' } = req.body;
        if (!planId || !['professional', 'business'].includes(planId)) {
            res.status(400).json({
                success: false,
                message: 'Invalid plan ID'
            });
            return;
        }
        if (!['monthly', 'yearly'].includes(interval)) {
            res.status(400).json({
                success: false,
                message: 'Invalid interval'
            });
            return;
        }
        const orderData = await paymentService_1.default.createSubscriptionOrder(userId.toString(), planId, interval);
        res.status(200).json({
            success: true,
            message: 'Payment order created successfully',
            data: orderData
        });
    }
    catch (error) {
        console.error('Create payment order error:', error);
        res.status(500).json({
            success: false,
            message: error instanceof Error ? error.message : 'Failed to create payment order'
        });
    }
};
exports.createPaymentOrder = createPaymentOrder;
// Verify payment and activate subscription
const verifyPayment = async (req, res) => {
    try {
        const { razorpay_order_id, razorpay_payment_id, razorpay_signature } = req.body;
        if (!razorpay_order_id || !razorpay_payment_id || !razorpay_signature) {
            res.status(400).json({
                success: false,
                message: 'Missing payment verification data'
            });
            return;
        }
        const result = await paymentService_1.default.handleSuccessfulPayment(razorpay_order_id, razorpay_payment_id, razorpay_signature);
        res.status(200).json({
            success: true,
            message: 'Payment verified and subscription activated',
            data: result
        });
    }
    catch (error) {
        console.error('Verify payment error:', error);
        res.status(400).json({
            success: false,
            message: error instanceof Error ? error.message : 'Payment verification failed'
        });
    }
};
exports.verifyPayment = verifyPayment;
// Handle payment failure
const handlePaymentFailure = async (req, res) => {
    try {
        const { razorpay_order_id, error } = req.body;
        if (!razorpay_order_id) {
            res.status(400).json({
                success: false,
                message: 'Order ID is required'
            });
            return;
        }
        await paymentService_1.default.handleFailedPayment(razorpay_order_id, error?.description || 'Payment failed');
        res.status(200).json({
            success: true,
            message: 'Payment failure recorded'
        });
    }
    catch (error) {
        console.error('Handle payment failure error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to handle payment failure'
        });
    }
};
exports.handlePaymentFailure = handlePaymentFailure;
// Change subscription plan
const changeSubscription = async (req, res) => {
    try {
        const userId = req.user?._id;
        if (!userId) {
            res.status(401).json({
                success: false,
                message: 'User not authenticated'
            });
            return;
        }
        const { planId, interval = 'monthly' } = req.body;
        if (!planId || !['free', 'professional', 'business'].includes(planId)) {
            res.status(400).json({
                success: false,
                message: 'Invalid plan ID'
            });
            return;
        }
        const subscription = await subscriptionService_1.default.changeSubscription(userId.toString(), planId, interval);
        res.status(200).json({
            success: true,
            message: 'Subscription updated successfully',
            data: { subscription }
        });
    }
    catch (error) {
        console.error('Change subscription error:', error);
        res.status(400).json({
            success: false,
            message: error instanceof Error ? error.message : 'Failed to change subscription'
        });
    }
};
exports.changeSubscription = changeSubscription;
// Cancel subscription
const cancelSubscription = async (req, res) => {
    try {
        const userId = req.user?._id;
        if (!userId) {
            res.status(401).json({
                success: false,
                message: 'User not authenticated'
            });
            return;
        }
        const { immediate = false } = req.body;
        const subscription = await subscriptionService_1.default.cancelSubscription(userId.toString(), immediate);
        res.status(200).json({
            success: true,
            message: immediate ? 'Subscription cancelled immediately' : 'Subscription will be cancelled at period end',
            data: { subscription }
        });
    }
    catch (error) {
        console.error('Cancel subscription error:', error);
        res.status(400).json({
            success: false,
            message: error instanceof Error ? error.message : 'Failed to cancel subscription'
        });
    }
};
exports.cancelSubscription = cancelSubscription;
// Get payment history
const getPaymentHistory = async (req, res) => {
    try {
        const userId = req.user?._id;
        if (!userId) {
            res.status(401).json({
                success: false,
                message: 'User not authenticated'
            });
            return;
        }
        const { page = 1, limit = 10 } = req.query;
        const result = await paymentService_1.default.getPaymentHistory(userId.toString(), parseInt(page), parseInt(limit));
        res.status(200).json({
            success: true,
            message: 'Payment history retrieved successfully',
            data: result
        });
    }
    catch (error) {
        console.error('Get payment history error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to retrieve payment history'
        });
    }
};
exports.getPaymentHistory = getPaymentHistory;
// Start free trial
const startFreeTrial = async (req, res) => {
    try {
        const userId = req.user?._id;
        if (!userId) {
            res.status(401).json({
                success: false,
                message: 'User not authenticated'
            });
            return;
        }
        const { planId } = req.body;
        if (!planId || !['professional', 'business'].includes(planId)) {
            res.status(400).json({
                success: false,
                message: 'Invalid plan ID for trial'
            });
            return;
        }
        // Check if user already has a subscription
        const existingSubscription = await Subscription_1.default.findOne({ userId });
        if (existingSubscription && existingSubscription.trialEnd) {
            res.status(400).json({
                success: false,
                message: 'Trial already used'
            });
            return;
        }
        const subscription = await subscriptionService_1.default.createSubscription(userId.toString(), planId, 'monthly', true // Start trial
        );
        res.status(200).json({
            success: true,
            message: 'Free trial started successfully',
            data: { subscription }
        });
    }
    catch (error) {
        console.error('Start free trial error:', error);
        res.status(400).json({
            success: false,
            message: error instanceof Error ? error.message : 'Failed to start trial'
        });
    }
};
exports.startFreeTrial = startFreeTrial;
// Sync subscription usage with actual data
const syncUsage = async (req, res) => {
    try {
        const userId = req.user?._id;
        if (!userId) {
            res.status(401).json({
                success: false,
                message: 'User not authenticated'
            });
            return;
        }
        await subscriptionService_1.default.syncUsageWithActualData(userId.toString());
        res.status(200).json({
            success: true,
            message: 'Usage synced successfully'
        });
    }
    catch (error) {
        console.error('Sync usage error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
};
exports.syncUsage = syncUsage;
// Check usage limits (middleware helper)
const checkUsageLimit = async (req, res) => {
    try {
        const userId = req.user?._id;
        if (!userId) {
            res.status(401).json({
                success: false,
                message: 'User not authenticated'
            });
            return;
        }
        const { action, amount = 1 } = req.query;
        if (!action || !['invoice', 'storage'].includes(action)) {
            res.status(400).json({
                success: false,
                message: 'Invalid action type'
            });
            return;
        }
        const canPerform = await subscriptionService_1.default.checkUsageLimit(userId.toString(), action, parseInt(amount));
        res.status(200).json({
            success: true,
            data: { canPerform }
        });
    }
    catch (error) {
        console.error('Check usage limit error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to check usage limit'
        });
    }
};
exports.checkUsageLimit = checkUsageLimit;
// Admin: Get revenue analytics
const getRevenueAnalytics = async (req, res) => {
    try {
        // Check if user is admin (you can implement admin role check)
        const { startDate, endDate } = req.query;
        const analytics = await paymentService_1.default.getRevenueAnalytics(startDate ? new Date(startDate) : undefined, endDate ? new Date(endDate) : undefined);
        res.status(200).json({
            success: true,
            message: 'Revenue analytics retrieved successfully',
            data: analytics
        });
    }
    catch (error) {
        console.error('Get revenue analytics error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to retrieve revenue analytics'
        });
    }
};
exports.getRevenueAnalytics = getRevenueAnalytics;
//# sourceMappingURL=subscriptionController.js.map