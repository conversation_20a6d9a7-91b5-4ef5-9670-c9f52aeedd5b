export declare class PaymentService {
    /**
     * Create Razorpay order for subscription payment
     */
    static createSubscriptionOrder(userId: string, planId: string, interval?: 'monthly' | 'yearly'): Promise<{
        orderId: string;
        amount: string | number;
        currency: string;
        paymentId: unknown;
        key: string | undefined;
    }>;
    /**
     * Verify payment signature
     */
    static verifyPaymentSignature(razorpayOrderId: string, razorpayPaymentId: string, razorpaySignature: string): boolean;
    /**
     * Handle successful payment
     */
    static handleSuccessfulPayment(razorpayOrderId: string, razorpayPaymentId: string, razorpaySignature: string): Promise<{
        payment: import("mongoose").Document<unknown, {}, import("../models/Payment").IPayment, {}> & import("../models/Payment").IPayment & Required<{
            _id: unknown;
        }> & {
            __v: number;
        };
        subscription: (import("mongoose").Document<unknown, {}, import("../models/Subscription").ISubscription, {}> & import("../models/Subscription").ISubscription & Required<{
            _id: unknown;
        }> & {
            __v: number;
        }) | null;
    }>;
    /**
     * <PERSON>le failed payment
     */
    static handleFailedPayment(razorpayOrderId: string, failureReason: string): Promise<(import("mongoose").Document<unknown, {}, import("../models/Payment").IPayment, {}> & import("../models/Payment").IPayment & Required<{
        _id: unknown;
    }> & {
        __v: number;
    }) | null>;
    /**
     * Create refund
     */
    static createRefund(paymentId: string, amount?: number, reason?: string): Promise<import("razorpay/dist/types/refunds").Refunds.RazorpayRefund>;
    /**
     * Get payment history for user
     */
    static getPaymentHistory(userId: string, page?: number, limit?: number): Promise<{
        payments: (import("mongoose").FlattenMaps<import("../models/Payment").IPayment> & Required<{
            _id: import("mongoose").FlattenMaps<unknown>;
        }> & {
            __v: number;
        })[];
        pagination: {
            current: number;
            pages: number;
            total: number;
            limit: number;
        };
    }>;
    /**
     * Get revenue analytics
     */
    static getRevenueAnalytics(startDate?: Date, endDate?: Date): Promise<{
        overview: any;
        planBreakdown: any[];
    }>;
    /**
     * Helper method to get plan name
     */
    private static getPlanName;
}
export default PaymentService;
//# sourceMappingURL=paymentService.d.ts.map