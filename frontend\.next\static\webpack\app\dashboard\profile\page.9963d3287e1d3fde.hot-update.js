"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/profile/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/profile/page.tsx":
/*!********************************************!*\
  !*** ./src/app/dashboard/profile/page.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ProfilePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../lib/auth */ \"(app-pages-browser)/./src/lib/auth.ts\");\n/* harmony import */ var _components_dashboard_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../components/dashboard/DashboardLayout */ \"(app-pages-browser)/./src/components/dashboard/DashboardLayout.tsx\");\n/* harmony import */ var _components_FileUpload__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../components/FileUpload */ \"(app-pages-browser)/./src/components/FileUpload.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction ProfilePage() {\n    var _formData_address, _formData_address1, _formData_address2, _formData_address3, _formData_address4;\n    _s();\n    const { user, updateUser } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [editing, setEditing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [saving, setSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        type: \"\",\n        text: \"\"\n    });\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [verificationLoading, setVerificationLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [verificationMessage, setVerificationMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [logoUploading, setLogoUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [logoPreview, setLogoPreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [signatureUploading, setSignatureUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [signaturePreview, setSignaturePreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (user) {\n            setFormData({\n                ...user,\n                address: user.address || {\n                    street: \"\",\n                    city: \"\",\n                    state: \"\",\n                    pincode: \"\",\n                    country: \"India\"\n                }\n            });\n            // Set logo preview if user has a logo\n            if (user.logo) {\n                // Handle both full paths and just filenames\n                const logoPath = user.logo.includes(\"/\") ? user.logo : \"uploads/logos/\".concat(user.logo);\n                setLogoPreview(\"http://localhost:5000/\".concat(logoPath));\n            }\n            // Set signature preview if user has a signature\n            if (user.signature) {\n                // Handle both full paths and just filenames\n                const signaturePath = user.signature.includes(\"/\") ? user.signature : \"uploads/signatures/\".concat(user.signature);\n                setSignaturePreview(\"http://localhost:5000/\".concat(signaturePath));\n            }\n        }\n    }, [\n        user\n    ]);\n    const handleInputChange = (e)=>{\n        const { name, value } = e.target;\n        // Handle nested address fields\n        if ([\n            \"street\",\n            \"city\",\n            \"state\",\n            \"pincode\",\n            \"country\"\n        ].includes(name)) {\n            setFormData((prev)=>({\n                    ...prev,\n                    address: {\n                        ...prev.address,\n                        [name]: value\n                    }\n                }));\n        } else {\n            setFormData((prev)=>({\n                    ...prev,\n                    [name]: value\n                }));\n        }\n    };\n    const handleLogoUpload = async (files)=>{\n        if (files.length === 0) return;\n        const file = files[0];\n        setLogoUploading(true);\n        setMessage({\n            type: \"\",\n            text: \"\"\n        });\n        try {\n            const formData = new FormData();\n            formData.append(\"logo\", file);\n            const token = localStorage.getItem(\"token\");\n            const response = await fetch(\"http://localhost:5000/api/uploads/logo\", {\n                method: \"POST\",\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(token)\n                },\n                body: formData\n            });\n            const data = await response.json();\n            if (data.success) {\n                // Update form data with new logo path\n                setFormData((prev)=>({\n                        ...prev,\n                        logo: data.data.logoPath\n                    }));\n                // Set preview\n                setLogoPreview(\"http://localhost:5000/\".concat(data.data.logoPath));\n                // Update user context with new logo\n                if (user) {\n                    updateUser({\n                        ...user,\n                        logo: data.data.logoPath\n                    });\n                }\n                setMessage({\n                    type: \"success\",\n                    text: \"Logo uploaded successfully!\"\n                });\n            } else {\n                setMessage({\n                    type: \"error\",\n                    text: data.message || \"Failed to upload logo\"\n                });\n            }\n        } catch (error) {\n            console.error(\"Logo upload error:\", error);\n            setMessage({\n                type: \"error\",\n                text: \"Failed to upload logo\"\n            });\n        } finally{\n            setLogoUploading(false);\n        }\n    };\n    const handleRemoveLogo = async ()=>{\n        setLogoUploading(true);\n        try {\n            const token = localStorage.getItem(\"token\");\n            const response = await fetch(\"http://localhost:5000/api/uploads/logo\", {\n                method: \"DELETE\",\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(token)\n                }\n            });\n            const data = await response.json();\n            if (data.success) {\n                setFormData((prev)=>({\n                        ...prev,\n                        logo: null\n                    }));\n                setLogoPreview(null);\n                // Update user context to remove logo\n                if (user) {\n                    updateUser({\n                        ...user,\n                        logo: undefined\n                    });\n                }\n                setMessage({\n                    type: \"success\",\n                    text: \"Logo removed successfully!\"\n                });\n            } else {\n                setMessage({\n                    type: \"error\",\n                    text: data.message || \"Failed to remove logo\"\n                });\n            }\n        } catch (error) {\n            console.error(\"Logo remove error:\", error);\n            setMessage({\n                type: \"error\",\n                text: \"Failed to remove logo\"\n            });\n        } finally{\n            setLogoUploading(false);\n        }\n    };\n    const handleSignatureUpload = async (files)=>{\n        if (files.length === 0) return;\n        const file = files[0];\n        setSignatureUploading(true);\n        setMessage({\n            type: \"\",\n            text: \"\"\n        });\n        try {\n            const formData = new FormData();\n            formData.append(\"signature\", file);\n            const token = localStorage.getItem(\"token\");\n            const response = await fetch(\"http://localhost:5000/api/uploads/signature\", {\n                method: \"POST\",\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(token)\n                },\n                body: formData\n            });\n            const data = await response.json();\n            if (data.success) {\n                // Update form data with new signature path\n                setFormData((prev)=>({\n                        ...prev,\n                        signature: data.data.signaturePath\n                    }));\n                // Set preview\n                setSignaturePreview(\"http://localhost:5000/\".concat(data.data.signaturePath));\n                // Update user context with new signature\n                if (user) {\n                    updateUser({\n                        ...user,\n                        signature: data.data.signaturePath\n                    });\n                }\n                setMessage({\n                    type: \"success\",\n                    text: \"Signature uploaded successfully!\"\n                });\n            } else {\n                setMessage({\n                    type: \"error\",\n                    text: data.message || \"Failed to upload signature\"\n                });\n            }\n        } catch (error) {\n            console.error(\"Signature upload error:\", error);\n            setMessage({\n                type: \"error\",\n                text: \"Failed to upload signature\"\n            });\n        } finally{\n            setSignatureUploading(false);\n        }\n    };\n    const handleRemoveSignature = async ()=>{\n        setSignatureUploading(true);\n        try {\n            const token = localStorage.getItem(\"token\");\n            const response = await fetch(\"http://localhost:5000/api/uploads/signature\", {\n                method: \"DELETE\",\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(token)\n                }\n            });\n            const data = await response.json();\n            if (data.success) {\n                setFormData((prev)=>({\n                        ...prev,\n                        signature: null\n                    }));\n                setSignaturePreview(null);\n                // Update user context to remove signature\n                if (user) {\n                    updateUser({\n                        ...user,\n                        signature: undefined\n                    });\n                }\n                setMessage({\n                    type: \"success\",\n                    text: \"Signature removed successfully!\"\n                });\n            } else {\n                setMessage({\n                    type: \"error\",\n                    text: data.message || \"Failed to remove signature\"\n                });\n            }\n        } catch (error) {\n            console.error(\"Signature remove error:\", error);\n            setMessage({\n                type: \"error\",\n                text: \"Failed to remove signature\"\n            });\n        } finally{\n            setSignatureUploading(false);\n        }\n    };\n    const handleSave = async ()=>{\n        setSaving(true);\n        setMessage({\n            type: \"\",\n            text: \"\"\n        });\n        try {\n            var _formData_name, _formData_businessName, _formData_gstNumber, _formData_phone, _formData_address_street, _formData_address, _formData_address_city, _formData_address1, _formData_address_state, _formData_address2, _formData_address_pincode, _formData_address3, _formData_address_country, _formData_address4;\n            // Clean up the form data before sending\n            const cleanFormData = {};\n            // Only include non-empty fields\n            if ((_formData_name = formData.name) === null || _formData_name === void 0 ? void 0 : _formData_name.trim()) {\n                cleanFormData.name = formData.name.trim();\n            }\n            if ((_formData_businessName = formData.businessName) === null || _formData_businessName === void 0 ? void 0 : _formData_businessName.trim()) {\n                cleanFormData.businessName = formData.businessName.trim();\n            }\n            if ((_formData_gstNumber = formData.gstNumber) === null || _formData_gstNumber === void 0 ? void 0 : _formData_gstNumber.trim()) {\n                cleanFormData.gstNumber = formData.gstNumber.trim();\n            }\n            if ((_formData_phone = formData.phone) === null || _formData_phone === void 0 ? void 0 : _formData_phone.trim()) {\n                cleanFormData.phone = formData.phone.trim();\n            }\n            // Handle address separately\n            const address = {};\n            if ((_formData_address = formData.address) === null || _formData_address === void 0 ? void 0 : (_formData_address_street = _formData_address.street) === null || _formData_address_street === void 0 ? void 0 : _formData_address_street.trim()) {\n                address.street = formData.address.street.trim();\n            }\n            if ((_formData_address1 = formData.address) === null || _formData_address1 === void 0 ? void 0 : (_formData_address_city = _formData_address1.city) === null || _formData_address_city === void 0 ? void 0 : _formData_address_city.trim()) {\n                address.city = formData.address.city.trim();\n            }\n            if ((_formData_address2 = formData.address) === null || _formData_address2 === void 0 ? void 0 : (_formData_address_state = _formData_address2.state) === null || _formData_address_state === void 0 ? void 0 : _formData_address_state.trim()) {\n                address.state = formData.address.state.trim();\n            }\n            if ((_formData_address3 = formData.address) === null || _formData_address3 === void 0 ? void 0 : (_formData_address_pincode = _formData_address3.pincode) === null || _formData_address_pincode === void 0 ? void 0 : _formData_address_pincode.trim()) {\n                address.pincode = formData.address.pincode.trim();\n            }\n            if ((_formData_address4 = formData.address) === null || _formData_address4 === void 0 ? void 0 : (_formData_address_country = _formData_address4.country) === null || _formData_address_country === void 0 ? void 0 : _formData_address_country.trim()) {\n                address.country = formData.address.country.trim();\n            }\n            // Only include address if it has at least one field\n            if (Object.keys(address).length > 0) {\n                cleanFormData.address = address;\n            }\n            // Include logo if it exists\n            if (formData.logo) {\n                cleanFormData.logo = formData.logo;\n            }\n            console.log(\"Sending profile data:\", cleanFormData);\n            const response = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.updateProfile)(cleanFormData);\n            if (response.success && response.data) {\n                updateUser(response.data.user);\n                setEditing(false);\n                setMessage({\n                    type: \"success\",\n                    text: \"Profile updated successfully!\"\n                });\n            } else {\n                console.error(\"Profile update failed:\", response);\n                const errorMessage = response.errors ? response.errors.join(\", \") : response.message || \"Failed to update profile\";\n                setMessage({\n                    type: \"error\",\n                    text: errorMessage\n                });\n            }\n        } catch (error) {\n            console.error(\"Profile update error:\", error);\n            setMessage({\n                type: \"error\",\n                text: \"Failed to update profile\"\n            });\n        } finally{\n            setSaving(false);\n        }\n    };\n    const handleCancel = ()=>{\n        if (user) {\n            setFormData({\n                ...user,\n                address: user.address || {\n                    street: \"\",\n                    city: \"\",\n                    state: \"\",\n                    pincode: \"\",\n                    country: \"India\"\n                }\n            });\n        }\n        setEditing(false);\n        setMessage({\n            type: \"\",\n            text: \"\"\n        });\n    };\n    const sendVerificationEmail = async ()=>{\n        setVerificationLoading(true);\n        setVerificationMessage(\"\");\n        try {\n            const response = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.authenticatedFetch)(\"/api/auth/send-verification\", {\n                method: \"POST\"\n            });\n            const data = await response.json();\n            if (data.success) {\n                if (data.developmentToken) {\n                    // Development mode - show direct verification link\n                    const verificationUrl = \"\".concat(window.location.origin, \"/verify-email?token=\").concat(data.developmentToken);\n                    setVerificationMessage(\"development_link:\".concat(verificationUrl));\n                } else {\n                    setVerificationMessage(\"Verification email sent! Please check your inbox.\");\n                }\n            } else {\n                setVerificationMessage(data.message || \"Failed to send verification email.\");\n            }\n        } catch (error) {\n            console.error(\"Send verification error:\", error);\n            setVerificationMessage(\"Network error. Please try again.\");\n        } finally{\n            setVerificationLoading(false);\n        }\n    };\n    if (!user) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        title: \"Profile\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__.DashboardHeader, {\n                title: \"Profile\",\n                subtitle: \"Manage your account information and business details\",\n                actions: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [\n                        editing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleCancel,\n                            className: \"px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-800 bg-white hover:bg-gray-50 transition-colors\",\n                            disabled: saving,\n                            children: \"Cancel\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                            lineNumber: 341,\n                            columnNumber: 15\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>editing ? handleSave() : setEditing(true),\n                            className: \"px-4 py-2 bg-indigo-600 text-white rounded-lg text-sm font-medium hover:bg-indigo-700 transition-colors disabled:opacity-50\",\n                            disabled: saving,\n                            children: saving ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full mr-2 inline-block\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                        lineNumber: 356,\n                                        columnNumber: 19\n                                    }, void 0),\n                                    \"Saving...\"\n                                ]\n                            }, void 0, true) : editing ? \"Save Changes\" : \"Edit Profile\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                            lineNumber: 349,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                    lineNumber: 339,\n                    columnNumber: 11\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                lineNumber: 335,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-4xl mx-auto p-4 lg:p-8\",\n                children: [\n                    message.text && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6 p-4 rounded-lg \".concat(message.type === \"success\" ? \"bg-green-50 border border-green-200 text-green-800\" : \"bg-red-50 border border-red-200 text-red-800\"),\n                        children: message.text\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                        lineNumber: 368,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row items-center space-y-4 sm:space-y-0 sm:space-x-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-24 h-24 rounded-full overflow-hidden border-2 border-gray-200 bg-gradient-to-br from-indigo-500 to-purple-600 flex items-center justify-center\",\n                                    children: logoPreview ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: logoPreview,\n                                        alt: \"Business Logo\",\n                                        className: \"w-full h-full object-cover\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                        lineNumber: 382,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white text-3xl font-bold\",\n                                        children: user.name.charAt(0).toUpperCase()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                        lineNumber: 388,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                    lineNumber: 380,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center sm:text-left\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-2xl lg:text-3xl font-bold text-gray-900\",\n                                            children: user.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                            lineNumber: 394,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-800 mt-1\",\n                                            children: user.email\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                            lineNumber: 395,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col sm:flex-row items-center sm:items-start space-y-2 sm:space-y-0 sm:space-x-3 mt-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat(user.isEmailVerified ? \"bg-green-100 text-green-800\" : \"bg-yellow-100 text-yellow-800\"),\n                                                    children: user.isEmailVerified ? \"✓ Email Verified\" : \"⚠ Email Not Verified\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                    lineNumber: 397,\n                                                    columnNumber: 17\n                                                }, this),\n                                                !user.isEmailVerified && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: sendVerificationEmail,\n                                                    disabled: verificationLoading,\n                                                    className: \"inline-flex items-center px-3 py-1 text-xs font-medium text-indigo-600 bg-indigo-50 border border-indigo-200 rounded-full hover:bg-indigo-100 transition-colors disabled:opacity-50\",\n                                                    children: verificationLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"animate-spin h-3 w-3 border border-indigo-600 border-t-transparent rounded-full mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 408,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Sending...\"\n                                                        ]\n                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-3 h-3 mr-1\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                    lineNumber: 414,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 413,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Verify Email\"\n                                                        ]\n                                                    }, void 0, true)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                    lineNumber: 401,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                            lineNumber: 396,\n                                            columnNumber: 15\n                                        }, this),\n                                        verificationMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-2 p-2 rounded-md text-xs \".concat(verificationMessage.includes(\"sent\") || verificationMessage.includes(\"development_link\") ? \"bg-green-50 text-green-700 border border-green-200\" : \"bg-red-50 text-red-700 border border-red-200\"),\n                                            children: verificationMessage.startsWith(\"development_link:\") ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"mb-2\",\n                                                        children: \"\\uD83D\\uDCE7 Email service not configured (Development Mode)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 430,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: verificationMessage.replace(\"development_link:\", \"\"),\n                                                        className: \"inline-flex items-center px-3 py-1 bg-green-600 text-white text-xs rounded-md hover:bg-green-700 transition-colors\",\n                                                        target: \"_blank\",\n                                                        rel: \"noopener noreferrer\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-3 h-3 mr-1\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M5 13l4 4L19 7\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                    lineNumber: 438,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 437,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Click to Verify Email\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 431,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 429,\n                                                columnNumber: 21\n                                            }, this) : verificationMessage\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                            lineNumber: 423,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                    lineNumber: 393,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                            lineNumber: 379,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                        lineNumber: 378,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-xl shadow-sm border border-gray-200 p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold text-gray-900 mb-6\",\n                                children: \"Profile Information\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                lineNumber: 454,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-gray-900 border-b border-gray-200 pb-2\",\n                                                children: \"Personal Information\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 459,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-800 mb-2\",\n                                                        children: \"Full Name\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 464,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        name: \"name\",\n                                                        value: formData.name || \"\",\n                                                        onChange: handleInputChange,\n                                                        disabled: !editing,\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-gray-900 \".concat(!editing ? \"bg-gray-50\" : \"\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 465,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 463,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-800 mb-2\",\n                                                        children: \"Email Address\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 476,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"email\",\n                                                        name: \"email\",\n                                                        value: formData.email || \"\",\n                                                        onChange: handleInputChange,\n                                                        disabled: !editing,\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-gray-900 \".concat(!editing ? \"bg-gray-50\" : \"\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 477,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 475,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-800 mb-2\",\n                                                        children: \"Phone Number\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 488,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"tel\",\n                                                        name: \"phone\",\n                                                        value: formData.phone || \"\",\n                                                        onChange: handleInputChange,\n                                                        disabled: !editing,\n                                                        placeholder: \"Enter your phone number\",\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-gray-900 \".concat(!editing ? \"bg-gray-50\" : \"\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 489,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 487,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                        lineNumber: 458,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-gray-900 border-b border-gray-200 pb-2\",\n                                                children: \"Business Information\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 503,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-800 mb-2\",\n                                                        children: \"Business Name\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 508,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        name: \"businessName\",\n                                                        value: formData.businessName || \"\",\n                                                        onChange: handleInputChange,\n                                                        disabled: !editing,\n                                                        placeholder: \"Enter your business name\",\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-gray-900 \".concat(!editing ? \"bg-gray-50\" : \"\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 509,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 507,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-800 mb-2\",\n                                                        children: \"GST Number\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 521,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        name: \"gstNumber\",\n                                                        value: formData.gstNumber || \"\",\n                                                        onChange: handleInputChange,\n                                                        disabled: !editing,\n                                                        placeholder: \"Enter GST number (optional)\",\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-gray-900 \".concat(!editing ? \"bg-gray-50\" : \"\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 522,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 520,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-800 mb-2\",\n                                                        children: \"Business Logo\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 535,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    logoPreview ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-32 h-32 border-2 border-gray-200 rounded-lg overflow-hidden bg-gray-50 flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                    src: logoPreview,\n                                                                    alt: \"Business Logo\",\n                                                                    className: \"w-full h-full object-contain\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                    lineNumber: 539,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 538,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            editing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FileUpload__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                        onUpload: handleLogoUpload,\n                                                                        accept: \".jpg,.jpeg,.png,.gif,.svg,.webp\",\n                                                                        maxSize: 5,\n                                                                        maxFiles: 1,\n                                                                        className: \"flex-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                        lineNumber: 547,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: handleRemoveLogo,\n                                                                        disabled: logoUploading,\n                                                                        className: \"px-3 py-2 text-sm font-medium text-red-600 bg-red-50 border border-red-200 rounded-md hover:bg-red-100 transition-colors disabled:opacity-50\",\n                                                                        children: logoUploading ? \"Removing...\" : \"Remove\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                        lineNumber: 554,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 546,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 537,\n                                                        columnNumber: 19\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-32 h-32 border-2 border-dashed border-gray-300 rounded-lg bg-gray-50 flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-8 h-8 text-gray-400 mx-auto mb-2\",\n                                                                            fill: \"none\",\n                                                                            stroke: \"currentColor\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\",\n                                                                                strokeWidth: 2,\n                                                                                d: \"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                                lineNumber: 569,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                            lineNumber: 568,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-gray-500\",\n                                                                            children: \"No logo\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                            lineNumber: 571,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                    lineNumber: 567,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 566,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            editing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FileUpload__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                onUpload: handleLogoUpload,\n                                                                accept: \".jpg,.jpeg,.png,.gif,.svg,.webp\",\n                                                                maxSize: 5,\n                                                                maxFiles: 1,\n                                                                className: \"w-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 575,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 565,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    logoUploading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-2 flex items-center text-sm text-gray-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"animate-spin h-4 w-4 border-2 border-indigo-600 border-t-transparent rounded-full mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 587,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Uploading logo...\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 586,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 534,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-800 mb-2\",\n                                                        children: \"Digital Signature\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 595,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500 mb-3\",\n                                                        children: \"Upload your signature to automatically include it in invoices\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 596,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    signaturePreview ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-48 h-24 border-2 border-gray-200 rounded-lg overflow-hidden bg-gray-50 flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                    src: signaturePreview,\n                                                                    alt: \"Digital Signature\",\n                                                                    className: \"w-full h-full object-contain\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                    lineNumber: 600,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 599,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            editing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FileUpload__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                        onUpload: handleSignatureUpload,\n                                                                        accept: \".jpg,.jpeg,.png,.gif,.svg,.webp\",\n                                                                        maxSize: 5,\n                                                                        maxFiles: 1,\n                                                                        className: \"flex-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                        lineNumber: 608,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: handleRemoveSignature,\n                                                                        disabled: signatureUploading,\n                                                                        className: \"px-3 py-2 text-sm font-medium text-red-600 bg-red-50 border border-red-200 rounded-md hover:bg-red-100 transition-colors disabled:opacity-50\",\n                                                                        children: signatureUploading ? \"Removing...\" : \"Remove\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                        lineNumber: 615,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 607,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 598,\n                                                        columnNumber: 19\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-48 h-24 border-2 border-dashed border-gray-300 rounded-lg bg-gray-50 flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-6 h-6 text-gray-400 mx-auto mb-1\",\n                                                                            fill: \"none\",\n                                                                            stroke: \"currentColor\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\",\n                                                                                strokeWidth: 2,\n                                                                                d: \"M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                                lineNumber: 630,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                            lineNumber: 629,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-gray-500\",\n                                                                            children: \"No signature\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                            lineNumber: 632,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                    lineNumber: 628,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 627,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            editing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FileUpload__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                onUpload: handleSignatureUpload,\n                                                                accept: \".jpg,.jpeg,.png,.gif,.svg,.webp\",\n                                                                maxSize: 5,\n                                                                maxFiles: 1,\n                                                                className: \"w-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 636,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 626,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    signatureUploading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-2 flex items-center text-sm text-gray-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"animate-spin h-4 w-4 border-2 border-indigo-600 border-t-transparent rounded-full mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 648,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Uploading signature...\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 647,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 594,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                        lineNumber: 502,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                lineNumber: 456,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 border-b border-gray-200 pb-2 mb-4\",\n                                        children: \"Address Information\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                        lineNumber: 658,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"md:col-span-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-800 mb-2\",\n                                                        children: \"Street Address\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 664,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        name: \"street\",\n                                                        value: ((_formData_address = formData.address) === null || _formData_address === void 0 ? void 0 : _formData_address.street) || \"\",\n                                                        onChange: handleInputChange,\n                                                        disabled: !editing,\n                                                        placeholder: \"Enter your street address\",\n                                                        rows: 3,\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-gray-900 \".concat(!editing ? \"bg-gray-50\" : \"\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 665,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 663,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-800 mb-2\",\n                                                        children: \"City\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 677,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        name: \"city\",\n                                                        value: ((_formData_address1 = formData.address) === null || _formData_address1 === void 0 ? void 0 : _formData_address1.city) || \"\",\n                                                        onChange: handleInputChange,\n                                                        disabled: !editing,\n                                                        placeholder: \"Enter city\",\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-gray-900 \".concat(!editing ? \"bg-gray-50\" : \"\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 678,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 676,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-800 mb-2\",\n                                                        children: \"State\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 690,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        name: \"state\",\n                                                        value: ((_formData_address2 = formData.address) === null || _formData_address2 === void 0 ? void 0 : _formData_address2.state) || \"\",\n                                                        onChange: handleInputChange,\n                                                        disabled: !editing,\n                                                        placeholder: \"Enter state\",\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-gray-900 \".concat(!editing ? \"bg-gray-50\" : \"\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 691,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 689,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-800 mb-2\",\n                                                        children: \"PIN Code\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 703,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        name: \"pincode\",\n                                                        value: ((_formData_address3 = formData.address) === null || _formData_address3 === void 0 ? void 0 : _formData_address3.pincode) || \"\",\n                                                        onChange: handleInputChange,\n                                                        disabled: !editing,\n                                                        placeholder: \"Enter PIN code\",\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-gray-900 \".concat(!editing ? \"bg-gray-50\" : \"\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 704,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 702,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-800 mb-2\",\n                                                        children: \"Country\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 716,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        name: \"country\",\n                                                        value: ((_formData_address4 = formData.address) === null || _formData_address4 === void 0 ? void 0 : _formData_address4.country) || \"India\",\n                                                        onChange: handleInputChange,\n                                                        disabled: !editing,\n                                                        placeholder: \"Enter country\",\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-gray-900 \".concat(!editing ? \"bg-gray-50\" : \"\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 717,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 715,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                        lineNumber: 662,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                lineNumber: 657,\n                                columnNumber: 11\n                            }, this),\n                            editing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:hidden mt-6 flex space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleCancel,\n                                        className: \"flex-1 px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-800 bg-white hover:bg-gray-50 transition-colors\",\n                                        disabled: saving,\n                                        children: \"Cancel\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                        lineNumber: 733,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleSave,\n                                        className: \"flex-1 px-4 py-2 bg-indigo-600 text-white rounded-lg text-sm font-medium hover:bg-indigo-700 transition-colors disabled:opacity-50\",\n                                        disabled: saving,\n                                        children: saving ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full mr-2 inline-block\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                    lineNumber: 747,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Saving...\"\n                                            ]\n                                        }, void 0, true) : \"Save Changes\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                        lineNumber: 740,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                lineNumber: 732,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                        lineNumber: 453,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-xl shadow-sm border border-gray-200 p-6 mt-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900 mb-4\",\n                                children: \"Account Information\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                lineNumber: 758,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 sm:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center py-2 border-b border-gray-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-gray-800\",\n                                                children: \"Account Type\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 761,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-900 capitalize\",\n                                                children: user.role\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 762,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                        lineNumber: 760,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center py-2 border-b border-gray-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-gray-800\",\n                                                children: \"Member Since\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 765,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-900\",\n                                                children: user.createdAt ? new Date(user.createdAt).toLocaleDateString(\"en-IN\") : \"N/A\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 766,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                        lineNumber: 764,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center py-2 border-b border-gray-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-gray-800\",\n                                                children: \"Last Login\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 771,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-900\",\n                                                children: user.lastLogin ? new Date(user.lastLogin).toLocaleDateString(\"en-IN\") : \"N/A\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 772,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                        lineNumber: 770,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center py-2 border-b border-gray-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-gray-800\",\n                                                children: \"Email Status\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 777,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat(user.isEmailVerified ? \"bg-green-100 text-green-800\" : \"bg-yellow-100 text-yellow-800\"),\n                                                        children: user.isEmailVerified ? \"Verified\" : \"Not Verified\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 779,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    !user.isEmailVerified && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: sendVerificationEmail,\n                                                        disabled: verificationLoading,\n                                                        className: \"text-xs text-indigo-600 hover:text-indigo-800 font-medium disabled:opacity-50\",\n                                                        children: verificationLoading ? \"Sending...\" : \"Send Verification\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 783,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 778,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                        lineNumber: 776,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                lineNumber: 759,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                        lineNumber: 757,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                lineNumber: 365,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n        lineNumber: 334,\n        columnNumber: 5\n    }, this);\n}\n_s(ProfilePage, \"mLWr/FK64trBTG7PQjmr6/dSb6o=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = ProfilePage;\nvar _c;\n$RefreshReg$(_c, \"ProfilePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/profile/page.tsx\n"));

/***/ })

});