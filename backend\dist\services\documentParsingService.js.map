{"version": 3, "file": "documentParsingService.js", "sourceRoot": "", "sources": ["../../src/services/documentParsingService.ts"], "names": [], "mappings": ";;;;;;AAAA,2DAA6B;AAE7B,mDAA4C;AA4B5C,MAAa,sBAAsB;IAGjC;QACE,IAAI,CAAC,UAAU,GAAG,IAAI,2BAAU,EAAE,CAAC;IACrC,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,kBAAkB,CAAC,QAAgB;QAC/C,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,IAAI,OAAO,CAAM,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;gBACtD,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,QAAQ,EAAE,EAAE,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE;oBAClD,IAAI,GAAG;wBAAE,MAAM,CAAC,GAAG,CAAC,CAAC;;wBAChB,OAAO,CAAC,IAAI,CAAC,CAAC;gBACrB,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YAEH,IAAI,IAAI,GAAG,EAAE,CAAC;YACd,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;gBAC9B,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;oBACnC,IAAI,IAAI,OAAO,CAAC,GAAG,GAAG,GAAG,CAAC;gBAC5B,CAAC;gBACD,IAAI,IAAI,IAAI,CAAC;YACf,CAAC;YAED,OAAO,IAAI,CAAC,IAAI,EAAE,CAAC;QACrB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YACnD,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;QACrD,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,oBAAoB,CAAC,QAAgB;QACjD,kEAAkE;QAClE,4BAA4B;QAC5B,iBAAiB;QACjB,0BAA0B;QAC1B,qCAAqC;QAErC,OAAO,CAAC,GAAG,CAAC,oCAAoC,EAAE,QAAQ,CAAC,CAAC;QAC5D,OAAO,2CAA2C,CAAC;IACrD,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,uBAAuB,CAAC,QAAgB;QACpD,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,kBAAE,CAAC,QAAQ,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;YACrD,OAAO,OAAO,CAAC;QACjB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;YACjD,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;QAC9C,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,WAAW,CAAC,QAAgB,EAAE,QAAgB;QAC1D,QAAQ,QAAQ,EAAE,CAAC;YACjB,KAAK,iBAAiB;gBACpB,OAAO,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;YAE3C,KAAK,YAAY,CAAC;YAClB,KAAK,WAAW,CAAC;YACjB,KAAK,WAAW;gBACd,OAAO,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;YAE7C,KAAK,YAAY,CAAC;YAClB,KAAK,UAAU;gBACb,OAAO,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAAC,CAAC;YAEhD,KAAK,oBAAoB,CAAC;YAC1B,KAAK,yEAAyE,CAAC;YAC/E,KAAK,0BAA0B,CAAC;YAChC,KAAK,mEAAmE;gBACtE,gEAAgE;gBAChE,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;YAErD;gBACE,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAC;QACjE,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,oBAAoB,CAAC,IAAY;QAC7C,MAAM,QAAQ,GAA+D,EAAE,CAAC;QAChF,IAAI,YAAY,GAAoE,OAAO,CAAC;QAC5F,IAAI,UAAU,GAAG,GAAG,CAAC,CAAC,0CAA0C;QAEhE,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QAErC,0BAA0B;QAC1B,IAAI,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YAChG,YAAY,GAAG,SAAS,CAAC;YACzB,UAAU,IAAI,GAAG,CAAC;QACpB,CAAC;aAAM,IAAI,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,SAAS,CAAC,QAAQ,CAAC,kBAAkB,CAAC,EAAE,CAAC;YACnF,YAAY,GAAG,SAAS,CAAC;YACzB,UAAU,IAAI,GAAG,CAAC;QACpB,CAAC;aAAM,IAAI,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;YAC/F,YAAY,GAAG,cAAc,CAAC;YAC9B,UAAU,IAAI,GAAG,CAAC;QACpB,CAAC;QAED,0BAA0B;QAE1B,kBAAkB;QAClB,MAAM,eAAe,GAAG;YACtB,iDAAiD;YACjD,mCAAmC;SACpC,CAAC;QAEF,KAAK,MAAM,OAAO,IAAI,eAAe,EAAE,CAAC;YACtC,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YACpC,IAAI,OAAO,EAAE,CAAC;gBACZ,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;oBACtB,QAAQ,CAAC,IAAI,CAAC;wBACZ,IAAI,EAAE,gBAAgB;wBACtB,KAAK,EAAE,KAAK,CAAC,IAAI,EAAE;wBACnB,UAAU,EAAE,GAAG;qBAChB,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,4BAA4B;QAC5B,MAAM,cAAc,GAAG;YACrB,sBAAsB;YACtB,gDAAgD;YAChD,uCAAuC;SACxC,CAAC;QAEF,KAAK,MAAM,OAAO,IAAI,cAAc,EAAE,CAAC;YACrC,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YACpC,IAAI,OAAO,EAAE,CAAC;gBACZ,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;oBACtB,MAAM,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;oBAC7C,IAAI,MAAM,IAAI,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC;wBACtD,QAAQ,CAAC,IAAI,CAAC;4BACZ,IAAI,EAAE,QAAQ;4BACd,KAAK,EAAE,MAAM;4BACb,UAAU,EAAE,GAAG;yBAChB,CAAC,CAAC;oBACL,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,cAAc;QACd,MAAM,UAAU,GAAG,wDAAwD,CAAC;QAC5E,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;QAC1C,IAAI,UAAU,EAAE,CAAC;YACf,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;gBACvB,QAAQ,CAAC,IAAI,CAAC;oBACZ,IAAI,EAAE,YAAY;oBAClB,KAAK,EAAE,GAAG;oBACV,UAAU,EAAE,GAAG;iBAChB,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;QACL,CAAC;QAED,cAAc;QACd,MAAM,UAAU,GAAG,4BAA4B,CAAC;QAChD,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;QAC1C,IAAI,UAAU,EAAE,CAAC;YACf,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;gBACvB,QAAQ,CAAC,IAAI,CAAC;oBACZ,IAAI,EAAE,YAAY;oBAClB,KAAK,EAAE,GAAG;oBACV,UAAU,EAAE,GAAG;iBAChB,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;QACL,CAAC;QAED,QAAQ;QACR,MAAM,YAAY,GAAG;YACnB,sCAAsC;YACtC,qFAAqF;SACtF,CAAC;QAEF,KAAK,MAAM,OAAO,IAAI,YAAY,EAAE,CAAC;YACnC,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YACpC,IAAI,OAAO,EAAE,CAAC;gBACZ,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;oBACrB,QAAQ,CAAC,IAAI,CAAC;wBACZ,IAAI,EAAE,MAAM;wBACZ,KAAK,EAAE,IAAI;wBACX,UAAU,EAAE,GAAG;qBAChB,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,kBAAkB;QAClB,MAAM,YAAY,GAAG,sDAAsD,CAAC;QAC5E,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;QAC9C,IAAI,YAAY,EAAE,CAAC;YACjB,YAAY,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;gBAC3B,QAAQ,CAAC,IAAI,CAAC;oBACZ,IAAI,EAAE,OAAO;oBACb,KAAK,EAAE,KAAK;oBACZ,UAAU,EAAE,GAAG;iBAChB,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;QACL,CAAC;QAED,gCAAgC;QAChC,MAAM,YAAY,GAAG,+BAA+B,CAAC;QACrD,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;QAC9C,IAAI,YAAY,EAAE,CAAC;YACjB,YAAY,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;gBAC3B,QAAQ,CAAC,IAAI,CAAC;oBACZ,IAAI,EAAE,OAAO;oBACb,KAAK,EAAE,KAAK,CAAC,IAAI,EAAE;oBACnB,UAAU,EAAE,GAAG;iBAChB,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;QACL,CAAC;QAED,gCAAgC;QAChC,IAAI,WAAW,GAAQ,EAAE,CAAC;QAE1B,IAAI,YAAY,KAAK,SAAS,IAAI,YAAY,KAAK,SAAS,EAAE,CAAC;YAC7D,yBAAyB;YACzB,MAAM,aAAa,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,gBAAgB,CAAC,CAAC;YACtE,IAAI,aAAa,EAAE,CAAC;gBAClB,WAAW,CAAC,aAAa,GAAG,aAAa,CAAC,KAAK,CAAC;YAClD,CAAC;YAED,iBAAiB;YACjB,MAAM,YAAY,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC;YAC7D,IAAI,YAAY,EAAE,CAAC;gBACjB,MAAM,MAAM,GAAG,UAAU,CAAC,YAAY,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,CAAC;gBAClE,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC;oBACnB,WAAW,CAAC,MAAM,GAAG,MAAM,CAAC;gBAC9B,CAAC;YACH,CAAC;YAED,qBAAqB;YACrB,MAAM,SAAS,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,YAAY,CAAC,CAAC;YAC9D,IAAI,SAAS,EAAE,CAAC;gBACd,WAAW,CAAC,SAAS,GAAG,SAAS,CAAC,KAAK,CAAC;YAC1C,CAAC;YAED,eAAe;YACf,MAAM,UAAU,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC;YACzD,IAAI,UAAU,EAAE,CAAC;gBACf,IAAI,CAAC;oBACH,WAAW,CAAC,IAAI,GAAG,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;gBAChD,CAAC;gBAAC,OAAO,CAAC,EAAE,CAAC;oBACX,sBAAsB;gBACxB,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO;YACL,QAAQ;YACR,YAAY;YACZ,UAAU,EAAE,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC;YACrC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,MAAM,GAAG,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC;SAC5D,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa,CAAC,QAAgB,EAAE,QAAgB;QACpD,IAAI,CAAC;YACH,6BAA6B;YAC7B,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;YAEjE,IAAI,CAAC,aAAa,IAAI,aAAa,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACxD,OAAO;oBACL,IAAI,EAAE,EAAE;oBACR,QAAQ,EAAE,EAAE;oBACZ,UAAU,EAAE,CAAC;iBACd,CAAC;YACJ,CAAC;YAED,6CAA6C;YAC7C,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,aAAa,CAAC,CAAC;YAEhE,kBAAkB;YAClB,MAAM,MAAM,GAAuB;gBACjC,IAAI,EAAE,aAAa;gBACnB,QAAQ,EAAE,QAAQ,CAAC,QAAQ,IAAI,EAAE;gBACjC,YAAY,EAAE,QAAQ,CAAC,YAAY,IAAI,OAAO;gBAC9C,UAAU,EAAE,QAAQ,CAAC,UAAU,IAAI,CAAC;gBACpC,GAAG,QAAQ,CAAC,WAAW,IAAI,EAAE,WAAW,EAAE,QAAQ,CAAC,WAAW,EAAE;aACjE,CAAC;YAEF,OAAO,MAAM,CAAC;QAEhB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;YAChD,OAAO;gBACL,IAAI,EAAE,EAAE;gBACR,QAAQ,EAAE,EAAE;gBACZ,UAAU,EAAE,CAAC;aACd,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACH,mBAAmB,CAAC,WAAgB;QAKlC,MAAM,MAAM,GAAa,EAAE,CAAC;QAC5B,MAAM,QAAQ,GAAa,EAAE,CAAC;QAE9B,wBAAwB;QACxB,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,CAAC;YAC/B,MAAM,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;QAC3C,CAAC;QAED,IAAI,CAAC,WAAW,CAAC,MAAM,IAAI,WAAW,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;YACnD,MAAM,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;QACjD,CAAC;QAED,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC;YACxB,QAAQ,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;QACjD,CAAC;QAED,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC;YACtB,QAAQ,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;QAC3C,CAAC;QAED,gDAAgD;QAChD,IAAI,WAAW,CAAC,SAAS,EAAE,CAAC;YAC1B,MAAM,QAAQ,GAAG,2DAA2D,CAAC;YAC7E,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC1C,QAAQ,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;YACrD,CAAC;QACH,CAAC;QAED,OAAO;YACL,OAAO,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC;YAC5B,MAAM;YACN,QAAQ;SACT,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,qBAAqB;QACnB,OAAO;YACL,iBAAiB;YACjB,YAAY;YACZ,WAAW;YACX,WAAW;YACX,YAAY;YACZ,UAAU;SACX,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,mBAAmB,CAAC,QAAgB;QAClC,OAAO,IAAI,CAAC,qBAAqB,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;IACzD,CAAC;CACF;AAtXD,wDAsXC;AAED,4BAA4B;AACf,QAAA,sBAAsB,GAAG,IAAI,sBAAsB,EAAE,CAAC"}