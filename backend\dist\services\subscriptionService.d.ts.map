{"version": 3, "file": "subscriptionService.d.ts", "sourceRoot": "", "sources": ["../../src/services/subscriptionService.ts"], "names": [], "mappings": "AAAA,OAAqB,EAAE,aAAa,EAAE,MAAM,wBAAwB,CAAC;AAgDrE,qBAAa,mBAAmB;IAE9B;;OAEG;WACU,kBAAkB,CAC7B,MAAM,EAAE,MAAM,EACd,MAAM,EAAE,MAAM,EACd,QAAQ,GAAE,SAAS,GAAG,QAAoB,EAC1C,UAAU,GAAE,OAAe,GAC1B,OAAO,CAAC,aAAa,CAAC;IA8DzB;;OAEG;WACU,kBAAkB,CAC7B,MAAM,EAAE,MAAM,EACd,SAAS,EAAE,MAAM,EACjB,QAAQ,GAAE,SAAS,GAAG,QAAoB,GACzC,OAAO,CAAC,aAAa,CAAC;IA+BzB;;OAEG;WACU,kBAAkB,CAAC,MAAM,EAAE,MAAM,EAAE,SAAS,GAAE,OAAe,GAAG,OAAO,CAAC,aAAa,CAAC;IAkBnG;;OAEG;WACU,eAAe,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,SAAS,GAAG,SAAS,EAAE,MAAM,GAAE,MAAU,GAAG,OAAO,CAAC,OAAO,CAAC;IAgBjH;;OAEG;WACU,cAAc,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,UAAU,GAAG,SAAS,EAAE,MAAM,GAAE,MAAU,GAAG,OAAO,CAAC,IAAI,CAAC;IAO5G;;OAEG;WACU,uBAAuB,CAAC,MAAM,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAoBnE;;OAEG;WACU,wBAAwB,CAAC,MAAM,EAAE,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC;IAkBnE;;OAEG;WACU,iBAAiB,IAAI,OAAO,CAAC,IAAI,CAAC;IA6B/C;;OAEG;WACU,qBAAqB,IAAI,OAAO,CAAC,IAAI,CAAC;IAanD;;OAEG;IACH,OAAO,CAAC,MAAM,CAAC,WAAW;IAS1B;;OAEG;IACH,MAAM,CAAC,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA2BxB,MAAM,CAAC,oBAAoB,CAAC,YAAY,EAAE,GAAG,GAAG,OAAO;IAIvD,MAAM,CAAC,gBAAgB,CAAC,YAAY,EAAE,GAAG,GAAG,OAAO;WAItC,0BAA0B,CAAC,YAAY,EAAE,GAAG,EAAE,IAAI,EAAE,UAAU,GAAG,SAAS,EAAE,MAAM,GAAE,MAAU,GAAG,OAAO,CAAC,IAAI,CAAC;CAQ5H;AAED,eAAe,mBAAmB,CAAC"}