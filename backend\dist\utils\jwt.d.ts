import { IUser } from '../models/User';
export interface JWTPayload {
    userId: string;
    email: string;
    role: string;
    exp?: number;
    iat?: number;
}
export declare const generateToken: (user: IUser | any) => string;
export declare const verifyToken: (token: string) => JWTPayload;
export declare const generateRefreshToken: (user: IUser | any) => string;
//# sourceMappingURL=jwt.d.ts.map