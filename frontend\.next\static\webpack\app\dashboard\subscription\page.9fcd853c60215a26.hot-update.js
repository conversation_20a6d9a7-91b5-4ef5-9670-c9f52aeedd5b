"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/subscription/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/subscription/page.tsx":
/*!*************************************************!*\
  !*** ./src/app/dashboard/subscription/page.tsx ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SubscriptionPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../lib/auth */ \"(app-pages-browser)/./src/lib/auth.ts\");\n/* harmony import */ var _components_dashboard_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../components/dashboard/DashboardLayout */ \"(app-pages-browser)/./src/components/dashboard/DashboardLayout.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction SubscriptionPage() {\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [plans, setPlans] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentSubscription, setCurrentSubscription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [upgrading, setUpgrading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [billingInterval, setBillingInterval] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"monthly\");\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (user) {\n            fetchPlansAndSubscription();\n        }\n    }, [\n        user\n    ]);\n    const fetchPlansAndSubscription = async ()=>{\n        try {\n            // First sync usage to ensure accurate data\n            try {\n                await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.authenticatedFetch)(\"/api/subscriptions/usage/sync\", {\n                    method: \"POST\"\n                });\n            } catch (syncError) {\n                console.warn(\"Failed to sync usage:\", syncError);\n            // Continue even if sync fails\n            }\n            const [plansResponse, subscriptionResponse] = await Promise.all([\n                (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.authenticatedFetch)(\"/api/subscriptions/plans\"),\n                (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.authenticatedFetch)(\"/api/subscriptions/current\")\n            ]);\n            const plansData = await plansResponse.json();\n            const subscriptionData = await subscriptionResponse.json();\n            if (plansData.success) {\n                setPlans(plansData.data.plans);\n            }\n            if (subscriptionData.success) {\n                setCurrentSubscription(subscriptionData.data.subscription);\n            }\n        } catch (error) {\n            console.error(\"Failed to fetch subscription data:\", error);\n            setError(\"Failed to load subscription information\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleUpgrade = async (planId)=>{\n        if (planId === \"free\") {\n            // Handle downgrade to free\n            try {\n                setUpgrading(planId);\n                const response = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.authenticatedFetch)(\"/api/subscriptions/change\", {\n                    method: \"POST\",\n                    body: JSON.stringify({\n                        planId,\n                        interval: billingInterval\n                    })\n                });\n                const data = await response.json();\n                if (data.success) {\n                    await fetchPlansAndSubscription();\n                    setError(\"\");\n                } else {\n                    setError(data.message);\n                }\n            } catch (error) {\n                console.error(\"Failed to change subscription:\", error);\n                setError(\"Failed to change subscription\");\n            } finally{\n                setUpgrading(null);\n            }\n            return;\n        }\n        // Handle paid plan upgrade\n        try {\n            var _plans_find;\n            setUpgrading(planId);\n            // Create payment order\n            const orderResponse = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.authenticatedFetch)(\"/api/subscriptions/payment/create-order\", {\n                method: \"POST\",\n                body: JSON.stringify({\n                    planId,\n                    interval: billingInterval\n                })\n            });\n            const orderData = await orderResponse.json();\n            console.log(\"\\uD83D\\uDD0D Order response:\", orderData);\n            if (!orderData.success) {\n                throw new Error(orderData.message || \"Failed to create payment order\");\n            }\n            // Check if Razorpay key is available\n            if (!orderData.data.key) {\n                throw new Error(\"Payment gateway is not configured. Please contact support.\");\n            }\n            console.log(\"✅ Order data received, initializing Razorpay...\");\n            // Initialize Razorpay payment\n            const options = {\n                key: orderData.data.key,\n                amount: orderData.data.amount,\n                currency: orderData.data.currency,\n                name: \"InvoNest\",\n                description: \"Upgrade to \".concat((_plans_find = plans.find((p)=>p.id === planId)) === null || _plans_find === void 0 ? void 0 : _plans_find.name, \" Plan\"),\n                order_id: orderData.data.orderId,\n                handler: async (response)=>{\n                    try {\n                        // Verify payment\n                        const verifyResponse = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.authenticatedFetch)(\"/api/subscriptions/payment/verify\", {\n                            method: \"POST\",\n                            body: JSON.stringify({\n                                razorpay_order_id: response.razorpay_order_id,\n                                razorpay_payment_id: response.razorpay_payment_id,\n                                razorpay_signature: response.razorpay_signature\n                            })\n                        });\n                        const verifyData = await verifyResponse.json();\n                        if (verifyData.success) {\n                            await fetchPlansAndSubscription();\n                            setError(\"\");\n                            alert(\"Subscription upgraded successfully!\");\n                        } else {\n                            throw new Error(verifyData.message || \"Payment verification failed\");\n                        }\n                    } catch (error) {\n                        console.error(\"Payment verification failed:\", error);\n                        setError(error instanceof Error ? error.message : \"Payment verification failed\");\n                    } finally{\n                        setUpgrading(null);\n                    }\n                },\n                modal: {\n                    ondismiss: ()=>{\n                        setUpgrading(null);\n                    }\n                },\n                theme: {\n                    color: \"#4F46E5\"\n                }\n            };\n            // Load Razorpay script and open payment modal\n            const script = document.createElement(\"script\");\n            script.src = \"https://checkout.razorpay.com/v1/checkout.js\";\n            script.onload = ()=>{\n                try {\n                    const rzp = new window.Razorpay(options);\n                    rzp.open();\n                } catch (error) {\n                    console.error(\"Failed to initialize Razorpay:\", error);\n                    setError(\"Failed to initialize payment gateway\");\n                    setUpgrading(null);\n                }\n            };\n            script.onerror = ()=>{\n                setError(\"Failed to load payment gateway\");\n                setUpgrading(null);\n            };\n            document.head.appendChild(script);\n        } catch (error) {\n            console.error(\"Failed to upgrade subscription:\", error);\n            setError(error instanceof Error ? error.message : \"Failed to upgrade subscription\");\n            setUpgrading(null);\n        }\n    };\n    const formatPrice = (price)=>{\n        return new Intl.NumberFormat(\"en-IN\", {\n            style: \"currency\",\n            currency: \"INR\"\n        }).format(price / 100);\n    };\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString(\"en-IN\", {\n            year: \"numeric\",\n            month: \"long\",\n            day: \"numeric\"\n        });\n    };\n    const getUsageColor = (percentage)=>{\n        if (percentage >= 90) return \"bg-red-500\";\n        if (percentage >= 75) return \"bg-yellow-500\";\n        return \"bg-green-500\";\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center py-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"animate-spin h-8 w-8 text-indigo-600\",\n                            xmlns: \"http://www.w3.org/2000/svg\",\n                            fill: \"none\",\n                            viewBox: \"0 0 24 24\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                    className: \"opacity-25\",\n                                    cx: \"12\",\n                                    cy: \"12\",\n                                    r: \"10\",\n                                    stroke: \"currentColor\",\n                                    strokeWidth: \"4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                    lineNumber: 240,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    className: \"opacity-75\",\n                                    fill: \"currentColor\",\n                                    d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                    lineNumber: 241,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                            lineNumber: 239,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-lg text-gray-600\",\n                            children: \"Loading subscription...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                            lineNumber: 243,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                    lineNumber: 238,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                lineNumber: 237,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n            lineNumber: 236,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__.DashboardHeader, {\n                title: \"Subscription & Billing\",\n                subtitle: \"Manage your InvoNest subscription and view usage\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                lineNumber: 252,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto p-4 lg:p-8 space-y-8\",\n                children: [\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                        lineNumber: 260,\n                        columnNumber: 11\n                    }, this),\n                    currentSubscription && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-xl shadow-sm border border-gray-200 p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                children: \"Current Subscription\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                lineNumber: 268,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-gray-900\",\n                                                children: currentSubscription.planName\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                lineNumber: 271,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600\",\n                                                children: [\n                                                    formatPrice(currentSubscription.amount),\n                                                    \" / \",\n                                                    currentSubscription.interval\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                lineNumber: 272,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-500 mt-1\",\n                                                children: [\n                                                    currentSubscription.status === \"active\" ? \"Active\" : currentSubscription.status,\n                                                    currentSubscription.currentPeriodEnd && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            \" until \",\n                                                            formatDate(currentSubscription.currentPeriodEnd)\n                                                        ]\n                                                    }, void 0, true)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                lineNumber: 275,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"md:col-span-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-sm font-medium text-gray-900 mb-3\",\n                                                children: \"Usage This Period\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                lineNumber: 285,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between text-sm text-gray-900\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Invoices\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                        lineNumber: 289,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            currentSubscription.usage.invoicesUsed,\n                                                                            \" / \",\n                                                                            currentSubscription.features.maxInvoices === -1 ? \"∞\" : currentSubscription.features.maxInvoices\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                        lineNumber: 290,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 288,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            currentSubscription.features.maxInvoices !== -1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-full bg-gray-200 rounded-full h-2 mt-1\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-2 rounded-full \".concat(getUsageColor(currentSubscription.usagePercentages.invoices).split(\" \")[1]),\n                                                                    style: {\n                                                                        width: \"\".concat(Math.min(currentSubscription.usagePercentages.invoices, 100), \"%\")\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                    lineNumber: 296,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 295,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                        lineNumber: 287,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between text-sm text-gray-900\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Storage\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                        lineNumber: 306,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            currentSubscription.usage.storageUsed,\n                                                                            \"MB / \",\n                                                                            currentSubscription.features.maxStorage,\n                                                                            \"MB\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                        lineNumber: 307,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 305,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-full bg-gray-200 rounded-full h-2 mt-1\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-2 rounded-full \".concat(getUsageColor(currentSubscription.usagePercentages.storage).split(\" \")[1]),\n                                                                    style: {\n                                                                        width: \"\".concat(Math.min(currentSubscription.usagePercentages.storage, 100), \"%\")\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                    lineNumber: 312,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 311,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                        lineNumber: 304,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                lineNumber: 286,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                        lineNumber: 284,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                lineNumber: 269,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                        lineNumber: 267,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-100 rounded-lg p-1 flex\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setBillingInterval(\"monthly\"),\n                                    className: \"px-4 py-2 text-sm font-medium rounded-md transition-colors \".concat(billingInterval === \"monthly\" ? \"bg-white text-indigo-600 shadow-sm\" : \"text-gray-800 hover:text-gray-900\"),\n                                    children: \"Monthly\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                    lineNumber: 327,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setBillingInterval(\"yearly\"),\n                                    className: \"px-4 py-2 text-sm font-medium rounded-md transition-colors \".concat(billingInterval === \"yearly\" ? \"bg-white text-indigo-600 shadow-sm\" : \"text-gray-800 hover:text-gray-900\"),\n                                    children: [\n                                        \"Yearly\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-1 text-xs bg-green-100 text-green-800 px-1.5 py-0.5 rounded\",\n                                            children: \"Save 17%\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                            lineNumber: 346,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                    lineNumber: 337,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                            lineNumber: 326,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                        lineNumber: 325,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                        children: plans.map((plan)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative bg-white rounded-xl shadow-sm border-2 transition-all \".concat(plan.popular ? \"border-indigo-500 ring-2 ring-indigo-200\" : \"border-gray-200 hover:border-indigo-300\", \" \").concat((currentSubscription === null || currentSubscription === void 0 ? void 0 : currentSubscription.planId) === plan.id ? \"ring-2 ring-green-200 border-green-500\" : \"\"),\n                                children: [\n                                    plan.popular && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -top-3 left-1/2 transform -translate-x-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"bg-indigo-600 text-white px-3 py-1 text-xs font-medium rounded-full\",\n                                            children: \"Most Popular\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                            lineNumber: 366,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                        lineNumber: 365,\n                                        columnNumber: 17\n                                    }, this),\n                                    (currentSubscription === null || currentSubscription === void 0 ? void 0 : currentSubscription.planId) === plan.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -top-3 right-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"bg-green-600 text-white px-3 py-1 text-xs font-medium rounded-full\",\n                                            children: \"Current Plan\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                            lineNumber: 374,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                        lineNumber: 373,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-semibold text-gray-900\",\n                                                children: plan.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                lineNumber: 381,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 mt-2\",\n                                                children: plan.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                lineNumber: 382,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-3xl font-bold text-gray-900\",\n                                                        children: formatPrice(plan.pricing[billingInterval])\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                        lineNumber: 385,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    plan.pricing[billingInterval] > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600 ml-1\",\n                                                        children: [\n                                                            \"/\",\n                                                            billingInterval === \"monthly\" ? \"month\" : \"year\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                        lineNumber: 389,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                lineNumber: 384,\n                                                columnNumber: 17\n                                            }, this),\n                                            billingInterval === \"yearly\" && plan.pricing.yearly > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-green-600 mt-1\",\n                                                children: [\n                                                    \"Save ₹\",\n                                                    ((plan.pricing.monthly * 12 - plan.pricing.yearly) / 100).toLocaleString(\"en-IN\"),\n                                                    \" per year\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                lineNumber: 396,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"mt-6 space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-4 h-4 text-green-500 mr-3\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M5 13l4 4L19 7\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                    lineNumber: 404,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 403,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: [\n                                                                    plan.features.maxInvoices === -1 ? \"Unlimited\" : plan.features.maxInvoices,\n                                                                    \" invoices/month\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 406,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                        lineNumber: 402,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-4 h-4 text-green-500 mr-3\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M5 13l4 4L19 7\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                    lineNumber: 412,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 411,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: [\n                                                                    plan.features.maxStorage,\n                                                                    \"MB storage\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 414,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                        lineNumber: 410,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    plan.features.documentAnalysis && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-4 h-4 text-green-500 mr-3\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M5 13l4 4L19 7\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                    lineNumber: 421,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 420,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: \"Document analysis\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 423,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                        lineNumber: 419,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    plan.features.prioritySupport && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-4 h-4 text-green-500 mr-3\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M5 13l4 4L19 7\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                    lineNumber: 429,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 428,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: \"Priority support\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 431,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                        lineNumber: 427,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    plan.features.apiAccess && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-4 h-4 text-green-500 mr-3\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M5 13l4 4L19 7\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                    lineNumber: 437,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 436,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: \"API access\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 439,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                        lineNumber: 435,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    plan.features.multiUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-4 h-4 text-green-500 mr-3\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M5 13l4 4L19 7\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                    lineNumber: 445,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 444,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: [\n                                                                    \"Up to \",\n                                                                    plan.features.maxUsers,\n                                                                    \" users\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 447,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                        lineNumber: 443,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                lineNumber: 401,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleUpgrade(plan.id),\n                                                disabled: upgrading === plan.id || (currentSubscription === null || currentSubscription === void 0 ? void 0 : currentSubscription.planId) === plan.id,\n                                                className: \"w-full mt-6 px-4 py-2 rounded-lg text-sm font-medium transition-colors \".concat((currentSubscription === null || currentSubscription === void 0 ? void 0 : currentSubscription.planId) === plan.id ? \"bg-green-100 text-green-800 cursor-not-allowed\" : plan.popular ? \"bg-indigo-600 text-white hover:bg-indigo-700 disabled:opacity-50\" : \"bg-gray-100 text-gray-900 hover:bg-gray-200 disabled:opacity-50\"),\n                                                children: upgrading === plan.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"animate-spin -ml-1 mr-3 h-4 w-4\",\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            fill: \"none\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                    className: \"opacity-25\",\n                                                                    cx: \"12\",\n                                                                    cy: \"12\",\n                                                                    r: \"10\",\n                                                                    stroke: \"currentColor\",\n                                                                    strokeWidth: \"4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                    lineNumber: 468,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    className: \"opacity-75\",\n                                                                    fill: \"currentColor\",\n                                                                    d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                    lineNumber: 469,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                            lineNumber: 467,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"Processing...\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                    lineNumber: 466,\n                                                    columnNumber: 21\n                                                }, this) : (currentSubscription === null || currentSubscription === void 0 ? void 0 : currentSubscription.planId) === plan.id ? \"Current Plan\" : plan.id === \"free\" ? \"Downgrade to Free\" : \"Upgrade to \".concat(plan.name)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                lineNumber: 454,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                        lineNumber: 380,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, plan.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                lineNumber: 356,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                        lineNumber: 354,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-xl shadow-sm border border-gray-200 p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold text-gray-900 mb-6\",\n                                children: \"Feature Comparison\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                lineNumber: 488,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"overflow-x-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                    className: \"min-w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                className: \"border-b border-gray-200\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-left py-3 px-4 font-medium text-gray-900\",\n                                                        children: \"Feature\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                        lineNumber: 493,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    plans.map((plan)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-center py-3 px-4 font-medium text-gray-900\",\n                                                            children: plan.name\n                                                        }, plan.id, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                            lineNumber: 495,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                lineNumber: 492,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                            lineNumber: 491,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                            className: \"divide-y divide-gray-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4 text-sm text-gray-900\",\n                                                            children: \"Monthly Invoices\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                            lineNumber: 503,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        plans.map((plan)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"text-center py-3 px-4 text-sm text-gray-900\",\n                                                                children: plan.features.maxInvoices === -1 ? \"∞\" : plan.features.maxInvoices\n                                                            }, plan.id, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 505,\n                                                                columnNumber: 21\n                                                            }, this))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                    lineNumber: 502,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4 text-sm text-gray-900\",\n                                                            children: \"Storage\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                            lineNumber: 511,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        plans.map((plan)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"text-center py-3 px-4 text-sm text-gray-900\",\n                                                                children: [\n                                                                    plan.features.maxStorage,\n                                                                    \"MB\"\n                                                                ]\n                                                            }, plan.id, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 513,\n                                                                columnNumber: 21\n                                                            }, this))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                    lineNumber: 510,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4 text-sm text-gray-900\",\n                                                            children: \"Document Analysis\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                            lineNumber: 519,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        plans.map((plan)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"text-center py-3 px-4\",\n                                                                children: plan.features.documentAnalysis ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-5 h-5 text-green-500 mx-auto\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M5 13l4 4L19 7\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                        lineNumber: 524,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                    lineNumber: 523,\n                                                                    columnNumber: 25\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-5 h-5 text-gray-300 mx-auto\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M6 18L18 6M6 6l12 12\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                        lineNumber: 528,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                    lineNumber: 527,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, plan.id, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 521,\n                                                                columnNumber: 21\n                                                            }, this))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                    lineNumber: 518,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4 text-sm text-gray-900\",\n                                                            children: \"Priority Support\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                            lineNumber: 535,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        plans.map((plan)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"text-center py-3 px-4\",\n                                                                children: plan.features.prioritySupport ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-5 h-5 text-green-500 mx-auto\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M5 13l4 4L19 7\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                        lineNumber: 540,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                    lineNumber: 539,\n                                                                    columnNumber: 25\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-5 h-5 text-gray-300 mx-auto\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M6 18L18 6M6 6l12 12\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                        lineNumber: 544,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                    lineNumber: 543,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, plan.id, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 537,\n                                                                columnNumber: 21\n                                                            }, this))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                    lineNumber: 534,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4 text-sm text-gray-900\",\n                                                            children: \"API Access\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                            lineNumber: 551,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        plans.map((plan)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"text-center py-3 px-4\",\n                                                                children: plan.features.apiAccess ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-5 h-5 text-green-500 mx-auto\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M5 13l4 4L19 7\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                        lineNumber: 556,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                    lineNumber: 555,\n                                                                    columnNumber: 25\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-5 h-5 text-gray-300 mx-auto\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M6 18L18 6M6 6l12 12\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                        lineNumber: 560,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                    lineNumber: 559,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, plan.id, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 553,\n                                                                columnNumber: 21\n                                                            }, this))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                    lineNumber: 550,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                            lineNumber: 501,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                    lineNumber: 490,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                lineNumber: 489,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                        lineNumber: 487,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                lineNumber: 257,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n        lineNumber: 251,\n        columnNumber: 5\n    }, this);\n}\n_s(SubscriptionPage, \"LOYJOYyij6YvjGWMxRhvseJEImk=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = SubscriptionPage;\nvar _c;\n$RefreshReg$(_c, \"SubscriptionPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/subscription/page.tsx\n"));

/***/ })

});