"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/subscription/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/subscription/page.tsx":
/*!*************************************************!*\
  !*** ./src/app/dashboard/subscription/page.tsx ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SubscriptionPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../lib/auth */ \"(app-pages-browser)/./src/lib/auth.ts\");\n/* harmony import */ var _components_dashboard_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../components/dashboard/DashboardLayout */ \"(app-pages-browser)/./src/components/dashboard/DashboardLayout.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction SubscriptionPage() {\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [plans, setPlans] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentSubscription, setCurrentSubscription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [upgrading, setUpgrading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [billingInterval, setBillingInterval] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"monthly\");\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (user) {\n            fetchPlansAndSubscription();\n        }\n    }, [\n        user\n    ]);\n    const fetchPlansAndSubscription = async ()=>{\n        try {\n            // First sync usage to ensure accurate data\n            try {\n                await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.authenticatedFetch)(\"/api/subscriptions/usage/sync\", {\n                    method: \"POST\"\n                });\n            } catch (syncError) {\n                console.warn(\"Failed to sync usage:\", syncError);\n            // Continue even if sync fails\n            }\n            const [plansResponse, subscriptionResponse] = await Promise.all([\n                (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.authenticatedFetch)(\"/api/subscriptions/plans\"),\n                (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.authenticatedFetch)(\"/api/subscriptions/current\")\n            ]);\n            const plansData = await plansResponse.json();\n            const subscriptionData = await subscriptionResponse.json();\n            if (plansData.success) {\n                setPlans(plansData.data.plans);\n            }\n            if (subscriptionData.success) {\n                setCurrentSubscription(subscriptionData.data.subscription);\n            }\n        } catch (error) {\n            console.error(\"Failed to fetch subscription data:\", error);\n            setError(\"Failed to load subscription information\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleUpgrade = async (planId)=>{\n        if (planId === \"free\") {\n            // Handle downgrade to free\n            try {\n                setUpgrading(planId);\n                const response = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.authenticatedFetch)(\"/api/subscriptions/change\", {\n                    method: \"POST\",\n                    body: JSON.stringify({\n                        planId,\n                        interval: billingInterval\n                    })\n                });\n                const data = await response.json();\n                if (data.success) {\n                    await fetchPlansAndSubscription();\n                    setError(\"\");\n                } else {\n                    setError(data.message);\n                }\n            } catch (error) {\n                console.error(\"Failed to change subscription:\", error);\n                setError(\"Failed to change subscription\");\n            } finally{\n                setUpgrading(null);\n            }\n            return;\n        }\n        // Handle paid plan upgrade\n        try {\n            var _plans_find;\n            setUpgrading(planId);\n            // Create payment order\n            const orderResponse = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.authenticatedFetch)(\"/api/subscriptions/payment/create-order\", {\n                method: \"POST\",\n                body: JSON.stringify({\n                    planId,\n                    interval: billingInterval\n                })\n            });\n            const orderData = await orderResponse.json();\n            console.log(\"\\uD83D\\uDD0D Order response:\", orderData);\n            if (!orderData.success) {\n                throw new Error(orderData.message || \"Failed to create payment order\");\n            }\n            // Check if Razorpay key is available\n            if (!orderData.data.key) {\n                throw new Error(\"Payment gateway is not configured. Please contact support.\");\n            }\n            console.log(\"✅ Order data received, initializing Razorpay...\");\n            // Initialize Razorpay payment\n            const options = {\n                key: orderData.data.key,\n                amount: orderData.data.amount,\n                currency: orderData.data.currency,\n                name: \"InvoNest\",\n                description: \"Upgrade to \".concat((_plans_find = plans.find((p)=>p.id === planId)) === null || _plans_find === void 0 ? void 0 : _plans_find.name, \" Plan\"),\n                order_id: orderData.data.orderId,\n                handler: async (response)=>{\n                    try {\n                        // Verify payment\n                        const verifyResponse = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.authenticatedFetch)(\"/api/subscriptions/payment/verify\", {\n                            method: \"POST\",\n                            body: JSON.stringify({\n                                razorpay_order_id: response.razorpay_order_id,\n                                razorpay_payment_id: response.razorpay_payment_id,\n                                razorpay_signature: response.razorpay_signature\n                            })\n                        });\n                        const verifyData = await verifyResponse.json();\n                        if (verifyData.success) {\n                            await fetchPlansAndSubscription();\n                            setError(\"\");\n                            alert(\"Subscription upgraded successfully!\");\n                        } else {\n                            throw new Error(verifyData.message || \"Payment verification failed\");\n                        }\n                    } catch (error) {\n                        console.error(\"Payment verification failed:\", error);\n                        setError(error instanceof Error ? error.message : \"Payment verification failed\");\n                    } finally{\n                        setUpgrading(null);\n                    }\n                },\n                modal: {\n                    ondismiss: ()=>{\n                        setUpgrading(null);\n                    }\n                },\n                theme: {\n                    color: \"#4F46E5\"\n                }\n            };\n            // Load Razorpay script and open payment modal\n            console.log(\"\\uD83D\\uDD04 Loading Razorpay script...\");\n            // Check if Razorpay script is already loaded\n            if (window.Razorpay) {\n                console.log(\"✅ Razorpay already loaded, opening payment modal...\");\n                try {\n                    const rzp = new window.Razorpay(options);\n                    rzp.open();\n                } catch (error) {\n                    console.error(\"Failed to initialize Razorpay:\", error);\n                    setError(\"Failed to initialize payment gateway\");\n                    setUpgrading(null);\n                }\n                return;\n            }\n            const script = document.createElement(\"script\");\n            script.src = \"https://checkout.razorpay.com/v1/checkout.js\";\n            script.onload = ()=>{\n                console.log(\"✅ Razorpay script loaded successfully\");\n                try {\n                    const rzp = new window.Razorpay(options);\n                    console.log(\"✅ Razorpay instance created, opening modal...\");\n                    rzp.open();\n                } catch (error) {\n                    console.error(\"Failed to initialize Razorpay:\", error);\n                    setError(\"Failed to initialize payment gateway\");\n                    setUpgrading(null);\n                }\n            };\n            script.onerror = ()=>{\n                console.error(\"❌ Failed to load Razorpay script\");\n                setError(\"Failed to load payment gateway\");\n                setUpgrading(null);\n            };\n            document.head.appendChild(script);\n        } catch (error) {\n            console.error(\"Failed to upgrade subscription:\", error);\n            setError(error instanceof Error ? error.message : \"Failed to upgrade subscription\");\n            setUpgrading(null);\n        }\n    };\n    const formatPrice = (price)=>{\n        return new Intl.NumberFormat(\"en-IN\", {\n            style: \"currency\",\n            currency: \"INR\"\n        }).format(price / 100);\n    };\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString(\"en-IN\", {\n            year: \"numeric\",\n            month: \"long\",\n            day: \"numeric\"\n        });\n    };\n    const getUsageColor = (percentage)=>{\n        if (percentage >= 90) return \"bg-red-500\";\n        if (percentage >= 75) return \"bg-yellow-500\";\n        return \"bg-green-500\";\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center py-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"animate-spin h-8 w-8 text-indigo-600\",\n                            xmlns: \"http://www.w3.org/2000/svg\",\n                            fill: \"none\",\n                            viewBox: \"0 0 24 24\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                    className: \"opacity-25\",\n                                    cx: \"12\",\n                                    cy: \"12\",\n                                    r: \"10\",\n                                    stroke: \"currentColor\",\n                                    strokeWidth: \"4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                    lineNumber: 259,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    className: \"opacity-75\",\n                                    fill: \"currentColor\",\n                                    d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                            lineNumber: 258,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-lg text-gray-600\",\n                            children: \"Loading subscription...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                            lineNumber: 262,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                    lineNumber: 257,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                lineNumber: 256,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n            lineNumber: 255,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__.DashboardHeader, {\n                title: \"Subscription & Billing\",\n                subtitle: \"Manage your InvoNest subscription and view usage\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                lineNumber: 271,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto p-4 lg:p-8 space-y-8\",\n                children: [\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                        lineNumber: 279,\n                        columnNumber: 11\n                    }, this),\n                    currentSubscription && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-xl shadow-sm border border-gray-200 p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                children: \"Current Subscription\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                lineNumber: 287,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-gray-900\",\n                                                children: currentSubscription.planName\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                lineNumber: 290,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600\",\n                                                children: [\n                                                    formatPrice(currentSubscription.amount),\n                                                    \" / \",\n                                                    currentSubscription.interval\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                lineNumber: 291,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-500 mt-1\",\n                                                children: [\n                                                    currentSubscription.status === \"active\" ? \"Active\" : currentSubscription.status,\n                                                    currentSubscription.currentPeriodEnd && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            \" until \",\n                                                            formatDate(currentSubscription.currentPeriodEnd)\n                                                        ]\n                                                    }, void 0, true)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                lineNumber: 294,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                        lineNumber: 289,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"md:col-span-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-sm font-medium text-gray-900 mb-3\",\n                                                children: \"Usage This Period\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                lineNumber: 304,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between text-sm text-gray-900\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Invoices\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                        lineNumber: 308,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            currentSubscription.usage.invoicesUsed,\n                                                                            \" / \",\n                                                                            currentSubscription.features.maxInvoices === -1 ? \"∞\" : currentSubscription.features.maxInvoices\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                        lineNumber: 309,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 307,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            currentSubscription.features.maxInvoices !== -1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-full bg-gray-200 rounded-full h-2 mt-1\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-2 rounded-full \".concat(getUsageColor(currentSubscription.usagePercentages.invoices).split(\" \")[1]),\n                                                                    style: {\n                                                                        width: \"\".concat(Math.min(currentSubscription.usagePercentages.invoices, 100), \"%\")\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                    lineNumber: 315,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 314,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                        lineNumber: 306,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between text-sm text-gray-900\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Storage\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                        lineNumber: 325,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            currentSubscription.usage.storageUsed,\n                                                                            \"MB / \",\n                                                                            currentSubscription.features.maxStorage,\n                                                                            \"MB\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                        lineNumber: 326,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 324,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-full bg-gray-200 rounded-full h-2 mt-1\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-2 rounded-full \".concat(getUsageColor(currentSubscription.usagePercentages.storage).split(\" \")[1]),\n                                                                    style: {\n                                                                        width: \"\".concat(Math.min(currentSubscription.usagePercentages.storage, 100), \"%\")\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                    lineNumber: 331,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 330,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                        lineNumber: 323,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                lineNumber: 305,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                        lineNumber: 303,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                lineNumber: 288,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                        lineNumber: 286,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-100 rounded-lg p-1 flex\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setBillingInterval(\"monthly\"),\n                                    className: \"px-4 py-2 text-sm font-medium rounded-md transition-colors \".concat(billingInterval === \"monthly\" ? \"bg-white text-indigo-600 shadow-sm\" : \"text-gray-800 hover:text-gray-900\"),\n                                    children: \"Monthly\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                    lineNumber: 346,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setBillingInterval(\"yearly\"),\n                                    className: \"px-4 py-2 text-sm font-medium rounded-md transition-colors \".concat(billingInterval === \"yearly\" ? \"bg-white text-indigo-600 shadow-sm\" : \"text-gray-800 hover:text-gray-900\"),\n                                    children: [\n                                        \"Yearly\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-1 text-xs bg-green-100 text-green-800 px-1.5 py-0.5 rounded\",\n                                            children: \"Save 17%\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                            lineNumber: 365,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                    lineNumber: 356,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                            lineNumber: 345,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                        lineNumber: 344,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                        children: plans.map((plan)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative bg-white rounded-xl shadow-sm border-2 transition-all \".concat(plan.popular ? \"border-indigo-500 ring-2 ring-indigo-200\" : \"border-gray-200 hover:border-indigo-300\", \" \").concat((currentSubscription === null || currentSubscription === void 0 ? void 0 : currentSubscription.planId) === plan.id ? \"ring-2 ring-green-200 border-green-500\" : \"\"),\n                                children: [\n                                    plan.popular && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -top-3 left-1/2 transform -translate-x-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"bg-indigo-600 text-white px-3 py-1 text-xs font-medium rounded-full\",\n                                            children: \"Most Popular\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                            lineNumber: 385,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                        lineNumber: 384,\n                                        columnNumber: 17\n                                    }, this),\n                                    (currentSubscription === null || currentSubscription === void 0 ? void 0 : currentSubscription.planId) === plan.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -top-3 right-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"bg-green-600 text-white px-3 py-1 text-xs font-medium rounded-full\",\n                                            children: \"Current Plan\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                            lineNumber: 393,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                        lineNumber: 392,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-semibold text-gray-900\",\n                                                children: plan.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                lineNumber: 400,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 mt-2\",\n                                                children: plan.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                lineNumber: 401,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-3xl font-bold text-gray-900\",\n                                                        children: formatPrice(plan.pricing[billingInterval])\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                        lineNumber: 404,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    plan.pricing[billingInterval] > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-600 ml-1\",\n                                                        children: [\n                                                            \"/\",\n                                                            billingInterval === \"monthly\" ? \"month\" : \"year\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                        lineNumber: 408,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                lineNumber: 403,\n                                                columnNumber: 17\n                                            }, this),\n                                            billingInterval === \"yearly\" && plan.pricing.yearly > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-green-600 mt-1\",\n                                                children: [\n                                                    \"Save ₹\",\n                                                    ((plan.pricing.monthly * 12 - plan.pricing.yearly) / 100).toLocaleString(\"en-IN\"),\n                                                    \" per year\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                lineNumber: 415,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"mt-6 space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-4 h-4 text-green-500 mr-3\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M5 13l4 4L19 7\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                    lineNumber: 423,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 422,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: [\n                                                                    plan.features.maxInvoices === -1 ? \"Unlimited\" : plan.features.maxInvoices,\n                                                                    \" invoices/month\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 425,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                        lineNumber: 421,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-4 h-4 text-green-500 mr-3\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M5 13l4 4L19 7\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                    lineNumber: 431,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 430,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: [\n                                                                    plan.features.maxStorage,\n                                                                    \"MB storage\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 433,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                        lineNumber: 429,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    plan.features.documentAnalysis && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-4 h-4 text-green-500 mr-3\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M5 13l4 4L19 7\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                    lineNumber: 440,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 439,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: \"Document analysis\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 442,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                        lineNumber: 438,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    plan.features.prioritySupport && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-4 h-4 text-green-500 mr-3\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M5 13l4 4L19 7\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                    lineNumber: 448,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 447,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: \"Priority support\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 450,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                        lineNumber: 446,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    plan.features.apiAccess && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-4 h-4 text-green-500 mr-3\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M5 13l4 4L19 7\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                    lineNumber: 456,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 455,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: \"API access\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 458,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                        lineNumber: 454,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    plan.features.multiUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-4 h-4 text-green-500 mr-3\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M5 13l4 4L19 7\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                    lineNumber: 464,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 463,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: [\n                                                                    \"Up to \",\n                                                                    plan.features.maxUsers,\n                                                                    \" users\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 466,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                        lineNumber: 462,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                lineNumber: 420,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleUpgrade(plan.id),\n                                                disabled: upgrading === plan.id || (currentSubscription === null || currentSubscription === void 0 ? void 0 : currentSubscription.planId) === plan.id,\n                                                className: \"w-full mt-6 px-4 py-2 rounded-lg text-sm font-medium transition-colors \".concat((currentSubscription === null || currentSubscription === void 0 ? void 0 : currentSubscription.planId) === plan.id ? \"bg-green-100 text-green-800 cursor-not-allowed\" : plan.popular ? \"bg-indigo-600 text-white hover:bg-indigo-700 disabled:opacity-50\" : \"bg-gray-100 text-gray-900 hover:bg-gray-200 disabled:opacity-50\"),\n                                                children: upgrading === plan.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"animate-spin -ml-1 mr-3 h-4 w-4\",\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            fill: \"none\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                    className: \"opacity-25\",\n                                                                    cx: \"12\",\n                                                                    cy: \"12\",\n                                                                    r: \"10\",\n                                                                    stroke: \"currentColor\",\n                                                                    strokeWidth: \"4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                    lineNumber: 487,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    className: \"opacity-75\",\n                                                                    fill: \"currentColor\",\n                                                                    d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                    lineNumber: 488,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                            lineNumber: 486,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"Processing...\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                    lineNumber: 485,\n                                                    columnNumber: 21\n                                                }, this) : (currentSubscription === null || currentSubscription === void 0 ? void 0 : currentSubscription.planId) === plan.id ? \"Current Plan\" : plan.id === \"free\" ? \"Downgrade to Free\" : \"Upgrade to \".concat(plan.name)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                lineNumber: 473,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                        lineNumber: 399,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, plan.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                lineNumber: 375,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                        lineNumber: 373,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-xl shadow-sm border border-gray-200 p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold text-gray-900 mb-6\",\n                                children: \"Feature Comparison\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                lineNumber: 507,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"overflow-x-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                    className: \"min-w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                className: \"border-b border-gray-200\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-left py-3 px-4 font-medium text-gray-900\",\n                                                        children: \"Feature\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                        lineNumber: 512,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    plans.map((plan)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-center py-3 px-4 font-medium text-gray-900\",\n                                                            children: plan.name\n                                                        }, plan.id, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                            lineNumber: 514,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                lineNumber: 511,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                            lineNumber: 510,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                            className: \"divide-y divide-gray-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4 text-sm text-gray-900\",\n                                                            children: \"Monthly Invoices\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                            lineNumber: 522,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        plans.map((plan)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"text-center py-3 px-4 text-sm text-gray-900\",\n                                                                children: plan.features.maxInvoices === -1 ? \"∞\" : plan.features.maxInvoices\n                                                            }, plan.id, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 524,\n                                                                columnNumber: 21\n                                                            }, this))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                    lineNumber: 521,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4 text-sm text-gray-900\",\n                                                            children: \"Storage\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                            lineNumber: 530,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        plans.map((plan)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"text-center py-3 px-4 text-sm text-gray-900\",\n                                                                children: [\n                                                                    plan.features.maxStorage,\n                                                                    \"MB\"\n                                                                ]\n                                                            }, plan.id, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 532,\n                                                                columnNumber: 21\n                                                            }, this))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                    lineNumber: 529,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4 text-sm text-gray-900\",\n                                                            children: \"Document Analysis\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                            lineNumber: 538,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        plans.map((plan)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"text-center py-3 px-4\",\n                                                                children: plan.features.documentAnalysis ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-5 h-5 text-green-500 mx-auto\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M5 13l4 4L19 7\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                        lineNumber: 543,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                    lineNumber: 542,\n                                                                    columnNumber: 25\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-5 h-5 text-gray-300 mx-auto\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M6 18L18 6M6 6l12 12\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                        lineNumber: 547,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                    lineNumber: 546,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, plan.id, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 540,\n                                                                columnNumber: 21\n                                                            }, this))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                    lineNumber: 537,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4 text-sm text-gray-900\",\n                                                            children: \"Priority Support\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                            lineNumber: 554,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        plans.map((plan)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"text-center py-3 px-4\",\n                                                                children: plan.features.prioritySupport ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-5 h-5 text-green-500 mx-auto\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M5 13l4 4L19 7\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                        lineNumber: 559,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                    lineNumber: 558,\n                                                                    columnNumber: 25\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-5 h-5 text-gray-300 mx-auto\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M6 18L18 6M6 6l12 12\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                        lineNumber: 563,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                    lineNumber: 562,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, plan.id, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 556,\n                                                                columnNumber: 21\n                                                            }, this))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                    lineNumber: 553,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-4 text-sm text-gray-900\",\n                                                            children: \"API Access\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                            lineNumber: 570,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        plans.map((plan)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"text-center py-3 px-4\",\n                                                                children: plan.features.apiAccess ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-5 h-5 text-green-500 mx-auto\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M5 13l4 4L19 7\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                        lineNumber: 575,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                    lineNumber: 574,\n                                                                    columnNumber: 25\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-5 h-5 text-gray-300 mx-auto\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M6 18L18 6M6 6l12 12\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                        lineNumber: 579,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                    lineNumber: 578,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, plan.id, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                                lineNumber: 572,\n                                                                columnNumber: 21\n                                                            }, this))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                                    lineNumber: 569,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                            lineNumber: 520,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                    lineNumber: 509,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                                lineNumber: 508,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                        lineNumber: 506,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n                lineNumber: 276,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\subscription\\\\page.tsx\",\n        lineNumber: 270,\n        columnNumber: 5\n    }, this);\n}\n_s(SubscriptionPage, \"LOYJOYyij6YvjGWMxRhvseJEImk=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = SubscriptionPage;\nvar _c;\n$RefreshReg$(_c, \"SubscriptionPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/subscription/page.tsx\n"));

/***/ })

});