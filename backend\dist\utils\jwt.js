"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.generateRefreshToken = exports.verifyToken = exports.generateToken = void 0;
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const generateToken = (user) => {
    const payload = {
        userId: user._id ? user._id.toString() : user.id,
        email: user.email,
        role: user.role
    };
    const secret = process.env.JWT_SECRET;
    if (!secret) {
        throw new Error('JWT_SECRET is not defined');
    }
    // Use number for expiresIn to avoid TypeScript issues
    return jsonwebtoken_1.default.sign(payload, secret, { expiresIn: 604800 }); // 7 days in seconds
};
exports.generateToken = generateToken;
const verifyToken = (token) => {
    try {
        const secret = process.env.JWT_SECRET;
        if (!secret) {
            throw new Error('JWT_SECRET is not defined');
        }
        return jsonwebtoken_1.default.verify(token, secret);
    }
    catch (error) {
        throw new Error('Invalid or expired token');
    }
};
exports.verifyToken = verifyToken;
const generateRefreshToken = (user) => {
    const payload = {
        userId: user._id ? user._id.toString() : user.id,
        type: 'refresh'
    };
    const secret = process.env.JWT_SECRET;
    if (!secret) {
        throw new Error('JWT_SECRET is not defined');
    }
    // Use number for expiresIn - 30 days in seconds
    return jsonwebtoken_1.default.sign(payload, secret, { expiresIn: 2592000 });
};
exports.generateRefreshToken = generateRefreshToken;
//# sourceMappingURL=jwt.js.map