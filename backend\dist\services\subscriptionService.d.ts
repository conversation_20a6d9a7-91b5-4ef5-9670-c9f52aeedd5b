import { ISubscription } from '../models/Subscription';
export declare class SubscriptionService {
    /**
     * Create a new subscription for a user
     */
    static createSubscription(userId: string, planId: string, interval?: 'monthly' | 'yearly', startTrial?: boolean): Promise<ISubscription>;
    /**
     * Upgrade/downgrade subscription
     */
    static changeSubscription(userId: string, newPlanId: string, interval?: 'monthly' | 'yearly'): Promise<ISubscription>;
    /**
     * Cancel subscription
     */
    static cancelSubscription(userId: string, immediate?: boolean): Promise<ISubscription>;
    /**
     * Check if user can perform an action
     */
    static checkUsageLimit(userId: string, action: 'invoice' | 'storage', amount?: number): Promise<boolean>;
    /**
     * Increment usage
     */
    static incrementUsage(userId: string, type: 'invoices' | 'storage', amount?: number): Promise<void>;
    /**
     * Sync subscription usage with actual data
     */
    static syncUsageWithActualData(userId: string): Promise<void>;
    /**
     * Get subscription with usage percentage
     */
    static getSubscriptionWithUsage(userId: string): Promise<any>;
    /**
     * Reset monthly usage for all subscriptions
     */
    static resetMonthlyUsage(): Promise<void>;
    /**
     * Handle trial expiration
     */
    static handleTrialExpiration(): Promise<void>;
    /**
     * Get plan name
     */
    private static getPlanName;
    /**
     * Get available plans
     */
    static getAvailablePlans(): {
        id: string;
        name: string;
        description: string;
        features: {
            maxInvoices: number;
            maxStorage: number;
            documentAnalysis: boolean;
            prioritySupport: boolean;
            apiAccess: boolean;
            customBranding: boolean;
            multiUser: boolean;
            maxUsers: number;
            advancedReports: boolean;
        } | {
            maxInvoices: number;
            maxStorage: number;
            documentAnalysis: boolean;
            prioritySupport: boolean;
            apiAccess: boolean;
            customBranding: boolean;
            multiUser: boolean;
            maxUsers: number;
            advancedReports: boolean;
        } | {
            maxInvoices: number;
            maxStorage: number;
            documentAnalysis: boolean;
            prioritySupport: boolean;
            apiAccess: boolean;
            customBranding: boolean;
            multiUser: boolean;
            maxUsers: number;
            advancedReports: boolean;
        };
        pricing: {
            monthly: number;
            yearly: number;
        } | {
            monthly: number;
            yearly: number;
        } | {
            monthly: number;
            yearly: number;
        };
        popular: boolean;
    }[];
    static isSubscriptionActive(subscription: any): boolean;
    static canCreateInvoice(subscription: any): boolean;
    static incrementSubscriptionUsage(subscription: any, type: 'invoices' | 'storage', amount?: number): Promise<void>;
}
export default SubscriptionService;
//# sourceMappingURL=subscriptionService.d.ts.map