"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getUPIProviders = exports.validateUPI = exports.verifyEmail = exports.sendEmailVerification = exports.logout = exports.updateProfile = exports.getProfile = exports.login = exports.register = void 0;
const crypto_1 = __importDefault(require("crypto"));
const User_1 = __importDefault(require("../models/User"));
const jwt_1 = require("../utils/jwt");
const Compliance_1 = require("../models/Compliance");
const Notification_1 = require("../models/Notification");
const emailService_1 = __importDefault(require("../services/emailService"));
const upiValidationService_1 = __importDefault(require("../services/upiValidationService"));
const adminAuth_1 = require("../utils/adminAuth");
// Register new user
const register = async (req, res) => {
    try {
        const { name, email, password, businessName, gstNumber, phone, address } = req.body;
        // Check if user already exists
        const existingUser = await User_1.default.findOne({ email });
        if (existingUser) {
            res.status(400).json({
                success: false,
                message: 'User already exists with this email'
            });
            return;
        }
        // Check if GST number already exists (if provided)
        if (gstNumber) {
            const existingGST = await User_1.default.findOne({ gstNumber });
            if (existingGST) {
                res.status(400).json({
                    success: false,
                    message: 'GST number already registered'
                });
                return;
            }
        }
        // Create new user
        const user = new User_1.default({
            name,
            email,
            password,
            businessName,
            gstNumber,
            phone,
            address
        });
        await user.save();
        // Generate email verification token
        const verificationToken = crypto_1.default.randomBytes(32).toString('hex');
        const verificationExpires = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours
        // Update user with verification token
        user.emailVerificationToken = verificationToken;
        user.emailVerificationExpires = verificationExpires;
        await user.save();
        // Send verification email (don't block registration if email fails)
        try {
            const emailService = (0, emailService_1.default)();
            await emailService.sendEmailVerificationEmail(user.email, verificationToken, user.name);
        }
        catch (error) {
            console.error('Failed to send verification email during registration:', error);
        }
        // Initialize notification preferences only (users can add compliance items manually)
        await initializeNotificationPreferences(user._id.toString());
        // Note: Compliance items are not auto-seeded to avoid fake data
        console.log(`✅ User registered successfully: ${user.email}`);
        // Create free subscription for new user
        try {
            const SubscriptionService = require('../services/subscriptionService').default;
            await SubscriptionService.createSubscription(user._id.toString(), 'free', 'monthly', false);
            console.log(`Created free subscription for user ${user._id}`);
        }
        catch (error) {
            console.error('Error creating free subscription during registration:', error);
        }
        // Generate tokens
        const token = (0, jwt_1.generateToken)(user);
        const refreshToken = (0, jwt_1.generateRefreshToken)(user);
        // Update last login
        user.lastLogin = new Date();
        await user.save();
        res.status(201).json({
            success: true,
            message: 'User registered successfully',
            data: {
                user: {
                    id: user._id,
                    name: user.name,
                    email: user.email,
                    role: user.role,
                    businessName: user.businessName,
                    gstNumber: user.gstNumber,
                    phone: user.phone,
                    address: user.address,
                    isEmailVerified: user.isEmailVerified,
                    createdAt: user.createdAt
                },
                token,
                refreshToken
            }
        });
    }
    catch (error) {
        console.error('Registration error:', error);
        if (error.name === 'ValidationError') {
            const errors = Object.values(error.errors).map((err) => err.message);
            res.status(400).json({
                success: false,
                message: 'Validation error',
                errors
            });
            return;
        }
        res.status(500).json({
            success: false,
            message: 'Internal server error during registration'
        });
    }
};
exports.register = register;
// Login user
const login = async (req, res) => {
    try {
        const { email, password } = req.body;
        // Validate input
        if (!email || !password) {
            res.status(400).json({
                success: false,
                message: 'Email and password are required'
            });
            return;
        }
        // Check admin credentials first
        const isAdminLogin = await (0, adminAuth_1.validateAdminCredentials)(email, password);
        if (isAdminLogin) {
            // Handle admin login
            const adminUser = (0, adminAuth_1.createAdminUserObject)();
            // Generate tokens for admin
            const token = (0, jwt_1.generateToken)(adminUser);
            const refreshToken = (0, jwt_1.generateRefreshToken)(adminUser);
            res.status(200).json({
                success: true,
                message: 'Admin login successful',
                data: {
                    user: adminUser,
                    token,
                    refreshToken
                }
            });
            return;
        }
        // Find regular user and include password for comparison
        const user = await User_1.default.findOne({ email }).select('+password');
        if (!user) {
            res.status(401).json({
                success: false,
                message: 'Invalid email or password'
            });
            return;
        }
        // Check if account is active
        if (!user.isActive) {
            res.status(401).json({
                success: false,
                message: 'Account is deactivated. Please contact support.'
            });
            return;
        }
        // Compare password
        const isPasswordValid = await user.comparePassword(password);
        if (!isPasswordValid) {
            res.status(401).json({
                success: false,
                message: 'Invalid email or password'
            });
            return;
        }
        // Generate tokens
        const token = (0, jwt_1.generateToken)(user);
        const refreshToken = (0, jwt_1.generateRefreshToken)(user);
        // Update last login
        user.lastLogin = new Date();
        await user.save();
        res.status(200).json({
            success: true,
            message: 'Login successful',
            data: {
                user: {
                    id: user._id,
                    name: user.name,
                    email: user.email,
                    role: user.role,
                    businessName: user.businessName,
                    gstNumber: user.gstNumber,
                    phone: user.phone,
                    address: user.address,
                    isEmailVerified: user.isEmailVerified,
                    lastLogin: user.lastLogin
                },
                token,
                refreshToken
            }
        });
    }
    catch (error) {
        console.error('Login error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error during login'
        });
    }
};
exports.login = login;
// Get current user profile
const getProfile = async (req, res) => {
    try {
        const user = req.user;
        if (!user) {
            res.status(401).json({
                success: false,
                message: 'User not authenticated'
            });
            return;
        }
        // Fetch fresh data from database to ensure we have the latest updates
        const freshUser = await User_1.default.findById(user._id);
        if (!freshUser) {
            res.status(404).json({
                success: false,
                message: 'User not found'
            });
            return;
        }
        console.log('🔍 Profile request - Fresh user data:', {
            name: freshUser.name,
            businessName: freshUser.businessName,
            updatedAt: freshUser.updatedAt
        });
        res.status(200).json({
            success: true,
            message: 'Profile retrieved successfully',
            data: {
                user: {
                    id: freshUser._id,
                    name: freshUser.name,
                    email: freshUser.email,
                    role: freshUser.role,
                    businessName: freshUser.businessName,
                    logo: freshUser.logo,
                    gstNumber: freshUser.gstNumber,
                    phone: freshUser.phone,
                    address: freshUser.address,
                    upiId: freshUser.upiId,
                    bankDetails: freshUser.bankDetails,
                    isEmailVerified: freshUser.isEmailVerified,
                    lastLogin: freshUser.lastLogin,
                    createdAt: freshUser.createdAt,
                    updatedAt: freshUser.updatedAt
                }
            }
        });
    }
    catch (error) {
        console.error('Get profile error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
};
exports.getProfile = getProfile;
// Update user profile
const updateProfile = async (req, res) => {
    try {
        const user = req.user;
        const { name, businessName, logo, gstNumber, phone, address, upiId, bankDetails } = req.body;
        console.log('🔄 Profile update request received:', {
            userId: user?._id,
            name,
            businessName,
            gstNumber,
            phone,
            upiId
        });
        if (!user) {
            res.status(401).json({
                success: false,
                message: 'User not authenticated'
            });
            return;
        }
        // Check if GST number is being changed and if it already exists
        if (gstNumber && gstNumber !== user.gstNumber) {
            const existingGST = await User_1.default.findOne({
                gstNumber,
                _id: { $ne: user._id }
            });
            if (existingGST) {
                res.status(400).json({
                    success: false,
                    message: 'GST number already registered by another user'
                });
                return;
            }
        }
        // Validate UPI ID if provided
        if (upiId !== undefined && upiId !== user.upiId) {
            if (upiId && upiId.trim()) {
                const upiValidation = await upiValidationService_1.default.validateWithSuggestions(upiId.trim());
                if (!upiValidation.isValid) {
                    res.status(400).json({
                        success: false,
                        message: upiValidation.error || 'Invalid UPI ID',
                        suggestions: upiValidation.suggestions
                    });
                    return;
                }
            }
        }
        // Update user fields
        const updateFields = {};
        if (name !== undefined)
            updateFields.name = name;
        if (businessName !== undefined)
            updateFields.businessName = businessName;
        if (logo !== undefined)
            updateFields.logo = logo;
        if (gstNumber !== undefined)
            updateFields.gstNumber = gstNumber;
        if (phone !== undefined)
            updateFields.phone = phone;
        if (address !== undefined)
            updateFields.address = address;
        if (upiId !== undefined)
            updateFields.upiId = upiId;
        if (bankDetails !== undefined)
            updateFields.bankDetails = bankDetails;
        console.log('🔄 Updating user profile with fields:', updateFields);
        console.log('🔍 User ID being updated:', user._id);
        // First, let's check the current user data
        const currentUser = await User_1.default.findById(user._id);
        console.log('📋 Current user data before update:', {
            name: currentUser?.name,
            businessName: currentUser?.businessName,
            gstNumber: currentUser?.gstNumber
        });
        const updatedUser = await User_1.default.findByIdAndUpdate(user._id, updateFields, {
            new: true,
            runValidators: true
        });
        console.log('📋 User data after update:', {
            name: updatedUser?.name,
            businessName: updatedUser?.businessName,
            gstNumber: updatedUser?.gstNumber
        });
        if (!updatedUser) {
            res.status(404).json({
                success: false,
                message: 'User not found'
            });
            return;
        }
        console.log('✅ Profile updated successfully:', {
            userId: updatedUser._id,
            name: updatedUser.name,
            businessName: updatedUser.businessName,
            updatedAt: updatedUser.updatedAt
        });
        res.status(200).json({
            success: true,
            message: 'Profile updated successfully',
            data: {
                user: {
                    id: updatedUser._id,
                    name: updatedUser.name,
                    email: updatedUser.email,
                    role: updatedUser.role,
                    businessName: updatedUser.businessName,
                    logo: updatedUser.logo,
                    gstNumber: updatedUser.gstNumber,
                    phone: updatedUser.phone,
                    address: updatedUser.address,
                    upiId: updatedUser.upiId,
                    bankDetails: updatedUser.bankDetails,
                    isEmailVerified: updatedUser.isEmailVerified,
                    lastLogin: updatedUser.lastLogin,
                    updatedAt: updatedUser.updatedAt
                }
            }
        });
    }
    catch (error) {
        console.error('Update profile error:', error);
        if (error.name === 'ValidationError') {
            const errors = Object.values(error.errors).map((err) => err.message);
            res.status(400).json({
                success: false,
                message: 'Validation error',
                errors
            });
            return;
        }
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
};
exports.updateProfile = updateProfile;
// Logout user with token blacklisting
const logout = async (req, res) => {
    try {
        const authHeader = req.headers.authorization;
        if (authHeader && authHeader.startsWith('Bearer ')) {
            const token = authHeader.substring(7);
            // Import tokenBlacklist service
            const { tokenBlacklist } = require('../services/tokenBlacklistService');
            // Blacklist the current token
            await tokenBlacklist.blacklistToken(token, 'logout');
            console.log(`🚫 Token blacklisted for user ${req.user?.id} on logout`);
        }
        res.status(200).json({
            success: true,
            message: 'Logged out successfully. Token has been invalidated.'
        });
    }
    catch (error) {
        console.error('Logout error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error during logout'
        });
    }
};
exports.logout = logout;
// Helper function to initialize user compliance items
async function initializeUserCompliance(userId) {
    try {
        // Get all active compliance deadlines
        const complianceDeadlines = await Compliance_1.ComplianceDeadline.find({ isActive: true });
        // Create user compliance entries for each deadline
        const userComplianceItems = complianceDeadlines.map(deadline => ({
            userId,
            complianceId: deadline._id,
            isEnabled: true,
            reminderDays: [7, 3, 1], // Default reminder days
            nextDueDate: deadline.nextDueDate || deadline.dueDate,
            isCompleted: false
        }));
        await Compliance_1.UserCompliance.insertMany(userComplianceItems);
        console.log(`Initialized ${userComplianceItems.length} compliance items for user ${userId}`);
    }
    catch (error) {
        console.error('Error initializing user compliance:', error);
    }
}
// Helper function to initialize notification preferences
async function initializeNotificationPreferences(userId) {
    try {
        const preferences = new Notification_1.NotificationPreference({
            userId,
            emailNotifications: true,
            complianceReminders: true,
            invoiceReminders: true,
            systemUpdates: true,
            marketingEmails: false,
            reminderTiming: {
                days: [7, 3, 1],
                timeOfDay: '09:00',
                timezone: 'Asia/Kolkata'
            },
            maxDailyEmails: 5,
            digestMode: false
        });
        await preferences.save();
        console.log(`Initialized notification preferences for user ${userId}`);
    }
    catch (error) {
        console.error('Error initializing notification preferences:', error);
    }
}
// Send email verification
const sendEmailVerification = async (req, res) => {
    try {
        const user = req.user;
        if (!user) {
            res.status(401).json({
                success: false,
                message: 'User not authenticated'
            });
            return;
        }
        if (user.isEmailVerified) {
            res.status(400).json({
                success: false,
                message: 'Email is already verified'
            });
            return;
        }
        // Generate verification token
        const verificationToken = crypto_1.default.randomBytes(32).toString('hex');
        const verificationExpires = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours
        // Update user with verification token
        await User_1.default.findByIdAndUpdate(user._id, {
            emailVerificationToken: verificationToken,
            emailVerificationExpires: verificationExpires
        });
        // Send verification email
        try {
            const emailService = (0, emailService_1.default)();
            await emailService.sendEmailVerificationEmail(user.email, verificationToken, user.name);
            res.status(200).json({
                success: true,
                message: 'Verification email sent successfully! Please check your inbox.'
            });
        }
        catch (emailError) {
            console.error('Failed to send verification email:', emailError);
            // If email service is not configured, provide alternative instructions
            if (emailError.message?.includes('not configured') || emailError.code === 'EAUTH') {
                res.status(200).json({
                    success: true,
                    message: 'Email service is not configured. For development purposes, you can manually verify your email by visiting: /verify-email?token=' + verificationToken,
                    developmentToken: process.env.NODE_ENV === 'development' ? verificationToken : undefined
                });
            }
            else {
                res.status(500).json({
                    success: false,
                    message: 'Email service is currently unavailable. Please try again later.'
                });
            }
            return;
        }
    }
    catch (error) {
        console.error('Send email verification error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
};
exports.sendEmailVerification = sendEmailVerification;
// Verify email with token
const verifyEmail = async (req, res) => {
    try {
        const { token } = req.body;
        if (!token) {
            res.status(400).json({
                success: false,
                message: 'Verification token is required'
            });
            return;
        }
        // Find user with valid verification token
        const user = await User_1.default.findOne({
            emailVerificationToken: token,
            emailVerificationExpires: { $gt: new Date() }
        }).select('+emailVerificationToken +emailVerificationExpires');
        if (!user) {
            res.status(400).json({
                success: false,
                message: 'Invalid or expired verification token'
            });
            return;
        }
        if (user.isEmailVerified) {
            res.status(400).json({
                success: false,
                message: 'Email is already verified'
            });
            return;
        }
        // Update user as verified
        user.isEmailVerified = true;
        user.emailVerificationToken = undefined;
        user.emailVerificationExpires = undefined;
        await user.save();
        res.status(200).json({
            success: true,
            message: 'Email verified successfully',
            data: {
                user: {
                    id: user._id,
                    name: user.name,
                    email: user.email,
                    isEmailVerified: user.isEmailVerified
                }
            }
        });
    }
    catch (error) {
        console.error('Email verification error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
};
exports.verifyEmail = verifyEmail;
// Validate UPI ID
const validateUPI = async (req, res) => {
    try {
        const { upiId } = req.body;
        if (!upiId) {
            res.status(400).json({
                success: false,
                message: 'UPI ID is required'
            });
            return;
        }
        const validation = await upiValidationService_1.default.validateWithSuggestions(upiId);
        if (validation.isValid) {
            res.status(200).json({
                success: true,
                message: 'UPI ID is valid',
                data: {
                    upiId,
                    provider: validation.provider,
                    exists: validation.exists
                }
            });
        }
        else {
            res.status(400).json({
                success: false,
                message: validation.error || 'Invalid UPI ID',
                suggestions: validation.suggestions
            });
        }
    }
    catch (error) {
        console.error('UPI validation error:', error);
        res.status(500).json({
            success: false,
            message: 'Unable to validate UPI ID at this time'
        });
    }
};
exports.validateUPI = validateUPI;
// Get popular UPI providers
const getUPIProviders = async (req, res) => {
    try {
        const providers = upiValidationService_1.default.getPopularProviders();
        res.status(200).json({
            success: true,
            message: 'UPI providers retrieved successfully',
            data: providers
        });
    }
    catch (error) {
        console.error('Get UPI providers error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
};
exports.getUPIProviders = getUPIProviders;
//# sourceMappingURL=authController.js.map