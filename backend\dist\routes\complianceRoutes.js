"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const complianceController_1 = require("../controllers/complianceController");
const auth_1 = require("../middleware/auth");
const complianceValidation_1 = require("../middleware/complianceValidation");
const router = express_1.default.Router();
// All routes require authentication
router.use(auth_1.authenticate);
// Get user compliance data (for dashboard)
router.get('/user-compliance', complianceController_1.getUserCompliance);
// Get compliance calendar
router.get('/calendar', complianceController_1.getComplianceCalendar);
// Get upcoming deadlines
router.get('/upcoming', complianceController_1.getUpcomingDeadlines);
// Get compliance statistics
router.get('/stats', complianceController_1.getComplianceStats);
// Get overdue activity details
router.get('/overdue-activity', complianceController_1.getOverdueActivity);
// Mark compliance as completed
router.patch('/:complianceId/complete', complianceController_1.markComplianceCompleted);
// Update compliance settings
router.put('/:complianceId/settings', complianceValidation_1.validateComplianceUpdate, complianceController_1.updateComplianceSettings);
// Add custom compliance item
router.post('/custom', complianceValidation_1.validateComplianceCreation, complianceController_1.addCustomCompliance);
// Clear all user compliance data (for removing fake data)
router.delete('/clear-all', complianceController_1.clearUserCompliance);
exports.default = router;
//# sourceMappingURL=complianceRoutes.js.map