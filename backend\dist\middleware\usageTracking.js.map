{"version": 3, "file": "usageTracking.js", "sourceRoot": "", "sources": ["../../src/middleware/usageTracking.ts"], "names": [], "mappings": ";;;;;;AACA,0FAAkE;AAElE,6CAA6C;AACtC,MAAM,eAAe,GAAG,CAAC,MAA6B,EAAE,EAAE;IAC/D,OAAO,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;QAC/D,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC;YAC7B,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBAC1B,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,wBAAwB;iBAClC,CAAC,CAAC;YACL,CAAC;YAED,+CAA+C;YAC/C,IAAI,MAAM,GAAG,CAAC,CAAC;YACf,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;gBACzB,IAAI,GAAG,CAAC,IAAI,EAAE,CAAC;oBACb,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,sBAAsB;gBAC3E,CAAC;qBAAM,IAAI,GAAG,CAAC,KAAK,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;oBACjD,wBAAwB;oBACxB,MAAM,GAAG,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,IAAI,EAAE,EAAE;wBACxC,OAAO,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC;oBACtD,CAAC,EAAE,CAAC,CAAC,CAAC;gBACR,CAAC;YACH,CAAC;YAED,uCAAuC;YACvC,MAAM,UAAU,GAAG,MAAM,6BAAmB,CAAC,eAAe,CAC1D,MAAM,CAAC,QAAQ,EAAE,EACjB,MAAM,EACN,MAAM,CACP,CAAC;YAEF,IAAI,CAAC,UAAU,EAAE,CAAC;gBAChB,MAAM,YAAY,GAAG,MAAM,6BAAmB,CAAC,wBAAwB,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC;gBAE3F,IAAI,OAAO,GAAG,EAAE,CAAC;gBACjB,IAAI,eAAe,GAAG,KAAK,CAAC;gBAE5B,QAAQ,MAAM,EAAE,CAAC;oBACf,KAAK,SAAS;wBACZ,OAAO,GAAG,wCAAwC,YAAY,EAAE,KAAK,CAAC,YAAY,IAAI,YAAY,EAAE,QAAQ,CAAC,WAAW,uBAAuB,CAAC;wBAChJ,eAAe,GAAG,YAAY,EAAE,MAAM,KAAK,MAAM,CAAC;wBAClD,MAAM;oBACR,KAAK,SAAS;wBACZ,OAAO,GAAG,wCAAwC,YAAY,EAAE,KAAK,CAAC,WAAW,MAAM,YAAY,EAAE,QAAQ,CAAC,UAAU,KAAK,CAAC;wBAC9H,eAAe,GAAG,IAAI,CAAC;wBACvB,MAAM;gBACV,CAAC;gBAED,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBAC1B,OAAO,EAAE,KAAK;oBACd,OAAO;oBACP,IAAI,EAAE,sBAAsB;oBAC5B,IAAI,EAAE;wBACJ,MAAM;wBACN,YAAY,EAAE,YAAY,EAAE,KAAK;wBACjC,MAAM,EAAE,YAAY,EAAE,QAAQ;wBAC9B,eAAe;wBACf,WAAW,EAAE,YAAY,EAAE,MAAM;qBAClC;iBACF,CAAC,CAAC;YACL,CAAC;YAED,+DAA+D;YAC/D,MAAM,cAAc,GAAG;gBACrB,SAAS,EAAE,UAAmB;gBAC9B,SAAS,EAAE,SAAkB;aAC9B,CAAC;YAEF,GAAG,CAAC,WAAW,GAAG,EAAE,IAAI,EAAE,cAAc,CAAC,MAAM,CAAC,EAAE,MAAM,EAAE,CAAC;YAC3D,IAAI,EAAE,CAAC;QACT,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;YACjD,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,8BAA8B;aACxC,CAAC,CAAC;QACL,CAAC;IACH,CAAC,CAAC;AACJ,CAAC,CAAC;AA9EW,QAAA,eAAe,mBA8E1B;AAEF,wDAAwD;AACjD,MAAM,cAAc,GAAG,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;IACtF,IAAI,CAAC;QACH,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC;QAC7B,MAAM,WAAW,GAAG,GAAG,CAAC,WAAW,CAAC;QAEpC,IAAI,MAAM,IAAI,WAAW,EAAE,CAAC;YAC1B,MAAM,6BAAmB,CAAC,cAAc,CACtC,MAAM,CAAC,QAAQ,EAAE,EACjB,WAAW,CAAC,IAAI,EAChB,WAAW,CAAC,MAAM,CACnB,CAAC;QACJ,CAAC;QAED,IAAI,EAAE,CAAC;IACT,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;QAC/C,iDAAiD;QACjD,IAAI,EAAE,CAAC;IACT,CAAC;AACH,CAAC,CAAC;AAnBW,QAAA,cAAc,kBAmBzB;AAEF,qCAAqC;AAC9B,MAAM,kBAAkB,GAAG,CAAC,OAAe,EAAE,EAAE;IACpD,OAAO,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;QAC/D,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC;YAC7B,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBAC1B,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,wBAAwB;iBAClC,CAAC,CAAC;YACL,CAAC;YAED,MAAM,YAAY,GAAG,MAAM,6BAAmB,CAAC,wBAAwB,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC;YAE3F,IAAI,CAAC,YAAY,EAAE,CAAC;gBAClB,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBAC1B,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,uBAAuB;oBAChC,IAAI,EAAE,iBAAiB;iBACxB,CAAC,CAAC;YACL,CAAC;YAED,IAAI,CAAC,6BAAmB,CAAC,oBAAoB,CAAC,YAAY,CAAC,EAAE,CAAC;gBAC5D,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBAC1B,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,8BAA8B;oBACvC,IAAI,EAAE,wBAAwB;iBAC/B,CAAC,CAAC;YACL,CAAC;YAED,0CAA0C;YAC1C,MAAM,SAAS,GAAG,YAAY,CAAC,QAAQ,IAAI,YAAY,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;YAE1E,IAAI,CAAC,SAAS,EAAE,CAAC;gBACf,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBAC1B,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,YAAY,OAAO,sCAAsC;oBAClE,IAAI,EAAE,uBAAuB;oBAC7B,IAAI,EAAE;wBACJ,OAAO;wBACP,WAAW,EAAE,YAAY,CAAC,MAAM;wBAChC,eAAe,EAAE,IAAI;qBACtB;iBACF,CAAC,CAAC;YACL,CAAC;YAED,IAAI,EAAE,CAAC;QACT,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;YACpD,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,gCAAgC;aAC1C,CAAC,CAAC;QACL,CAAC;IACH,CAAC,CAAC;AACJ,CAAC,CAAC;AAtDW,QAAA,kBAAkB,sBAsD7B;AAEF,kDAAkD;AAC3C,MAAM,mBAAmB,GAAG,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;IAC3F,IAAI,CAAC;QACH,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC;QAC7B,IAAI,MAAM,EAAE,CAAC;YACX,MAAM,YAAY,GAAG,MAAM,6BAAmB,CAAC,wBAAwB,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC;YAC3F,GAAG,CAAC,gBAAgB,GAAG,YAAY,CAAC;QACtC,CAAC;QACD,IAAI,EAAE,CAAC;IACT,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;QACrD,IAAI,EAAE,CAAC,CAAC,qCAAqC;IAC/C,CAAC;AACH,CAAC,CAAC;AAZW,QAAA,mBAAmB,uBAY9B"}