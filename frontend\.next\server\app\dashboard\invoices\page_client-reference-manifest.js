globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/dashboard/invoices/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./src/app/page.tsx":{"*":{"id":"(ssr)/./src/app/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs":{"*":{"id":"(ssr)/./node_modules/react-hot-toast/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/contexts/AuthContext.tsx":{"*":{"id":"(ssr)/./src/contexts/AuthContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/login/page.tsx":{"*":{"id":"(ssr)/./src/app/login/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/page.tsx":{"*":{"id":"(ssr)/./src/app/dashboard/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/invoices/page.tsx":{"*":{"id":"(ssr)/./src/app/dashboard/invoices/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/invoices/[id]/page.tsx":{"*":{"id":"(ssr)/./src/app/dashboard/invoices/[id]/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/documents/page.tsx":{"*":{"id":"(ssr)/./src/app/dashboard/documents/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/compliance/page.tsx":{"*":{"id":"(ssr)/./src/app/dashboard/compliance/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/subscription/page.tsx":{"*":{"id":"(ssr)/./src/app/dashboard/subscription/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/notifications/page.tsx":{"*":{"id":"(ssr)/./src/app/dashboard/notifications/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/invoices/create/page.tsx":{"*":{"id":"(ssr)/./src/app/dashboard/invoices/create/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/profile/page.tsx":{"*":{"id":"(ssr)/./src/app/dashboard/profile/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/page.tsx":{"*":{"id":"(ssr)/./src/app/admin/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/users/page.tsx":{"*":{"id":"(ssr)/./src/app/admin/users/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/settings/page.tsx":{"*":{"id":"(ssr)/./src/app/dashboard/settings/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/settings/payment/page.tsx":{"*":{"id":"(ssr)/./src/app/dashboard/settings/payment/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\Desktop\\invoNest\\frontend\\src\\app\\page.tsx":{"id":"(app-pages-browser)/./src/app/page.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\invoNest\\frontend\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\invoNest\\frontend\\node_modules\\react-hot-toast\\dist\\index.mjs":{"id":"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\invoNest\\frontend\\src\\app\\globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\invoNest\\frontend\\src\\contexts\\AuthContext.tsx":{"id":"(app-pages-browser)/./src/contexts/AuthContext.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\invoNest\\frontend\\node_modules\\next\\dist\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\invoNest\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\invoNest\\frontend\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\invoNest\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\invoNest\\frontend\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\invoNest\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\invoNest\\frontend\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\invoNest\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\invoNest\\frontend\\node_modules\\next\\dist\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\invoNest\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\invoNest\\frontend\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\invoNest\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\invoNest\\frontend\\src\\app\\login\\page.tsx":{"id":"(app-pages-browser)/./src/app/login/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\invoNest\\frontend\\src\\app\\dashboard\\page.tsx":{"id":"(app-pages-browser)/./src/app/dashboard/page.tsx","name":"*","chunks":["app/dashboard/page","static/chunks/app/dashboard/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\invoNest\\frontend\\src\\app\\dashboard\\invoices\\page.tsx":{"id":"(app-pages-browser)/./src/app/dashboard/invoices/page.tsx","name":"*","chunks":["app/dashboard/invoices/page","static/chunks/app/dashboard/invoices/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\invoNest\\frontend\\src\\app\\dashboard\\invoices\\[id]\\page.tsx":{"id":"(app-pages-browser)/./src/app/dashboard/invoices/[id]/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\invoNest\\frontend\\src\\app\\dashboard\\documents\\page.tsx":{"id":"(app-pages-browser)/./src/app/dashboard/documents/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\invoNest\\frontend\\src\\app\\dashboard\\compliance\\page.tsx":{"id":"(app-pages-browser)/./src/app/dashboard/compliance/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\invoNest\\frontend\\src\\app\\dashboard\\subscription\\page.tsx":{"id":"(app-pages-browser)/./src/app/dashboard/subscription/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\invoNest\\frontend\\src\\app\\dashboard\\notifications\\page.tsx":{"id":"(app-pages-browser)/./src/app/dashboard/notifications/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\invoNest\\frontend\\src\\app\\dashboard\\invoices\\create\\page.tsx":{"id":"(app-pages-browser)/./src/app/dashboard/invoices/create/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\invoNest\\frontend\\src\\app\\dashboard\\profile\\page.tsx":{"id":"(app-pages-browser)/./src/app/dashboard/profile/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\invoNest\\frontend\\src\\app\\admin\\page.tsx":{"id":"(app-pages-browser)/./src/app/admin/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\invoNest\\frontend\\src\\app\\admin\\users\\page.tsx":{"id":"(app-pages-browser)/./src/app/admin/users/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\invoNest\\frontend\\src\\app\\dashboard\\settings\\page.tsx":{"id":"(app-pages-browser)/./src/app/dashboard/settings/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\invoNest\\frontend\\src\\app\\dashboard\\settings\\payment\\page.tsx":{"id":"(app-pages-browser)/./src/app/dashboard/settings/payment/page.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\Desktop\\invoNest\\frontend\\src\\":[],"C:\\Users\\<USER>\\Desktop\\invoNest\\frontend\\src\\app\\page":[],"C:\\Users\\<USER>\\Desktop\\invoNest\\frontend\\src\\app\\layout":["static/css/app/layout.css"],"C:\\Users\\<USER>\\Desktop\\invoNest\\frontend\\src\\app\\dashboard\\page":[],"C:\\Users\\<USER>\\Desktop\\invoNest\\frontend\\src\\app\\dashboard\\invoices\\page":[]}}