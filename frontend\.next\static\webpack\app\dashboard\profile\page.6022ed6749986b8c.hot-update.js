"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/profile/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/profile/page.tsx":
/*!********************************************!*\
  !*** ./src/app/dashboard/profile/page.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ProfilePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../lib/auth */ \"(app-pages-browser)/./src/lib/auth.ts\");\n/* harmony import */ var _components_dashboard_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../components/dashboard/DashboardLayout */ \"(app-pages-browser)/./src/components/dashboard/DashboardLayout.tsx\");\n/* harmony import */ var _components_FileUpload__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../components/FileUpload */ \"(app-pages-browser)/./src/components/FileUpload.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction ProfilePage() {\n    var _formData_address, _formData_address1, _formData_address2, _formData_address3, _formData_address4;\n    _s();\n    const { user, updateUser } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [editing, setEditing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [saving, setSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        type: \"\",\n        text: \"\"\n    });\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [verificationLoading, setVerificationLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [verificationMessage, setVerificationMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [logoUploading, setLogoUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [logoPreview, setLogoPreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [signatureUploading, setSignatureUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [signaturePreview, setSignaturePreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (user) {\n            setFormData({\n                ...user,\n                address: user.address || {\n                    street: \"\",\n                    city: \"\",\n                    state: \"\",\n                    pincode: \"\",\n                    country: \"India\"\n                }\n            });\n            // Set logo preview if user has a logo\n            if (user.logo) {\n                // Handle both full paths and just filenames\n                const logoPath = user.logo.includes(\"/\") ? user.logo : \"uploads/logos/\".concat(user.logo);\n                setLogoPreview(\"http://localhost:5000/\".concat(logoPath));\n            }\n        }\n    }, [\n        user\n    ]);\n    const handleInputChange = (e)=>{\n        const { name, value } = e.target;\n        // Handle nested address fields\n        if ([\n            \"street\",\n            \"city\",\n            \"state\",\n            \"pincode\",\n            \"country\"\n        ].includes(name)) {\n            setFormData((prev)=>({\n                    ...prev,\n                    address: {\n                        ...prev.address,\n                        [name]: value\n                    }\n                }));\n        } else {\n            setFormData((prev)=>({\n                    ...prev,\n                    [name]: value\n                }));\n        }\n    };\n    const handleLogoUpload = async (files)=>{\n        if (files.length === 0) return;\n        const file = files[0];\n        setLogoUploading(true);\n        setMessage({\n            type: \"\",\n            text: \"\"\n        });\n        try {\n            const formData = new FormData();\n            formData.append(\"logo\", file);\n            const token = localStorage.getItem(\"token\");\n            const response = await fetch(\"http://localhost:5000/api/uploads/logo\", {\n                method: \"POST\",\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(token)\n                },\n                body: formData\n            });\n            const data = await response.json();\n            if (data.success) {\n                // Update form data with new logo path\n                setFormData((prev)=>({\n                        ...prev,\n                        logo: data.data.logoPath\n                    }));\n                // Set preview\n                setLogoPreview(\"http://localhost:5000/\".concat(data.data.logoPath));\n                // Update user context with new logo\n                if (user) {\n                    updateUser({\n                        ...user,\n                        logo: data.data.logoPath\n                    });\n                }\n                setMessage({\n                    type: \"success\",\n                    text: \"Logo uploaded successfully!\"\n                });\n            } else {\n                setMessage({\n                    type: \"error\",\n                    text: data.message || \"Failed to upload logo\"\n                });\n            }\n        } catch (error) {\n            console.error(\"Logo upload error:\", error);\n            setMessage({\n                type: \"error\",\n                text: \"Failed to upload logo\"\n            });\n        } finally{\n            setLogoUploading(false);\n        }\n    };\n    const handleRemoveLogo = async ()=>{\n        setLogoUploading(true);\n        try {\n            const token = localStorage.getItem(\"token\");\n            const response = await fetch(\"http://localhost:5000/api/uploads/logo\", {\n                method: \"DELETE\",\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(token)\n                }\n            });\n            const data = await response.json();\n            if (data.success) {\n                setFormData((prev)=>({\n                        ...prev,\n                        logo: null\n                    }));\n                setLogoPreview(null);\n                // Update user context to remove logo\n                if (user) {\n                    updateUser({\n                        ...user,\n                        logo: undefined\n                    });\n                }\n                setMessage({\n                    type: \"success\",\n                    text: \"Logo removed successfully!\"\n                });\n            } else {\n                setMessage({\n                    type: \"error\",\n                    text: data.message || \"Failed to remove logo\"\n                });\n            }\n        } catch (error) {\n            console.error(\"Logo remove error:\", error);\n            setMessage({\n                type: \"error\",\n                text: \"Failed to remove logo\"\n            });\n        } finally{\n            setLogoUploading(false);\n        }\n    };\n    const handleSave = async ()=>{\n        setSaving(true);\n        setMessage({\n            type: \"\",\n            text: \"\"\n        });\n        try {\n            var _formData_name, _formData_businessName, _formData_gstNumber, _formData_phone, _formData_address_street, _formData_address, _formData_address_city, _formData_address1, _formData_address_state, _formData_address2, _formData_address_pincode, _formData_address3, _formData_address_country, _formData_address4;\n            // Clean up the form data before sending\n            const cleanFormData = {};\n            // Only include non-empty fields\n            if ((_formData_name = formData.name) === null || _formData_name === void 0 ? void 0 : _formData_name.trim()) {\n                cleanFormData.name = formData.name.trim();\n            }\n            if ((_formData_businessName = formData.businessName) === null || _formData_businessName === void 0 ? void 0 : _formData_businessName.trim()) {\n                cleanFormData.businessName = formData.businessName.trim();\n            }\n            if ((_formData_gstNumber = formData.gstNumber) === null || _formData_gstNumber === void 0 ? void 0 : _formData_gstNumber.trim()) {\n                cleanFormData.gstNumber = formData.gstNumber.trim();\n            }\n            if ((_formData_phone = formData.phone) === null || _formData_phone === void 0 ? void 0 : _formData_phone.trim()) {\n                cleanFormData.phone = formData.phone.trim();\n            }\n            // Handle address separately\n            const address = {};\n            if ((_formData_address = formData.address) === null || _formData_address === void 0 ? void 0 : (_formData_address_street = _formData_address.street) === null || _formData_address_street === void 0 ? void 0 : _formData_address_street.trim()) {\n                address.street = formData.address.street.trim();\n            }\n            if ((_formData_address1 = formData.address) === null || _formData_address1 === void 0 ? void 0 : (_formData_address_city = _formData_address1.city) === null || _formData_address_city === void 0 ? void 0 : _formData_address_city.trim()) {\n                address.city = formData.address.city.trim();\n            }\n            if ((_formData_address2 = formData.address) === null || _formData_address2 === void 0 ? void 0 : (_formData_address_state = _formData_address2.state) === null || _formData_address_state === void 0 ? void 0 : _formData_address_state.trim()) {\n                address.state = formData.address.state.trim();\n            }\n            if ((_formData_address3 = formData.address) === null || _formData_address3 === void 0 ? void 0 : (_formData_address_pincode = _formData_address3.pincode) === null || _formData_address_pincode === void 0 ? void 0 : _formData_address_pincode.trim()) {\n                address.pincode = formData.address.pincode.trim();\n            }\n            if ((_formData_address4 = formData.address) === null || _formData_address4 === void 0 ? void 0 : (_formData_address_country = _formData_address4.country) === null || _formData_address_country === void 0 ? void 0 : _formData_address_country.trim()) {\n                address.country = formData.address.country.trim();\n            }\n            // Only include address if it has at least one field\n            if (Object.keys(address).length > 0) {\n                cleanFormData.address = address;\n            }\n            // Include logo if it exists\n            if (formData.logo) {\n                cleanFormData.logo = formData.logo;\n            }\n            console.log(\"Sending profile data:\", cleanFormData);\n            const response = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.updateProfile)(cleanFormData);\n            if (response.success && response.data) {\n                updateUser(response.data.user);\n                setEditing(false);\n                setMessage({\n                    type: \"success\",\n                    text: \"Profile updated successfully!\"\n                });\n            } else {\n                console.error(\"Profile update failed:\", response);\n                const errorMessage = response.errors ? response.errors.join(\", \") : response.message || \"Failed to update profile\";\n                setMessage({\n                    type: \"error\",\n                    text: errorMessage\n                });\n            }\n        } catch (error) {\n            console.error(\"Profile update error:\", error);\n            setMessage({\n                type: \"error\",\n                text: \"Failed to update profile\"\n            });\n        } finally{\n            setSaving(false);\n        }\n    };\n    const handleCancel = ()=>{\n        if (user) {\n            setFormData({\n                ...user,\n                address: user.address || {\n                    street: \"\",\n                    city: \"\",\n                    state: \"\",\n                    pincode: \"\",\n                    country: \"India\"\n                }\n            });\n        }\n        setEditing(false);\n        setMessage({\n            type: \"\",\n            text: \"\"\n        });\n    };\n    const sendVerificationEmail = async ()=>{\n        setVerificationLoading(true);\n        setVerificationMessage(\"\");\n        try {\n            const response = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.authenticatedFetch)(\"/api/auth/send-verification\", {\n                method: \"POST\"\n            });\n            const data = await response.json();\n            if (data.success) {\n                if (data.developmentToken) {\n                    // Development mode - show direct verification link\n                    const verificationUrl = \"\".concat(window.location.origin, \"/verify-email?token=\").concat(data.developmentToken);\n                    setVerificationMessage(\"development_link:\".concat(verificationUrl));\n                } else {\n                    setVerificationMessage(\"Verification email sent! Please check your inbox.\");\n                }\n            } else {\n                setVerificationMessage(data.message || \"Failed to send verification email.\");\n            }\n        } catch (error) {\n            console.error(\"Send verification error:\", error);\n            setVerificationMessage(\"Network error. Please try again.\");\n        } finally{\n            setVerificationLoading(false);\n        }\n    };\n    if (!user) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        title: \"Profile\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__.DashboardHeader, {\n                title: \"Profile\",\n                subtitle: \"Manage your account information and business details\",\n                actions: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [\n                        editing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleCancel,\n                            className: \"px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-800 bg-white hover:bg-gray-50 transition-colors\",\n                            disabled: saving,\n                            children: \"Cancel\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                            lineNumber: 261,\n                            columnNumber: 15\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>editing ? handleSave() : setEditing(true),\n                            className: \"px-4 py-2 bg-indigo-600 text-white rounded-lg text-sm font-medium hover:bg-indigo-700 transition-colors disabled:opacity-50\",\n                            disabled: saving,\n                            children: saving ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full mr-2 inline-block\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                        lineNumber: 276,\n                                        columnNumber: 19\n                                    }, void 0),\n                                    \"Saving...\"\n                                ]\n                            }, void 0, true) : editing ? \"Save Changes\" : \"Edit Profile\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                            lineNumber: 269,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                    lineNumber: 259,\n                    columnNumber: 11\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                lineNumber: 255,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-4xl mx-auto p-4 lg:p-8\",\n                children: [\n                    message.text && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6 p-4 rounded-lg \".concat(message.type === \"success\" ? \"bg-green-50 border border-green-200 text-green-800\" : \"bg-red-50 border border-red-200 text-red-800\"),\n                        children: message.text\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                        lineNumber: 288,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row items-center space-y-4 sm:space-y-0 sm:space-x-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-24 h-24 rounded-full overflow-hidden border-2 border-gray-200 bg-gradient-to-br from-indigo-500 to-purple-600 flex items-center justify-center\",\n                                    children: logoPreview ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: logoPreview,\n                                        alt: \"Business Logo\",\n                                        className: \"w-full h-full object-cover\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                        lineNumber: 302,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white text-3xl font-bold\",\n                                        children: user.name.charAt(0).toUpperCase()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                    lineNumber: 300,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center sm:text-left\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-2xl lg:text-3xl font-bold text-gray-900\",\n                                            children: user.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                            lineNumber: 314,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-800 mt-1\",\n                                            children: user.email\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                            lineNumber: 315,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col sm:flex-row items-center sm:items-start space-y-2 sm:space-y-0 sm:space-x-3 mt-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat(user.isEmailVerified ? \"bg-green-100 text-green-800\" : \"bg-yellow-100 text-yellow-800\"),\n                                                    children: user.isEmailVerified ? \"✓ Email Verified\" : \"⚠ Email Not Verified\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                    lineNumber: 317,\n                                                    columnNumber: 17\n                                                }, this),\n                                                !user.isEmailVerified && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: sendVerificationEmail,\n                                                    disabled: verificationLoading,\n                                                    className: \"inline-flex items-center px-3 py-1 text-xs font-medium text-indigo-600 bg-indigo-50 border border-indigo-200 rounded-full hover:bg-indigo-100 transition-colors disabled:opacity-50\",\n                                                    children: verificationLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"animate-spin h-3 w-3 border border-indigo-600 border-t-transparent rounded-full mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 328,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Sending...\"\n                                                        ]\n                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-3 h-3 mr-1\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                    lineNumber: 334,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 333,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Verify Email\"\n                                                        ]\n                                                    }, void 0, true)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                    lineNumber: 321,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                            lineNumber: 316,\n                                            columnNumber: 15\n                                        }, this),\n                                        verificationMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-2 p-2 rounded-md text-xs \".concat(verificationMessage.includes(\"sent\") || verificationMessage.includes(\"development_link\") ? \"bg-green-50 text-green-700 border border-green-200\" : \"bg-red-50 text-red-700 border border-red-200\"),\n                                            children: verificationMessage.startsWith(\"development_link:\") ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"mb-2\",\n                                                        children: \"\\uD83D\\uDCE7 Email service not configured (Development Mode)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 350,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: verificationMessage.replace(\"development_link:\", \"\"),\n                                                        className: \"inline-flex items-center px-3 py-1 bg-green-600 text-white text-xs rounded-md hover:bg-green-700 transition-colors\",\n                                                        target: \"_blank\",\n                                                        rel: \"noopener noreferrer\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-3 h-3 mr-1\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M5 13l4 4L19 7\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                    lineNumber: 358,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 357,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Click to Verify Email\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 351,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 349,\n                                                columnNumber: 21\n                                            }, this) : verificationMessage\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                            lineNumber: 343,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                    lineNumber: 313,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                            lineNumber: 299,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                        lineNumber: 298,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-xl shadow-sm border border-gray-200 p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold text-gray-900 mb-6\",\n                                children: \"Profile Information\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                lineNumber: 374,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-gray-900 border-b border-gray-200 pb-2\",\n                                                children: \"Personal Information\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 379,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-800 mb-2\",\n                                                        children: \"Full Name\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 384,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        name: \"name\",\n                                                        value: formData.name || \"\",\n                                                        onChange: handleInputChange,\n                                                        disabled: !editing,\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-gray-900 \".concat(!editing ? \"bg-gray-50\" : \"\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 385,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 383,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-800 mb-2\",\n                                                        children: \"Email Address\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 396,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"email\",\n                                                        name: \"email\",\n                                                        value: formData.email || \"\",\n                                                        onChange: handleInputChange,\n                                                        disabled: !editing,\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-gray-900 \".concat(!editing ? \"bg-gray-50\" : \"\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 397,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 395,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-800 mb-2\",\n                                                        children: \"Phone Number\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 408,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"tel\",\n                                                        name: \"phone\",\n                                                        value: formData.phone || \"\",\n                                                        onChange: handleInputChange,\n                                                        disabled: !editing,\n                                                        placeholder: \"Enter your phone number\",\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-gray-900 \".concat(!editing ? \"bg-gray-50\" : \"\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 409,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 407,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                        lineNumber: 378,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-gray-900 border-b border-gray-200 pb-2\",\n                                                children: \"Business Information\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 423,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-800 mb-2\",\n                                                        children: \"Business Name\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 428,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        name: \"businessName\",\n                                                        value: formData.businessName || \"\",\n                                                        onChange: handleInputChange,\n                                                        disabled: !editing,\n                                                        placeholder: \"Enter your business name\",\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-gray-900 \".concat(!editing ? \"bg-gray-50\" : \"\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 429,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 427,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-800 mb-2\",\n                                                        children: \"GST Number\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 441,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        name: \"gstNumber\",\n                                                        value: formData.gstNumber || \"\",\n                                                        onChange: handleInputChange,\n                                                        disabled: !editing,\n                                                        placeholder: \"Enter GST number (optional)\",\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-gray-900 \".concat(!editing ? \"bg-gray-50\" : \"\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 442,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 440,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-800 mb-2\",\n                                                        children: \"Business Logo\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 455,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    logoPreview ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-32 h-32 border-2 border-gray-200 rounded-lg overflow-hidden bg-gray-50 flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                    src: logoPreview,\n                                                                    alt: \"Business Logo\",\n                                                                    className: \"w-full h-full object-contain\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                    lineNumber: 459,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 458,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            editing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FileUpload__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                        onUpload: handleLogoUpload,\n                                                                        accept: \".jpg,.jpeg,.png,.gif,.svg,.webp\",\n                                                                        maxSize: 5,\n                                                                        maxFiles: 1,\n                                                                        className: \"flex-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                        lineNumber: 467,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: handleRemoveLogo,\n                                                                        disabled: logoUploading,\n                                                                        className: \"px-3 py-2 text-sm font-medium text-red-600 bg-red-50 border border-red-200 rounded-md hover:bg-red-100 transition-colors disabled:opacity-50\",\n                                                                        children: logoUploading ? \"Removing...\" : \"Remove\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                        lineNumber: 474,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 466,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 457,\n                                                        columnNumber: 19\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-32 h-32 border-2 border-dashed border-gray-300 rounded-lg bg-gray-50 flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-8 h-8 text-gray-400 mx-auto mb-2\",\n                                                                            fill: \"none\",\n                                                                            stroke: \"currentColor\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\",\n                                                                                strokeWidth: 2,\n                                                                                d: \"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                                lineNumber: 489,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                            lineNumber: 488,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-gray-500\",\n                                                                            children: \"No logo\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                            lineNumber: 491,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                    lineNumber: 487,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 486,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            editing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FileUpload__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                onUpload: handleLogoUpload,\n                                                                accept: \".jpg,.jpeg,.png,.gif,.svg,.webp\",\n                                                                maxSize: 5,\n                                                                maxFiles: 1,\n                                                                className: \"w-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 495,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 485,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    logoUploading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-2 flex items-center text-sm text-gray-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"animate-spin h-4 w-4 border-2 border-indigo-600 border-t-transparent rounded-full mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 507,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Uploading logo...\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 506,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 454,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                        lineNumber: 422,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                lineNumber: 376,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 border-b border-gray-200 pb-2 mb-4\",\n                                        children: \"Address Information\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                        lineNumber: 517,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"md:col-span-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-800 mb-2\",\n                                                        children: \"Street Address\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 523,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        name: \"street\",\n                                                        value: ((_formData_address = formData.address) === null || _formData_address === void 0 ? void 0 : _formData_address.street) || \"\",\n                                                        onChange: handleInputChange,\n                                                        disabled: !editing,\n                                                        placeholder: \"Enter your street address\",\n                                                        rows: 3,\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-gray-900 \".concat(!editing ? \"bg-gray-50\" : \"\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 524,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 522,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-800 mb-2\",\n                                                        children: \"City\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 536,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        name: \"city\",\n                                                        value: ((_formData_address1 = formData.address) === null || _formData_address1 === void 0 ? void 0 : _formData_address1.city) || \"\",\n                                                        onChange: handleInputChange,\n                                                        disabled: !editing,\n                                                        placeholder: \"Enter city\",\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-gray-900 \".concat(!editing ? \"bg-gray-50\" : \"\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 537,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 535,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-800 mb-2\",\n                                                        children: \"State\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 549,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        name: \"state\",\n                                                        value: ((_formData_address2 = formData.address) === null || _formData_address2 === void 0 ? void 0 : _formData_address2.state) || \"\",\n                                                        onChange: handleInputChange,\n                                                        disabled: !editing,\n                                                        placeholder: \"Enter state\",\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-gray-900 \".concat(!editing ? \"bg-gray-50\" : \"\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 550,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 548,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-800 mb-2\",\n                                                        children: \"PIN Code\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 562,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        name: \"pincode\",\n                                                        value: ((_formData_address3 = formData.address) === null || _formData_address3 === void 0 ? void 0 : _formData_address3.pincode) || \"\",\n                                                        onChange: handleInputChange,\n                                                        disabled: !editing,\n                                                        placeholder: \"Enter PIN code\",\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-gray-900 \".concat(!editing ? \"bg-gray-50\" : \"\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 563,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 561,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-800 mb-2\",\n                                                        children: \"Country\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 575,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        name: \"country\",\n                                                        value: ((_formData_address4 = formData.address) === null || _formData_address4 === void 0 ? void 0 : _formData_address4.country) || \"India\",\n                                                        onChange: handleInputChange,\n                                                        disabled: !editing,\n                                                        placeholder: \"Enter country\",\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-gray-900 \".concat(!editing ? \"bg-gray-50\" : \"\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 576,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 574,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                        lineNumber: 521,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                lineNumber: 516,\n                                columnNumber: 11\n                            }, this),\n                            editing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:hidden mt-6 flex space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleCancel,\n                                        className: \"flex-1 px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-800 bg-white hover:bg-gray-50 transition-colors\",\n                                        disabled: saving,\n                                        children: \"Cancel\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                        lineNumber: 592,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleSave,\n                                        className: \"flex-1 px-4 py-2 bg-indigo-600 text-white rounded-lg text-sm font-medium hover:bg-indigo-700 transition-colors disabled:opacity-50\",\n                                        disabled: saving,\n                                        children: saving ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full mr-2 inline-block\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                    lineNumber: 606,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Saving...\"\n                                            ]\n                                        }, void 0, true) : \"Save Changes\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                        lineNumber: 599,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                lineNumber: 591,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                        lineNumber: 373,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-xl shadow-sm border border-gray-200 p-6 mt-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900 mb-4\",\n                                children: \"Account Information\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                lineNumber: 617,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 sm:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center py-2 border-b border-gray-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-gray-800\",\n                                                children: \"Account Type\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 620,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-900 capitalize\",\n                                                children: user.role\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 621,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                        lineNumber: 619,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center py-2 border-b border-gray-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-gray-800\",\n                                                children: \"Member Since\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 624,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-900\",\n                                                children: user.createdAt ? new Date(user.createdAt).toLocaleDateString(\"en-IN\") : \"N/A\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 625,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                        lineNumber: 623,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center py-2 border-b border-gray-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-gray-800\",\n                                                children: \"Last Login\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 630,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-900\",\n                                                children: user.lastLogin ? new Date(user.lastLogin).toLocaleDateString(\"en-IN\") : \"N/A\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 631,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                        lineNumber: 629,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center py-2 border-b border-gray-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-gray-800\",\n                                                children: \"Email Status\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 636,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat(user.isEmailVerified ? \"bg-green-100 text-green-800\" : \"bg-yellow-100 text-yellow-800\"),\n                                                        children: user.isEmailVerified ? \"Verified\" : \"Not Verified\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 638,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    !user.isEmailVerified && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: sendVerificationEmail,\n                                                        disabled: verificationLoading,\n                                                        className: \"text-xs text-indigo-600 hover:text-indigo-800 font-medium disabled:opacity-50\",\n                                                        children: verificationLoading ? \"Sending...\" : \"Send Verification\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 642,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                                lineNumber: 637,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                        lineNumber: 635,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                                lineNumber: 618,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                        lineNumber: 616,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n                lineNumber: 285,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\page.tsx\",\n        lineNumber: 254,\n        columnNumber: 5\n    }, this);\n}\n_s(ProfilePage, \"mLWr/FK64trBTG7PQjmr6/dSb6o=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = ProfilePage;\nvar _c;\n$RefreshReg$(_c, \"ProfilePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/profile/page.tsx\n"));

/***/ })

});