export interface AdminUser {
    _id: string;
    id: string;
    name: string;
    email: string;
    role: 'admin';
    businessName: string;
    isEmailVerified: boolean;
    lastLogin: Date;
    createdAt: Date;
}
/**
 * Check if the provided credentials match the admin credentials from environment
 */
export declare const validateAdminCredentials: (email: string, password: string) => Promise<boolean>;
/**
 * Create admin user object for JWT token and response
 */
export declare const createAdminUserObject: () => AdminUser;
/**
 * Check if a user ID belongs to the admin user
 */
export declare const isAdminUserId: (userId: string) => boolean;
//# sourceMappingURL=adminAuth.d.ts.map